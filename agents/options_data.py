from datetime import datetime

import requests
from smolagents import tool

from wheel_trader import config


# Simple biotech stock detection
def is_biotech_stock(symbol: str) -> bool:
    """
    Check if a stock symbol is a biotech company.

    Args:
        symbol: Stock symbol (e.g., MRNA, GILD)

    Returns:
        True if biotech, False otherwise
    """
    # Common biotech stock symbols (you can expand this list)
    biotech_symbols = {
        'MRNA', 'BNTX', 'NVAX', 'GILD', 'BIIB', 'AMGN', 'REGN', 'VRTX',
        'ILMN', 'BMRN', 'ALNY', 'SRPT', 'TECH', 'BLUE', 'CRSP', 'EDIT',
        'NTLA', 'BEAM', 'ARKG', 'XBI'  # XBI is biotech ETF
    }

    return symbol.upper() in biotech_symbols


@tool
def options_data_tool(symbol: str, expiry_date: str) -> dict:
    """
    Fetch options data for a given symbol and expiration date.

    Args:
        symbol: Stock symbol (e.g., AAPL)
        expiry_date: Expiration date in YYYY-MM-DD format
    """

    # Get API key from environment
    api_key = config.POLYGON_API_KEY
    if not api_key:
        return {
            "symbol": symbol,
            "expiry_date": expiry_date,
            "error": "POLYGON_API_KEY not set in environment variables",
            "status": "error"
        }

    try:
        # Fetch options chain from Polygon
        url = "https://api.polygon.io/v3/reference/options/contracts"
        params = {
            "underlying_ticker": symbol,
            "expiration_date": expiry_date,
            "apikey": api_key,
            "limit": 100
        }

        response = requests.get(url, params=params, timeout=30)
        response.raise_for_status()

        data = response.json()

        return {
            "symbol": symbol,
            "expiry_date": expiry_date,
            "contracts_count": len(data.get("results", [])),
            "contracts": data.get("results", [])[:10],
            "status": "success"
        }

    except Exception as e:
        return {
            "symbol": symbol,
            "expiry_date": expiry_date,
            "error": str(e),
            "status": "error"
        }


@tool
def market_analysis_tool(symbol: str, analysis_type: str) -> dict:
    """
    Perform technical analysis on market data.

    Args:
        symbol: Stock symbol to analyze
        analysis_type: Type of analysis (rsi, sma, bollinger)
    """

    try:
        # Mock analysis results (replace with real analysis)
        if analysis_type == "rsi":
            result = {
                "symbol": symbol,
                "analysis_type": "rsi",
                "rsi_value": 65.5,
                "signal": "neutral",
                "timestamp": datetime.now().isoformat()
            }
        elif analysis_type == "sma":
            result = {
                "symbol": symbol,
                "analysis_type": "sma",
                "sma_20": 150.25,
                "sma_50": 148.75,
                "signal": "bullish",
                "timestamp": datetime.now().isoformat()
            }
        elif analysis_type == "bollinger":
            result = {
                "symbol": symbol,
                "analysis_type": "bollinger",
                "upper_band": 155.0,
                "middle_band": 150.0,
                "lower_band": 145.0,
                "signal": "neutral",
                "timestamp": datetime.now().isoformat()
            }
        else:
            result = {
                "symbol": symbol,
                "analysis_type": analysis_type,
                "error": f"Unsupported analysis type: {analysis_type}",
                "status": "error"
            }

        return result

    except Exception as e:
        return {
            "symbol": symbol,
            "analysis_type": analysis_type,
            "error": str(e),
            "status": "error"
        }


@tool
def risk_assessment_tool(symbol: str, position_size: float, portfolio_value: float) -> dict:
    """
    Assess risk for a potential trade.

    Args:
        symbol: Stock symbol for the trade
        position_size: Size of the position in dollars
        portfolio_value: Total portfolio value in dollars
    """
    try:
        # Calculate position as percentage of portfolio
        position_pct = (position_size / portfolio_value) * 100

        # Simple risk assessment
        if position_pct > 10:
            risk_level = "high"
            recommendation = "reduce_position"
        elif position_pct > 5:
            risk_level = "medium"
            recommendation = "proceed_with_caution"
        else:
            risk_level = "low"
            recommendation = "acceptable"

        return {
            "symbol": symbol,
            "position_size": position_size,
            "portfolio_value": portfolio_value,
            "position_percentage": round(position_pct, 2),
            "risk_level": risk_level,
            "recommendation": recommendation,
            "max_recommended_size": portfolio_value * 0.05,  # 5% max
            "status": "success"
        }

    except Exception as e:
        return {
            "symbol": symbol,
            "error": str(e),
            "status": "error"
        }


@tool
def filtered_market_analysis_tool(symbol: str, analysis_type: str, exclude_biotech: bool = True) -> dict:
    """
    Perform technical analysis on market data with optional biotech filtering.

    Args:
        symbol: Stock symbol to analyze
        analysis_type: Type of analysis (rsi, sma, bollinger)
        exclude_biotech: If True, skip biotech stocks (default: True)
    """

    # Check if we should skip this symbol
    if exclude_biotech and is_biotech_stock(symbol):
        return {
            "symbol": symbol,
            "analysis_type": analysis_type,
            "error": f"Biotech stock {symbol} excluded from analysis",
            "status": "filtered_out",
            "filter_reason": "biotech_exclusion"
        }

    # If not filtered, use the regular market analysis tool
    return market_analysis_tool(symbol, analysis_type)
