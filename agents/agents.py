"""
Agent Creation and Management

This module provides functions to create different types of trading agents
with various configurations and filtering strategies.
"""

from typing import Dict

from wheel_trader.smolagents_e2b import HealthAwareAgent

from .tools import (
    aggressive_market_analysis_tool,
    conservative_market_analysis_tool,
    market_analysis_tool,
    options_data_tool,
    risk_assessment_tool,
)


def create_trading_agent(name: str = "trading_agent", model=None) -> HealthAwareAgent:
    """
    Create a health-aware trading agent with E2B execution and domain-specific tools.

    Args:
        name: Name for the agent
        model: Model to use (defaults to InferenceClientModel)

    Returns:
        HealthAwareAgent configured for trading tasks
    """
    tools = [
        options_data_tool,
        market_analysis_tool,
        risk_assessment_tool
    ]

    return HealthAwareAgent(
        name=name,
        model=model,
        tools=tools
    )


def create_multi_agent_system(model=None) -> Dict[str, HealthAwareAgent]:
    """
    Create a multi-agent system with specialized agents.

    Returns:
        Dictionary of specialized agents
    """
    return {
        "data_agent": HealthAwareAgent(
            name="data_agent",
            model=model,
            tools=[options_data_tool]
        ),
        "analysis_agent": HealthAwareAgent(
            name="analysis_agent",
            model=model,
            tools=[market_analysis_tool]
        ),
        "risk_agent": HealthAwareAgent(
            name="risk_agent",
            model=model,
            tools=[risk_assessment_tool]
        ),
        "coordinator_agent": HealthAwareAgent(
            name="coordinator_agent",
            model=model,
            tools=[options_data_tool, market_analysis_tool, risk_assessment_tool]
        )
    }


def create_conservative_agent(name: str = "conservative_agent", model=None) -> HealthAwareAgent:
    """
    Create a conservative trading agent that excludes biotech stocks.
    
    Args:
        name: Name for the agent
        
    Returns:
        HealthAwareAgent configured with biotech exclusion
    """
    tools = [
        conservative_market_analysis_tool,  # Uses biotech filtering
        options_data_tool,                  # Regular options data
        risk_assessment_tool               # Regular risk assessment
    ]
    
    agent = HealthAwareAgent(
        name=name,
        tools=tools,
        health_threshold=0.7,
        memory_backend="pgvector",
        enable_observability=True
    )
    
    return agent


def create_aggressive_agent(name: str = "aggressive_agent") -> HealthAwareAgent:
    """
    Create an aggressive trading agent that includes all stocks (no biotech exclusion).
    
    Args:
        name: Name for the agent
        
    Returns:
        HealthAwareAgent configured without biotech exclusion
    """
    tools = [
        aggressive_market_analysis_tool,   # No biotech filtering
        options_data_tool,                 # Regular options data  
        risk_assessment_tool              # Regular risk assessment
    ]
    
    agent = HealthAwareAgent(
        name=name,
        tools=tools,
        health_threshold=0.7,
        memory_backend="pgvector", 
        enable_observability=True
    )
    
    return agent


def create_comparative_agent_pair() -> dict:
    """
    Create both conservative and aggressive agents for comparison.
    
    Returns:
        Dictionary with both agents: {"conservative": agent_a, "aggressive": agent_b}
    """
    return {
        "conservative": create_conservative_agent("conservative_agent"),
        "aggressive": create_aggressive_agent("aggressive_agent")
    }


# Simple test function to verify agents work
def test_agent_filtering():
    """
    Simple test to verify the agents have different filtering behavior.
    """
    print("🧪 Testing Comparative Agents...")
    
    # Create both agents
    agents = create_comparative_agent_pair()
    conservative = agents["conservative"]
    aggressive = agents["aggressive"]
    
    print(f"✅ Created conservative agent: {conservative.name}")
    print(f"✅ Created aggressive agent: {aggressive.name}")
    
    # Test that they have different tools
    conservative_tools = [tool.__name__ for tool in conservative.agent.tools]
    aggressive_tools = [tool.__name__ for tool in aggressive.agent.tools]
    
    print(f"Conservative tools: {conservative_tools}")
    print(f"Aggressive tools: {aggressive_tools}")
    
    print("✅ Comparative agents created successfully!")
    
    return agents


if __name__ == "__main__":
    # Run test if file is executed directly
    test_agent_filtering()
