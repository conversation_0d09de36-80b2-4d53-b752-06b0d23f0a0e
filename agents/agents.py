"""
Comparative Trading Agents

This module creates two different trading agents with different stock filtering strategies:
- Conservative Agent: Excludes biotech stocks
- Aggressive Agent: Includes all stocks

Both agents use the same trading tools but with different filtering configurations.
"""

from smolagents import tool
from wheel_trader.smolagents_e2b import HealthAwareAgent
from .options_data import (
    filtered_market_analysis_tool,
    options_data_tool,
    risk_assessment_tool,
    is_biotech_stock
)


@tool
def conservative_market_analysis_tool(symbol: str, analysis_type: str) -> dict:
    """
    Conservative market analysis that excludes biotech stocks.
    
    Args:
        symbol: Stock symbol to analyze
        analysis_type: Type of analysis (rsi, sma, bollinger)
    """
    return filtered_market_analysis_tool(symbol, analysis_type, exclude_biotech=True)


@tool  
def aggressive_market_analysis_tool(symbol: str, analysis_type: str) -> dict:
    """
    Aggressive market analysis that includes all stocks (no biotech exclusion).
    
    Args:
        symbol: Stock symbol to analyze  
        analysis_type: Type of analysis (rsi, sma, bollinger)
    """
    return filtered_market_analysis_tool(symbol, analysis_type, exclude_biotech=False)


def create_conservative_agent(name: str = "conservative_agent") -> HealthAwareAgent:
    """
    Create a conservative trading agent that excludes biotech stocks.
    
    Args:
        name: Name for the agent
        
    Returns:
        HealthAwareAgent configured with biotech exclusion
    """
    tools = [
        conservative_market_analysis_tool,  # Uses biotech filtering
        options_data_tool,                  # Regular options data
        risk_assessment_tool               # Regular risk assessment
    ]
    
    agent = HealthAwareAgent(
        name=name,
        tools=tools,
        health_threshold=0.7,
        memory_backend="pgvector",
        enable_observability=True
    )
    
    return agent


def create_aggressive_agent(name: str = "aggressive_agent") -> HealthAwareAgent:
    """
    Create an aggressive trading agent that includes all stocks (no biotech exclusion).
    
    Args:
        name: Name for the agent
        
    Returns:
        HealthAwareAgent configured without biotech exclusion
    """
    tools = [
        aggressive_market_analysis_tool,   # No biotech filtering
        options_data_tool,                 # Regular options data  
        risk_assessment_tool              # Regular risk assessment
    ]
    
    agent = HealthAwareAgent(
        name=name,
        tools=tools,
        health_threshold=0.7,
        memory_backend="pgvector", 
        enable_observability=True
    )
    
    return agent


def create_comparative_agent_pair() -> dict:
    """
    Create both conservative and aggressive agents for comparison.
    
    Returns:
        Dictionary with both agents: {"conservative": agent_a, "aggressive": agent_b}
    """
    return {
        "conservative": create_conservative_agent("conservative_agent"),
        "aggressive": create_aggressive_agent("aggressive_agent")
    }


# Simple test function to verify agents work
def test_agent_filtering():
    """
    Simple test to verify the agents have different filtering behavior.
    """
    print("🧪 Testing Comparative Agents...")
    
    # Create both agents
    agents = create_comparative_agent_pair()
    conservative = agents["conservative"]
    aggressive = agents["aggressive"]
    
    print(f"✅ Created conservative agent: {conservative.name}")
    print(f"✅ Created aggressive agent: {aggressive.name}")
    
    # Test that they have different tools
    conservative_tools = [tool.__name__ for tool in conservative.agent.tools]
    aggressive_tools = [tool.__name__ for tool in aggressive.agent.tools]
    
    print(f"Conservative tools: {conservative_tools}")
    print(f"Aggressive tools: {aggressive_tools}")
    
    print("✅ Comparative agents created successfully!")
    
    return agents


if __name__ == "__main__":
    # Run test if file is executed directly
    test_agent_filtering()
