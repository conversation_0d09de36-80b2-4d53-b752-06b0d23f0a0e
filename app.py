from flask import Flask, render_template
from wheel_trader.coordinator import coordinator_app

app = Flask(__name__)

@app.route('/')
def index():
    """Serve the main documentation page"""
    return render_template('index.html')

# Register the coordinator blueprint
app.register_blueprint(coordinator_app)

# Error handlers
@app.errorhandler(404)
def not_found_error(error):
    return render_template('index.html'), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('index.html'), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8000, debug=True)
