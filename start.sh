#!/bin/bash
# CoveredCalls-Agents Quick Start Script with Phoenix Integration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 CoveredCalls-Agents Startup with Phoenix Integration${NC}"
echo "================================================================"

# Check if virtual environment is activated
if [[ "$VIRTUAL_ENV" == "" ]]; then
    echo -e "${YELLOW}⚠️  Virtual environment not detected${NC}"
    echo -e "${YELLOW}   Recommendation: Activate your virtual environment first${NC}"
    echo -e "${YELLOW}   Run: source venv/bin/activate${NC}"
    echo ""
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo -e "${YELLOW}⚠️  .env file not found${NC}"
    if [ -f ".env.example" ]; then
        echo -e "${BLUE}📋 Copying .env.example to .env${NC}"
        cp .env.example .env
        echo -e "${RED}❌ Please edit .env file with your actual API keys before continuing${NC}"
        exit 1
    else
        echo -e "${RED}❌ No .env.example file found${NC}"
        exit 1
    fi
fi

# Check if Phoenix is running
echo -e "${BLUE}🔍 Checking Phoenix status...${NC}"
if docker ps --filter "name=phoenix" --format "table {{.Names}}" | grep -q "phoenix"; then
    echo -e "${GREEN}✅ Phoenix is running${NC}"
else
    echo -e "${YELLOW}⚠️  Phoenix not running. Starting Phoenix...${NC}"
    
    # Start Phoenix using docker-compose
    if [ -f "docker-compose.phoenix.yml" ]; then
        docker-compose -f docker-compose.phoenix.yml up -d
        echo -e "${GREEN}✅ Phoenix started${NC}"
        echo -e "${BLUE}⏳ Waiting for Phoenix to be ready...${NC}"
        sleep 10
    else
        echo -e "${RED}❌ docker-compose.phoenix.yml not found${NC}"
        exit 1
    fi
fi

# Install dependencies if needed
echo -e "${BLUE}📦 Checking dependencies...${NC}"
if ! python -c "import phoenix.otel" 2>/dev/null; then
    echo -e "${YELLOW}⚠️  Phoenix dependencies not found. Installing...${NC}"
    pip install -r requirements.txt
fi

# Run Phoenix integration test (optional)
echo -e "${BLUE}🧪 Testing Phoenix integration...${NC}"
if python test_phoenix_integration.py; then
    echo -e "${GREEN}✅ Phoenix integration test passed${NC}"
else
    echo -e "${YELLOW}⚠️  Phoenix integration test had issues (continuing anyway)${NC}"
fi

# Start the application
echo -e "${BLUE}🚀 Starting CoveredCalls-Agents application...${NC}"
echo ""
echo -e "${GREEN}📊 Phoenix UI:      http://localhost:6006${NC}"
echo -e "${GREEN}🖥️  Application:    http://localhost:8000${NC}"
echo ""
echo -e "${BLUE}Press Ctrl+C to stop the application${NC}"
echo ""

# Set Phoenix environment variables
export PHOENIX_COLLECTOR_ENDPOINT=${PHOENIX_COLLECTOR_ENDPOINT:-"http://localhost:6006/v1/traces"}
export PHOENIX_PROJECT_NAME=${PHOENIX_PROJECT_NAME:-"coveredcalls-agents"}
export PHOENIX_ENABLE_TRACING=${PHOENIX_ENABLE_TRACING:-"true"}

# Start the application
python app.py
