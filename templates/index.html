<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Covered Calls Agent Platform</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; line-height: 1.6; color: #333; max-width: 800px; margin: 2rem auto; padding: 0 1rem; background-color: #f9f9f9; }
        h1, h2, h3 { color: #222; }
        h1 { text-align: center; border-bottom: 2px solid #eee; padding-bottom: 0.5rem; }
        .container { background-color: #fff; padding: 2rem; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.05); }
        .badge { background-color: #eef; color: #44d; padding: 0.2rem 0.6rem; border-radius: 4px; font-size: 0.85em; font-weight: 600; }
        code { background-color: #eee; padding: 0.2em 0.4em; margin: 0; border-radius: 3px; font-family: "SFMono-Regular", <PERSON><PERSON><PERSON>, "Liberation Mono", Menlo, monospace; }
        pre { background: #2d2d2d; color: #f8f8f2; padding: 1em; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📈 Covered Calls Agent Platform</h1>
        <p>Welcome to the autonomous agent platform for managing wheel trading strategies. This system uses a suite of intelligent agents to analyze market data, execute trades, and continuously learn from its decisions.</p>

        <h2>Core Capabilities</h2>
        <p>The platform is built on three foundational pillars that ensure robust, secure, and intelligent operation:</p>
        <ul>
            <li><span class="badge">Memory & Observability</span> Agents have long-term memory, powered by pgvector, allowing them to learn from past actions. All operations are traced in real-time using Arize Phoenix for full system visibility.</li>
            <li><span class="badge">Secure Execution</span> Every agent and tool runs in a hardened, isolated sandbox via e2b containers. This prevents runaway code and secures the platform's infrastructure.</li>
            <li><span class="badge">Continuous Evaluation</span> Agent performance is constantly measured against a set of objective metrics. A health score is calculated nightly, and underperforming agents are automatically sidelined to ensure only the most effective models are active.</li>
        </ul>

        <h2>How to Use It</h2>
        <p>The platform is primarily API-driven. You can interact with the system by sending requests to the main coordinator endpoint.</p>
        <h3>Example: Triggering the Coordinator Agent</h3>
        <p>To initiate a task, you would typically call the main application endpoint:</p>
        <pre><code>
curl -X POST http://localhost:8000/execute_task \\
     -H "Content-Type: application/json" \\
     -d '{
           "task": "Analyze AAPL for a covered call opportunity for next month."
         }'
        </code></pre>
        <p>The <code>agent_coordinator</code> will receive this task, select the appropriate agent, and manage the execution within the secure, monitored environment.</p>

        <h2>Testing & Monitoring</h2>
        <p>Explore the platform capabilities:</p>
        <ul>
            <li><a href="/comparative-testing"><strong>🔬 Comparative Agent Testing</strong></a> - Compare Conservative vs Aggressive trading strategies</li>
            <li><a href="http://localhost:6006" target="_blank"><strong>📊 Phoenix Observability Dashboard</strong></a> - Monitor real-time agent performance</li>
        </ul>
    </div>
</body>
</html>
