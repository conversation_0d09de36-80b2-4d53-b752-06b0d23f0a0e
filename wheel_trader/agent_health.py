"""
Agent Health Management System

Monitors agent performance and health scores based on execution metrics.
Implements health gating to prevent unhealthy agents from executing.
"""
from typing import Dict, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass

from supabase import create_client, Client
from wheel_trader import config


@dataclass
class HealthMetrics:
    """Container for agent health metrics"""
    total_runs: int
    successful_runs: int
    avg_duration_ms: int
    avg_cpu_ms: int
    avg_mem_mb: int
    success_rate: float
    health_score: float
    last_updated: datetime


class AgentHealthManager:
    """
    Manages agent health scores and execution gating.

    Health Score Calculation:
    - Base score: success_rate (0.0 to 1.0)
    - Penalty for high duration: -0.1 if avg_duration > 10s
    - Penalty for high CPU usage: -0.1 if avg_cpu > 5s
    - Penalty for high memory: -0.1 if avg_mem > 100MB
    - Final score clamped to [0.0, 1.0]
    """

    def __init__(self, health_threshold: float = 0.7):
        self.health_threshold = health_threshold
        self.supabase = None
        self._init_supabase()

    def _init_supabase(self):
        """Initialize Supabase client"""
        try:
            if config.SUPABASE_URL and config.SUPABASE_KEY:
                self.supabase: Client = create_client(config.SUPABASE_URL, config.SUPABASE_KEY)
        except Exception as e:
            print(f"Warning: Could not initialize Supabase for health management: {e}")

    def get_health_metrics(self, agent_name: str, hours_back: int = 24) -> Optional[HealthMetrics]:
        """
        Get health metrics for an agent based on recent execution history.

        Args:
            agent_name: Name of the agent
            hours_back: How many hours back to look for metrics

        Returns:
            HealthMetrics object or None if no data available
        """
        if not self.supabase:
            return None

        try:
            # Calculate time threshold
            time_threshold = datetime.now() - timedelta(hours=hours_back)

            # Query execution audit log
            response = self.supabase.table("exec_audit_log").select("*").eq(
                "agent_name", agent_name
            ).gte("created_at", time_threshold.isoformat()).execute()

            if not response.data:
                return None

            # Calculate metrics
            executions = response.data
            total_runs = len(executions)
            successful_runs = sum(1 for ex in executions if ex['success'])

            if total_runs == 0:
                return None

            # Calculate averages
            avg_duration_ms = sum(ex['duration_ms'] for ex in executions) // total_runs
            avg_cpu_ms = sum(ex['cpu_ms'] or 0 for ex in executions) // total_runs
            avg_mem_mb = sum(ex['mem_peak_mb'] or 0 for ex in executions) // total_runs

            success_rate = successful_runs / total_runs

            # Calculate health score
            health_score = self._calculate_health_score(
                success_rate, avg_duration_ms, avg_cpu_ms, avg_mem_mb
            )

            return HealthMetrics(
                total_runs=total_runs,
                successful_runs=successful_runs,
                avg_duration_ms=avg_duration_ms,
                avg_cpu_ms=avg_cpu_ms,
                avg_mem_mb=avg_mem_mb,
                success_rate=success_rate,
                health_score=health_score,
                last_updated=datetime.now()
            )

        except Exception as e:
            print(f"Error getting health metrics for {agent_name}: {e}")
            return None

    def _calculate_health_score(
        self, success_rate: float, avg_duration_ms: int,
        avg_cpu_ms: int, avg_mem_mb: int
    ) -> float:
        """
        Calculate health score based on performance metrics.

        Args:
            success_rate: Ratio of successful executions (0.0 to 1.0)
            avg_duration_ms: Average execution duration in milliseconds
            avg_cpu_ms: Average CPU time in milliseconds
            avg_mem_mb: Average memory usage in MB

        Returns:
            Health score between 0.0 and 1.0
        """
        score = success_rate

        # Apply penalties for poor performance
        if avg_duration_ms > 10000:  # > 10 seconds
            score -= 0.1

        if avg_cpu_ms > 5000:  # > 5 seconds CPU time
            score -= 0.1

        if avg_mem_mb > 100:  # > 100 MB memory
            score -= 0.1

        # Clamp to valid range
        return max(0.0, min(1.0, score))

    def update_agent_health(self, agent_name: str) -> bool:
        """
        Update the agent health score in the database.

        Args:
            agent_name: Name of the agent to update

        Returns:
            True if update was successful, False otherwise
        """
        if not self.supabase:
            return False

        try:
            # Get current metrics
            metrics = self.get_health_metrics(agent_name)
            if not metrics:
                return False

            # Upsert to agent_health table
            health_data = {
                "agent_name": agent_name,
                "version": "1.0",  # Could be made configurable
                "health_score": metrics.health_score,
                "total_runs": metrics.total_runs,
                "successful_runs": metrics.successful_runs,
                "avg_duration_ms": metrics.avg_duration_ms,
                "last_updated": metrics.last_updated.isoformat()
            }

            self.supabase.table("agent_health").upsert(health_data).execute()
            return True

        except Exception as e:
            print(f"Error updating health for {agent_name}: {e}")
            return False

    def is_agent_healthy(self, agent_name: str) -> bool:
        """
        Check if an agent is healthy enough to execute.

        Args:
            agent_name: Name of the agent to check

        Returns:
            True if agent is healthy (score >= threshold), False otherwise
        """
        if not self.supabase:
            # If no database connection, allow execution (fail open)
            return True

        try:
            # First try to get from agent_health table
            response = self.supabase.table("agent_health").select("health_score").eq(
                "agent_name", agent_name
            ).execute()

            if response.data:
                health_score = response.data[0]['health_score']
                return float(health_score) >= self.health_threshold

            # If not in health table, calculate on the fly
            metrics = self.get_health_metrics(agent_name)
            if metrics:
                self.update_agent_health(agent_name)
                return metrics.health_score >= self.health_threshold

            # If no metrics available, allow execution (new agent)
            return True

        except Exception as e:
            print(f"Error checking health for {agent_name}: {e}")
            # Fail open - allow execution if health check fails
            return True

    def get_all_agent_health(self) -> Dict[str, HealthMetrics]:
        """
        Get health metrics for all agents.

        Returns:
            Dictionary mapping agent names to their health metrics
        """
        if not self.supabase:
            return {}

        try:
            # Get all agents from health table
            response = self.supabase.table("agent_health").select("*").execute()

            result = {}
            for row in response.data:
                # TODO: get cpu and mem from e2b
                metrics = HealthMetrics(
                    total_runs=row['total_runs'],
                    successful_runs=row['successful_runs'],
                    avg_duration_ms=row['avg_duration_ms'],
                    avg_cpu_ms=0,  # Not stored in health table
                    avg_mem_mb=0,  # Not stored in health table
                    success_rate=row['successful_runs'] / max(row['total_runs'], 1),
                    health_score=float(row['health_score']),
                    last_updated=datetime.fromisoformat(row['last_updated'])
                )
                result[row['agent_name']] = metrics

            return result

        except Exception as e:
            print(f"Error getting all agent health: {e}")
            return {}

    def refresh_all_health_scores(self) -> int:
        """
        Refresh health scores for all agents that have recent activity.

        Returns:
            Number of agents updated
        """
        if not self.supabase:
            return 0

        try:
            # Get all unique agent names from recent executions
            time_threshold = datetime.now() - timedelta(hours=24)
            response = self.supabase.table("exec_audit_log").select("agent_name").gte(
                "created_at", time_threshold.isoformat()
            ).execute()

            if not response.data:
                return 0

            # Get unique agent names
            agent_names = set(row['agent_name'] for row in response.data)

            # Update health for each agent
            updated_count = 0
            for agent_name in agent_names:
                if self.update_agent_health(agent_name):
                    updated_count += 1

            return updated_count

        except Exception as e:
            print(f"Error refreshing health scores: {e}")
            return 0

    def get_unhealthy_agents(self) -> Dict[str, float]:
        """
        Get all agents with health scores below the threshold.

        Returns:
            Dictionary mapping agent names to their health scores
        """
        all_health = self.get_all_agent_health()
        return {
            name: metrics.health_score
            for name, metrics in all_health.items()
            if metrics.health_score < self.health_threshold
        }
