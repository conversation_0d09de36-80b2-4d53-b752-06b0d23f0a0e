"""
Observability Module for Options Trading Agents

Comprehensive observability system with Phoenix tracing, memory integration,
and financial market monitoring capabilities.
"""

from .memory_bridge import MemoryObservabilityBridge, auto_store_trace_memory
from .tracer import AgentTracer, trace_execution, trace_memory_operation

# Import ObservabilityManager from parent module with fallback
try:
    from ..observability import ObservabilityManager
except ImportError:
    # Create a fallback ObservabilityManager
    class ObservabilityManager:
        def __init__(self, agent_name: str, memory_manager=None):
            self.agent_name = agent_name
            self.memory_manager = memory_manager

        def start_session(self, session_name=None):
            return True

        def trace_agent_operation(self, operation_name: str, metadata=None):
            return None

        def trace_with_memory(self, operation_name: str, store_result: bool = True):
            class FallbackContext:
                def __enter__(self):
                    return self
                def __exit__(self, exc_type, exc_val, exc_tb):
                    return False
            return FallbackContext()

        def get_observability_stats(self):
            return {"agent_name": self.agent_name, "fallback_mode": True}

__all__ = [
    "AgentTracer",
    "MemoryObservabilityBridge",
    "ObservabilityManager",
    "trace_execution",
    "trace_memory_operation",
    "auto_store_trace_memory"
]
