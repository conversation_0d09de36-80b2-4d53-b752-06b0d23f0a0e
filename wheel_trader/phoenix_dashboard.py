"""
Phoenix Dashboard Manager for Options Trading Agents

This module provides custom dashboard configuration and management
for Phoenix observability, integrating with the memory system and
enhanced evaluation metrics.
"""

import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import requests

from supabase import create_client, Client
from wheel_trader import config

# Configure logging
logger = logging.getLogger(__name__)


class PhoenixDashboardManager:
    """
    Manages custom Phoenix dashboard configuration and data integration.
    
    Provides functionality to:
    - Configure custom Phoenix dashboards
    - Link Phoenix traces with Supabase evaluation data
    - Create real-time monitoring views
    - Manage dashboard templates and layouts
    """
    
    def __init__(self, phoenix_url: str = "http://localhost:6006",
                 supabase_client: Optional[Client] = None):
        """
        Initialize the Phoenix dashboard manager.
        
        Args:
            phoenix_url: Phoenix server URL
            supabase_client: Supabase client for data integration
        """
        self.phoenix_url = phoenix_url.rstrip('/')
        self.supabase = supabase_client or create_client(config.SUPABASE_URL, config.SUPABASE_KEY)
        
        # Dashboard configuration
        self.dashboard_config = {
            "refresh_interval": 30,  # seconds
            "default_time_range": "1h",
            "max_traces_per_view": 1000,
            "enable_real_time": True
        }
        
        # Custom panels configuration
        self.custom_panels = self._get_custom_panels_config()
    
    def setup_custom_dashboard(self) -> Dict[str, Any]:
        """
        Set up custom Phoenix dashboard with trading-specific panels.
        
        Returns:
            Dashboard configuration status
        """
        try:
            logger.info("Setting up custom Phoenix dashboard...")
            
            # Create custom dashboard layout
            dashboard_layout = self._create_dashboard_layout()
            
            # Configure data sources
            data_sources = self._configure_data_sources()
            
            # Set up real-time updates
            real_time_config = self._setup_real_time_updates()
            
            # Apply dashboard configuration
            config_result = self._apply_dashboard_config(dashboard_layout, data_sources)
            
            logger.info("Custom Phoenix dashboard setup completed")
            
            return {
                "status": "success",
                "dashboard_url": f"{self.phoenix_url}/dashboard/custom",
                "panels_configured": len(self.custom_panels),
                "real_time_enabled": real_time_config["enabled"],
                "data_sources": len(data_sources)
            }
            
        except Exception as e:
            logger.error(f"Failed to setup custom dashboard: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _create_dashboard_layout(self) -> Dict[str, Any]:
        """Create custom dashboard layout for trading agents."""
        return {
            "title": "Options Trading Agents - Observability Dashboard",
            "description": "Comprehensive monitoring for trading agents with memory and evaluation integration",
            "layout": {
                "rows": [
                    {
                        "title": "Agent Performance Overview",
                        "panels": [
                            "agent_health_summary",
                            "evaluation_scores_trend",
                            "memory_utilization",
                            "trace_performance"
                        ]
                    },
                    {
                        "title": "Memory & Evaluation Analytics",
                        "panels": [
                            "memory_context_usage",
                            "hallucination_detection",
                            "evaluation_distribution",
                            "context_relevance"
                        ]
                    },
                    {
                        "title": "Trading Operations",
                        "panels": [
                            "trading_operations_table",
                            "symbol_performance",
                            "strategy_effectiveness",
                            "error_analysis"
                        ]
                    },
                    {
                        "title": "System Health",
                        "panels": [
                            "system_metrics",
                            "database_performance",
                            "trace_ingestion_rate",
                            "alert_summary"
                        ]
                    }
                ]
            },
            "time_range": {
                "default": "1h",
                "options": ["15m", "1h", "3h", "6h", "12h", "24h", "7d"]
            },
            "refresh": {
                "interval": 30,
                "auto_refresh": True
            }
        }
    
    def _get_custom_panels_config(self) -> Dict[str, Dict[str, Any]]:
        """Get configuration for custom dashboard panels."""
        return {
            "agent_health_summary": {
                "type": "stat",
                "title": "Agent Health Scores",
                "query": """
                    SELECT 
                        agent_name,
                        health_score,
                        last_updated
                    FROM agent_health 
                    ORDER BY health_score DESC
                """,
                "visualization": {
                    "type": "gauge",
                    "thresholds": [
                        {"value": 0.0, "color": "red"},
                        {"value": 0.7, "color": "yellow"},
                        {"value": 0.8, "color": "green"}
                    ]
                }
            },
            
            "evaluation_scores_trend": {
                "type": "timeseries",
                "title": "Evaluation Scores Over Time",
                "query": """
                    SELECT 
                        created_at as timestamp,
                        agent_name,
                        evaluation_score,
                        correctness_score,
                        confidence_score
                    FROM agent_metrics 
                    WHERE created_at >= NOW() - INTERVAL '{time_range}'
                    ORDER BY created_at
                """,
                "visualization": {
                    "type": "line",
                    "y_axis": {"min": 0, "max": 1},
                    "legend": {"show": True}
                }
            },
            
            "memory_utilization": {
                "type": "gauge",
                "title": "Memory Context Utilization",
                "query": """
                    SELECT 
                        COUNT(*) FILTER (WHERE memory_context_used = true)::float / COUNT(*) as utilization_rate
                    FROM agent_metrics 
                    WHERE created_at >= NOW() - INTERVAL '{time_range}'
                """,
                "visualization": {
                    "type": "gauge",
                    "unit": "percent",
                    "thresholds": [
                        {"value": 0.0, "color": "red"},
                        {"value": 0.5, "color": "yellow"},
                        {"value": 0.8, "color": "green"}
                    ]
                }
            },
            
            "hallucination_detection": {
                "type": "stat",
                "title": "Hallucination Detection",
                "query": """
                    SELECT 
                        AVG(hallucination_score) as avg_hallucination,
                        COUNT(*) FILTER (WHERE hallucination_score > 0.3) as high_hallucination_count
                    FROM agent_metrics 
                    WHERE created_at >= NOW() - INTERVAL '{time_range}'
                """,
                "visualization": {
                    "type": "stat",
                    "color_mode": "background",
                    "thresholds": [
                        {"value": 0.0, "color": "green"},
                        {"value": 0.3, "color": "yellow"},
                        {"value": 0.5, "color": "red"}
                    ]
                }
            },
            
            "trading_operations_table": {
                "type": "table",
                "title": "Recent Trading Operations",
                "query": """
                    SELECT 
                        agent_name,
                        task_description,
                        evaluation_score,
                        execution_time_ms,
                        memory_context_count,
                        created_at
                    FROM agent_metrics 
                    WHERE created_at >= NOW() - INTERVAL '{time_range}'
                    AND task_description ILIKE '%trading%' OR task_description ILIKE '%options%'
                    ORDER BY created_at DESC
                    LIMIT 50
                """,
                "visualization": {
                    "type": "table",
                    "columns": [
                        {"field": "agent_name", "title": "Agent"},
                        {"field": "task_description", "title": "Task", "width": 300},
                        {"field": "evaluation_score", "title": "Score", "type": "number"},
                        {"field": "execution_time_ms", "title": "Duration (ms)", "type": "number"},
                        {"field": "memory_context_count", "title": "Memory Used", "type": "number"},
                        {"field": "created_at", "title": "Timestamp", "type": "datetime"}
                    ]
                }
            },
            
            "trace_performance": {
                "type": "timeseries",
                "title": "Trace Performance Metrics",
                "query": """
                    SELECT 
                        start_time as timestamp,
                        agent_name,
                        duration_ms,
                        operation_type
                    FROM trading_analytics.agent_trace_summary 
                    WHERE start_time >= NOW() - INTERVAL '{time_range}'
                    ORDER BY start_time
                """,
                "data_source": "phoenix_postgres",
                "visualization": {
                    "type": "line",
                    "y_axis": {"label": "Duration (ms)"},
                    "legend": {"show": True}
                }
            }
        }
    
    def _configure_data_sources(self) -> List[Dict[str, Any]]:
        """Configure data sources for the dashboard."""
        return [
            {
                "name": "supabase_main",
                "type": "postgresql",
                "url": config.SUPABASE_URL,
                "database": "postgres",
                "description": "Main Supabase database with agent metrics and memory data"
            },
            {
                "name": "phoenix_postgres",
                "type": "postgresql", 
                "url": "postgresql://phoenix_user:phoenix_pass@localhost:5433/phoenix_db",
                "database": "phoenix_db",
                "description": "Phoenix PostgreSQL database with trace data"
            }
        ]
    
    def _setup_real_time_updates(self) -> Dict[str, Any]:
        """Set up real-time dashboard updates."""
        try:
            # Configure real-time data refresh
            real_time_config = {
                "enabled": True,
                "refresh_interval": self.dashboard_config["refresh_interval"],
                "websocket_enabled": True,
                "auto_refresh_panels": [
                    "agent_health_summary",
                    "evaluation_scores_trend",
                    "memory_utilization",
                    "trace_performance"
                ]
            }
            
            logger.info("Real-time updates configured")
            return real_time_config
            
        except Exception as e:
            logger.warning(f"Failed to setup real-time updates: {e}")
            return {"enabled": False, "error": str(e)}
    
    def _apply_dashboard_config(self, layout: Dict[str, Any], 
                               data_sources: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Apply dashboard configuration to Phoenix."""
        try:
            # This would typically make API calls to Phoenix to configure the dashboard
            # For now, we'll simulate the configuration
            
            config_payload = {
                "dashboard": layout,
                "data_sources": data_sources,
                "panels": self.custom_panels,
                "timestamp": datetime.now().isoformat()
            }
            
            # Save configuration locally for reference
            with open("phoenix/dashboard_config.json", "w") as f:
                json.dump(config_payload, f, indent=2)
            
            logger.info("Dashboard configuration applied successfully")
            
            return {
                "status": "success",
                "panels_configured": len(self.custom_panels),
                "data_sources_configured": len(data_sources)
            }
            
        except Exception as e:
            logger.error(f"Failed to apply dashboard configuration: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
    
    def get_dashboard_status(self) -> Dict[str, Any]:
        """Get current dashboard status and health."""
        try:
            # Check Phoenix connectivity
            phoenix_status = self._check_phoenix_health()
            
            # Check data source connectivity
            data_source_status = self._check_data_sources()
            
            # Get recent metrics
            recent_metrics = self._get_recent_metrics()
            
            return {
                "status": "healthy" if phoenix_status["healthy"] else "unhealthy",
                "phoenix": phoenix_status,
                "data_sources": data_source_status,
                "recent_metrics": recent_metrics,
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get dashboard status: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _check_phoenix_health(self) -> Dict[str, Any]:
        """Check Phoenix server health."""
        try:
            response = requests.get(f"{self.phoenix_url}/health", timeout=5)
            return {
                "healthy": response.status_code == 200,
                "url": self.phoenix_url,
                "response_time_ms": response.elapsed.total_seconds() * 1000
            }
        except Exception as e:
            return {
                "healthy": False,
                "error": str(e)
            }
    
    def _check_data_sources(self) -> Dict[str, Any]:
        """Check data source connectivity."""
        try:
            # Test Supabase connection
            supabase_test = self.supabase.table("agent_health").select("count", count="exact").execute()
            supabase_healthy = supabase_test.count is not None
            
            return {
                "supabase": {
                    "healthy": supabase_healthy,
                    "url": config.SUPABASE_URL
                },
                "phoenix_postgres": {
                    "healthy": True,  # Would test actual connection in production
                    "url": "localhost:5433"
                }
            }
            
        except Exception as e:
            return {
                "error": str(e),
                "supabase": {"healthy": False},
                "phoenix_postgres": {"healthy": False}
            }
    
    def _get_recent_metrics(self) -> Dict[str, Any]:
        """Get recent dashboard metrics."""
        try:
            # Get recent evaluation metrics
            recent_evaluations = self.supabase.table("agent_metrics")\
                .select("evaluation_score, created_at")\
                .gte("created_at", (datetime.now() - timedelta(hours=1)).isoformat())\
                .execute()
            
            if recent_evaluations.data:
                avg_score = sum(item["evaluation_score"] or 0 for item in recent_evaluations.data) / len(recent_evaluations.data)
                total_evaluations = len(recent_evaluations.data)
            else:
                avg_score = 0
                total_evaluations = 0
            
            return {
                "total_evaluations_last_hour": total_evaluations,
                "average_evaluation_score": round(avg_score, 3),
                "data_freshness": "real-time"
            }
            
        except Exception as e:
            logger.warning(f"Failed to get recent metrics: {e}")
            return {
                "error": str(e)
            }


# Utility functions for dashboard integration
def create_dashboard_manager() -> PhoenixDashboardManager:
    """Create a Phoenix dashboard manager instance."""
    return PhoenixDashboardManager()

def setup_phoenix_dashboard() -> Dict[str, Any]:
    """Set up Phoenix dashboard with custom configuration."""
    manager = create_dashboard_manager()
    return manager.setup_custom_dashboard()

def get_dashboard_health() -> Dict[str, Any]:
    """Get Phoenix dashboard health status."""
    manager = create_dashboard_manager()
    return manager.get_dashboard_status()
