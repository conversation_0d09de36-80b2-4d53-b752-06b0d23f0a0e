from supabase import create_client, Client
import wheel_trader.config as config
import json
from datetime import datetime, timed<PERSON><PERSON>

def calculate_health_scores():
    supabase: Client = create_client(config.SUPABASE_URL, config.SUPABASE_KEY)
    yesterday = datetime.now() - timedelta(days=1)
    
    # Get all agent runs from the last 24 hours
    response = supabase.table("agent_metrics").select("*").gte("ts", yesterday.isoformat()).execute()
    
    if not response.data:
        return

    # Aggregate metrics by agent version
    agent_versions = {}
    for run in response.data:
        key = (run['agent_name'], run['agent_version'])
        if key not in agent_versions:
            agent_versions[key] = []
        agent_versions[key].append(json.loads(run['metrics_json']))

    # Calculate and save health scores
    for (agent_name, agent_version), metrics_list in agent_versions.items():
        error_rate = sum(1 for m in metrics_list if m['correctness'] == 0.0) / len(metrics_list)
        avg_latency = sum(m['latency_ms'] for m in metrics_list) / len(metrics_list)
        
        # Simple scoring model
        latency_penalty = min(avg_latency / 10000, 0.5) # Penalize high latency
        score = 1 - error_rate - latency_penalty
        
        health_data = {
            "agent_name": agent_name,
            "agent_version": agent_version,
            "score": score,
        }
        
        supabase.table("agent_health").upsert(health_data).execute()

if __name__ == "__main__":
    calculate_health_scores()
