from supabase import create_client, Client
from wheel_trader import config
import json


def evaluate_run(agent_name, agent_version, result):
    supabase: Client = create_client(config.SUPABASE_URL, config.SUPABASE_KEY)
    # This is a placeholder for a more sophisticated evaluation
    # In a real scenario, you would parse the result and compute metrics
    metrics = {
        "correctness": 1.0 if result.get("exit_code") == 0 else 0.0,
        "latency_ms": result.get("duration_ms", 0),
        "hallucination_score": 0.0,  # Placeholder
    }

    log_data = {
        "agent_name": agent_name,
        "agent_version": agent_version,
        "metrics_json": json.dumps(metrics),
    }

    supabase.table("agent_metrics").insert(log_data).execute()
