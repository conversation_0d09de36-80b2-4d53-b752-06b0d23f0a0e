"""
Enhanced Evaluation Harness with Memory Context Integration

This module provides comprehensive evaluation capabilities that leverage the
memory system for context-aware evaluation, hallucination detection, and
performance analysis with Phoenix trace integration.
"""

import hashlib
import logging
import re
import time
from typing import Any, Dict, List, Optional

from supabase import Client, create_client

from wheel_trader import config
from wheel_trader.memory.manager import MemoryManager
from wheel_trader.observability.tracer import AgentTracer

# Configure logging
logger = logging.getLogger(__name__)


class EnhancedEvaluator:
    """
    Enhanced evaluation harness with memory context integration.
    
    Provides comprehensive evaluation capabilities including:
    - Memory-context evaluation for improved accuracy
    - Hallucination detection using memory patterns
    - Phoenix trace integration for latency metrics
    - Comprehensive metrics storage and analysis
    """
    
    def __init__(self, memory_manager: Optional[MemoryManager] = None, 
                 tracer: Optional[AgentTracer] = None):
        """
        Initialize the enhanced evaluator.
        
        Args:
            memory_manager: Memory manager for context retrieval
            tracer: Agent tracer for observability integration
        """
        self.memory_manager = memory_manager
        self.tracer = tracer
        self.supabase: Client = create_client(config.SUPABASE_URL, config.SUPABASE_KEY)
        
        # Evaluation configuration
        self.config = {
            "memory_context_limit": 5,
            "hallucination_threshold": 0.3,
            "confidence_threshold": 0.7,
            "latency_thresholds": {
                "fast": 1000,      # < 1s
                "normal": 10000,   # 1-10s
                "slow": 30000,     # 10-30s
                # > 30s = timeout
            }
        }
    
    def evaluate_with_memory_context(self, agent_name: str, task: str, 
                                   result: Any, trace_id: str = None,
                                   span_id: str = None, 
                                   execution_time_ms: int = None,
                                   custom_metrics: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Perform comprehensive evaluation with memory context integration.
        
        Args:
            agent_name: Name of the agent being evaluated
            task: Task description that was executed
            result: Result from agent execution
            trace_id: Phoenix trace ID for observability correlation
            span_id: Phoenix span ID for detailed tracing
            execution_time_ms: Execution time in milliseconds
            custom_metrics: Additional custom metrics to include
            
        Returns:
            Comprehensive evaluation metrics dictionary
        """
        start_time = time.time()
        
        try:
            # 1. Retrieve memory context for evaluation
            memory_context = self._get_memory_context(agent_name, task)
            
            # 2. Extract latency from trace if not provided
            if execution_time_ms is None and trace_id:
                execution_time_ms = self._extract_latency_from_trace(trace_id)
            
            # 3. Calculate comprehensive evaluation metrics
            metrics = self._calculate_evaluation_metrics(
                task, result, memory_context, execution_time_ms
            )
            
            # 4. Add custom metrics if provided
            if custom_metrics:
                metrics.update(custom_metrics)
            
            # 5. Store evaluation in database
            evaluation_id = self._store_evaluation_metrics(
                agent_name, task, result, metrics, memory_context,
                trace_id, span_id, execution_time_ms
            )
            
            # 6. Store evaluation result as memory for future reference
            if self.memory_manager:
                self._store_evaluation_memory(
                    agent_name, task, result, metrics, evaluation_id
                )
            
            evaluation_time = int((time.time() - start_time) * 1000)
            logger.info(f"Evaluation completed for {agent_name} in {evaluation_time}ms")
            
            return {
                **metrics,
                "evaluation_id": evaluation_id,
                "evaluation_time_ms": evaluation_time,
                "memory_context_count": len(memory_context)
            }
            
        except Exception as e:
            logger.error(f"Evaluation failed for {agent_name}: {e}")
            return {
                "error": str(e),
                "evaluation_failed": True,
                "evaluation_time_ms": int((time.time() - start_time) * 1000)
            }
    
    def _get_memory_context(self, agent_name: str, task: str) -> List[Dict[str, Any]]:
        """Retrieve relevant memory context for evaluation."""
        if not self.memory_manager:
            return []
        
        try:
            memories = self.memory_manager.search_memories(
                query=task,
                limit=self.config["memory_context_limit"],
                filters={
                    "agent_name": agent_name,
                    "mem_type": ["agent_action", "market_analysis", "evaluation_result"],
                    "min_importance": 0.3
                }
            )
            return memories or []
        except Exception as e:
            logger.warning(f"Failed to retrieve memory context: {e}")
            return []
    
    def _calculate_evaluation_metrics(self, task: str, result: Any, 
                                    memory_context: List[Dict], 
                                    execution_time_ms: int = None) -> Dict[str, Any]:
        """Calculate comprehensive evaluation metrics."""
        
        # Basic metrics
        metrics = {
            "correctness": self._evaluate_correctness(task, result, memory_context),
            "hallucination_score": self._detect_hallucination(result, memory_context),
            "context_relevance": self._evaluate_context_relevance(task, memory_context),
            "confidence_score": self._calculate_confidence(result, memory_context),
            "memory_utilization": len(memory_context),
            "latency_ms": execution_time_ms or 0,
            "latency_category": self._categorize_latency(execution_time_ms),
            "task_complexity": self._assess_task_complexity(task),
            "result_quality": self._assess_result_quality(result)
        }
        
        # Calculate overall evaluation score
        metrics["evaluation_score"] = self._calculate_overall_score(metrics)
        
        return metrics
    
    def _evaluate_correctness(self, task: str, result: Any, 
                            memory_context: List[Dict]) -> float:
        """Evaluate correctness of the result."""
        try:
            # Basic correctness checks
            correctness = 0.5  # Base score
            
            # Check if result is not empty/None
            if result and str(result).strip():
                correctness += 0.2
            
            # Check for error indicators
            result_str = str(result).lower()
            if any(error_word in result_str for error_word in ['error', 'failed', 'exception']):
                correctness -= 0.3
            
            # Check consistency with memory context
            if memory_context:
                consistency_score = self._check_memory_consistency(result, memory_context)
                correctness += consistency_score * 0.3
            
            # Check for financial/trading specific correctness
            if any(keyword in task.lower() for keyword in ['options', 'trading', 'market', 'analysis']):
                correctness += self._evaluate_financial_correctness(result) * 0.2
            
            return max(0.0, min(1.0, correctness))
            
        except Exception as e:
            logger.warning(f"Correctness evaluation failed: {e}")
            return 0.5
    
    def _detect_hallucination(self, result: Any, memory_context: List[Dict]) -> float:
        """Detect potential hallucinations using memory context."""
        try:
            if not memory_context:
                return 0.1  # Low hallucination score when no context available
            
            result_str = str(result).lower()
            hallucination_indicators = 0
            total_checks = 0
            
            # Check for contradictions with memory
            for memory in memory_context:
                memory_content = memory.get('content', '').lower()
                total_checks += 1
                
                # Simple contradiction detection (would be more sophisticated in production)
                if self._check_contradiction(result_str, memory_content):
                    hallucination_indicators += 1
            
            # Check for unrealistic claims
            if self._contains_unrealistic_claims(result_str):
                hallucination_indicators += 1
                total_checks += 1
            
            # Calculate hallucination score
            if total_checks == 0:
                return 0.1
            
            hallucination_score = hallucination_indicators / total_checks
            return min(1.0, hallucination_score)
            
        except Exception as e:
            logger.warning(f"Hallucination detection failed: {e}")
            return 0.1
    
    def _evaluate_context_relevance(self, task: str, memory_context: List[Dict]) -> float:
        """Evaluate relevance of memory context to the task."""
        if not memory_context:
            return 0.0
        
        try:
            task_keywords = set(re.findall(r'\b\w+\b', task.lower()))
            total_relevance = 0.0
            
            for memory in memory_context:
                content = memory.get('content', '')
                memory_keywords = set(re.findall(r'\b\w+\b', content.lower()))
                
                # Calculate keyword overlap
                overlap = len(task_keywords.intersection(memory_keywords))
                relevance = overlap / max(len(task_keywords), 1)
                total_relevance += relevance
            
            return min(1.0, total_relevance / len(memory_context))
            
        except Exception as e:
            logger.warning(f"Context relevance evaluation failed: {e}")
            return 0.0
    
    def _calculate_confidence(self, result: Any, memory_context: List[Dict]) -> float:
        """Calculate confidence score based on result and context."""
        try:
            confidence = 0.5  # Base confidence
            
            # Increase confidence with memory context
            if memory_context:
                confidence += min(0.3, len(memory_context) * 0.1)
            
            # Check result quality indicators
            result_str = str(result)
            if len(result_str) > 50:  # Detailed response
                confidence += 0.1
            
            # Check for uncertainty indicators
            uncertainty_words = ['maybe', 'possibly', 'might', 'uncertain', 'unclear']
            if any(word in result_str.lower() for word in uncertainty_words):
                confidence -= 0.2
            
            # Check for confidence indicators
            confidence_words = ['confident', 'certain', 'definitely', 'clearly']
            if any(word in result_str.lower() for word in confidence_words):
                confidence += 0.1
            
            return max(0.0, min(1.0, confidence))

        except Exception as e:
            logger.warning(f"Confidence calculation failed: {e}")
            return 0.5

    def _categorize_latency(self, execution_time_ms: int = None) -> str:
        """Categorize execution latency."""
        if execution_time_ms is None:
            return 'unknown'

        thresholds = self.config["latency_thresholds"]
        if execution_time_ms < thresholds["fast"]:
            return 'fast'
        elif execution_time_ms < thresholds["normal"]:
            return 'normal'
        elif execution_time_ms < thresholds["slow"]:
            return 'slow'
        else:
            return 'timeout'

    def _assess_task_complexity(self, task: str) -> float:
        """Assess the complexity of the task."""
        try:
            complexity = 0.3  # Base complexity

            # Length-based complexity
            if len(task) > 200:
                complexity += 0.2
            elif len(task) > 100:
                complexity += 0.1

            # Keyword-based complexity
            complex_keywords = ['analyze', 'compare', 'evaluate', 'strategy', 'optimize']
            complexity += sum(0.1 for keyword in complex_keywords if keyword in task.lower())

            # Financial complexity indicators
            financial_keywords = ['options', 'derivatives', 'volatility', 'risk', 'portfolio']
            complexity += sum(0.05 for keyword in financial_keywords if keyword in task.lower())

            return min(1.0, complexity)

        except Exception as e:
            logger.warning(f"Task complexity assessment failed: {e}")
            return 0.5

    def _assess_result_quality(self, result: Any) -> float:
        """Assess the quality of the result."""
        try:
            if not result:
                return 0.0

            result_str = str(result)
            quality = 0.3  # Base quality

            # Length and detail
            if len(result_str) > 100:
                quality += 0.2
            if len(result_str) > 500:
                quality += 0.1

            # Structure indicators
            if any(indicator in result_str for indicator in ['\n', ':', '-', '•']):
                quality += 0.1

            # Financial content quality
            financial_terms = ['price', 'volume', 'analysis', 'recommendation', 'risk']
            quality += sum(0.05 for term in financial_terms if term in result_str.lower())

            return min(1.0, quality)

        except Exception as e:
            logger.warning(f"Result quality assessment failed: {e}")
            return 0.3

    def _calculate_overall_score(self, metrics: Dict[str, Any]) -> float:
        """Calculate overall evaluation score from individual metrics."""
        try:
            # Weighted scoring
            weights = {
                "correctness": 0.4,
                "confidence_score": 0.2,
                "context_relevance": 0.15,
                "result_quality": 0.15,
                "hallucination_penalty": 0.1
            }

            score = (
                metrics.get("correctness", 0.5) * weights["correctness"] +
                metrics.get("confidence_score", 0.5) * weights["confidence_score"] +
                metrics.get("context_relevance", 0.0) * weights["context_relevance"] +
                metrics.get("result_quality", 0.3) * weights["result_quality"]
            )

            # Apply hallucination penalty
            hallucination_score = metrics.get("hallucination_score", 0.1)
            hallucination_penalty = hallucination_score * weights["hallucination_penalty"]
            score = max(0.0, score - hallucination_penalty)

            # Latency penalty
            latency_category = metrics.get("latency_category", "normal")
            if latency_category == "slow":
                score *= 0.9
            elif latency_category == "timeout":
                score *= 0.7

            return min(1.0, score)

        except Exception as e:
            logger.warning(f"Overall score calculation failed: {e}")
            return 0.5

    def _check_memory_consistency(self, result: Any, memory_context: List[Dict]) -> float:
        """Check consistency between result and memory context."""
        try:
            result_str = str(result).lower()
            consistency_scores = []

            for memory in memory_context:
                content = memory.get('content', '').lower()

                # Simple keyword overlap check
                result_words = set(re.findall(r'\b\w+\b', result_str))
                memory_words = set(re.findall(r'\b\w+\b', content))

                if result_words and memory_words:
                    overlap = len(result_words.intersection(memory_words))
                    consistency = overlap / max(len(result_words), len(memory_words))
                    consistency_scores.append(consistency)

            return sum(consistency_scores) / len(consistency_scores) if consistency_scores else 0.0

        except Exception as e:
            logger.warning(f"Memory consistency check failed: {e}")
            return 0.0

    def _check_contradiction(self, result_str: str, memory_content: str) -> bool:
        """Check for contradictions between result and memory content."""
        try:
            # NOTE: Simple contradiction patterns (would be more sophisticated in production)
            contradiction_patterns = [
                (r'\bbullish\b', r'\bbearish\b'),
                (r'\bbuy\b', r'\bsell\b'),
                (r'\bup\b', r'\bdown\b'),
                (r'\bpositive\b', r'\bnegative\b'),
                (r'\bprofit\b', r'\bloss\b')
            ]

            for pattern1, pattern2 in contradiction_patterns:
                if (re.search(pattern1, result_str) and re.search(pattern2, memory_content)) or \
                   (re.search(pattern2, result_str) and re.search(pattern1, memory_content)):
                    return True

            return False

        except Exception as e:
            logger.warning(f"Contradiction check failed: {e}")
            return False

    def _contains_unrealistic_claims(self, result_str: str) -> bool:
        """Check for unrealistic claims in the result."""
        try:
            # NOTE: Patterns that might indicate unrealistic claims
            unrealistic_patterns = [
                r'\b100%\s+(profit|return|guarantee)',
                r'\brisk-free\b',
                r'\bguaranteed\s+(profit|return)',
                r'\b(never|always)\s+(profitable|successful)',
                r'\b1000%\s+return'
            ]

            return any(re.search(pattern, result_str, re.IGNORECASE) for pattern in unrealistic_patterns)

        except Exception as e:
            logger.warning(f"Unrealistic claims check failed: {e}")
            return False

    def _evaluate_financial_correctness(self, result: Any) -> float:
        """Evaluate financial/trading specific correctness."""
        try:
            result_str = str(result).lower()
            correctness = 0.0

            # Check for proper financial terminology
            financial_terms = ['options', 'strike', 'expiration', 'premium', 'volatility', 'delta']
            term_score = sum(0.1 for term in financial_terms if term in result_str)
            correctness += min(0.5, term_score)

            # Check for risk awareness
            risk_terms = ['risk', 'loss', 'potential', 'caution', 'consider']
            risk_score = sum(0.1 for term in risk_terms if term in result_str)
            correctness += min(0.3, risk_score)

            # Check for unrealistic promises (negative score)
            if self._contains_unrealistic_claims(result_str):
                correctness -= 0.5

            return max(0.0, min(1.0, correctness))

        except Exception as e:
            logger.warning(f"Financial correctness evaluation failed: {e}")
            return 0.0

    def _extract_latency_from_trace(self, trace_id: str) -> Optional[int]:
        """Extract latency from Phoenix trace."""
        try:
            if not self.tracer or not trace_id:
                return None

            # Get trace stats from tracer
            trace_stats = self.tracer.get_trace_stats()

            # Find the specific trace
            for trace in trace_stats.get("recent_traces", []):
                if trace.get("trace_id") == trace_id:
                    return trace.get("duration_ms")

            return None

        except Exception as e:
            logger.warning(f"Failed to extract latency from trace: {e}")
            return None

    def _store_evaluation_metrics(self, agent_name: str, task: str, result: Any,
                                 metrics: Dict[str, Any], memory_context: List[Dict],
                                 trace_id: str = None, span_id: str = None,
                                 execution_time_ms: int = None) -> str:
        """Store evaluation metrics in the database."""
        try:
            # Prepare memory IDs
            memory_ids = [mem.get('id') for mem in memory_context if mem.get('id')]

            # Create task hash for deduplication
            task_hash = hashlib.sha256(task.encode()).hexdigest()

            # Prepare data for database
            data = {
                "agent_name": agent_name,
                "agent_version": "1.0",  # Default version
                "task_description": task,
                "task_hash": task_hash,
                "metrics_json": json.dumps(metrics),
                "execution_time_ms": execution_time_ms,
                "memory_context_used": len(memory_context) > 0,
                "memory_ids": memory_ids,
                "memory_context_count": len(memory_context),
                "trace_id": trace_id,
                "span_id": span_id,
                "evaluation_score": metrics.get("evaluation_score"),
                "correctness_score": metrics.get("correctness"),
                "hallucination_score": metrics.get("hallucination_score"),
                "context_relevance_score": metrics.get("context_relevance"),
                "confidence_score": metrics.get("confidence_score"),
                "latency_category": metrics.get("latency_category", "normal"),
                "success": metrics.get("evaluation_score", 0.5) > 0.5,
                "error_message": None
            }

            # Insert into database
            response = self.supabase.table("agent_metrics").insert(data).execute()

            if response.data:
                return response.data[0]["id"]
            else:
                logger.error("Failed to store evaluation metrics")
                return None

        except Exception as e:
            logger.error(f"Failed to store evaluation metrics: {e}")
            return None

    def _store_evaluation_memory(self, agent_name: str, task: str, result: Any,
                                metrics: Dict[str, Any], evaluation_id: str):
        """Store evaluation result as memory for future reference."""
        try:
            if not self.memory_manager:
                return

            # Create memory content
            content = f"EVALUATION: {task} | Score: {metrics.get('evaluation_score', 0.5):.2f} | Result: {str(result)[:200]}..."

            # Prepare metadata
            metadata = {
                "evaluation_id": evaluation_id,
                "evaluation_score": metrics.get("evaluation_score"),
                "correctness": metrics.get("correctness"),
                "hallucination_score": metrics.get("hallucination_score"),
                "confidence_score": metrics.get("confidence_score"),
                "task_hash": hashlib.sha256(task.encode()).hexdigest(),
                "evaluation_timestamp": datetime.now().isoformat()
            }

            # Store as memory
            self.memory_manager.store_memory(
                content=content,
                metadata=metadata,
                mem_type="evaluation_result",
                importance=metrics.get("evaluation_score", 0.5),
                tags=["evaluation", "performance", agent_name]
            )

        except Exception as e:
            logger.warning(f"Failed to store evaluation memory: {e}")

    def get_agent_performance_summary(self, agent_name: str, days_back: int = 7) -> Dict[str, Any]:
        """Get performance summary for an agent."""
        try:
            # Use the database function
            response = self.supabase.rpc(
                "get_agent_performance_trends",
                {"p_agent_name": agent_name, "p_days_back": days_back}
            ).execute()

            if response.data:
                return {
                    "agent_name": agent_name,
                    "days_analyzed": days_back,
                    "performance_trends": response.data,
                    "summary": self._calculate_performance_summary(response.data)
                }
            else:
                return {"agent_name": agent_name, "error": "No data found"}

        except Exception as e:
            logger.error(f"Failed to get performance summary: {e}")
            return {"agent_name": agent_name, "error": str(e)}

    def _calculate_performance_summary(self, trends_data: List[Dict]) -> Dict[str, Any]:
        """Calculate summary statistics from trends data."""
        if not trends_data:
            return {}

        try:
            total_evaluations = sum(row.get("evaluations_count", 0) for row in trends_data)
            avg_score = sum(row.get("avg_score", 0) for row in trends_data) / len(trends_data)
            avg_success_rate = sum(row.get("success_rate", 0) for row in trends_data) / len(trends_data)
            avg_execution_time = sum(row.get("avg_execution_time", 0) for row in trends_data) / len(trends_data)

            return {
                "total_evaluations": total_evaluations,
                "average_score": round(avg_score, 3),
                "average_success_rate": round(avg_success_rate, 3),
                "average_execution_time_ms": round(avg_execution_time, 0),
                "performance_grade": self._calculate_performance_grade(avg_score, avg_success_rate)
            }

        except Exception as e:
            logger.warning(f"Failed to calculate performance summary: {e}")
            return {}

    def _calculate_performance_grade(self, avg_score: float, avg_success_rate: float) -> str:
        """Calculate performance grade based on metrics."""
        combined_score = (avg_score + avg_success_rate) / 2

        if combined_score >= 0.9:
            return "A"
        elif combined_score >= 0.8:
            return "B"
        elif combined_score >= 0.7:
            return "C"
        elif combined_score >= 0.6:
            return "D"
        else:
            return "F"


# Backward compatibility function
def evaluate_run(agent_name: str, agent_version: str, result: Dict[str, Any]) -> Dict[str, Any]:
    """
    Backward compatibility function for existing evaluator.py usage.

    This function maintains compatibility with the existing evaluation interface
    while providing enhanced capabilities through the new EnhancedEvaluator.
    """
    try:
        # Create enhanced evaluator instance
        evaluator = EnhancedEvaluator()

        # Extract task from result if available
        task = result.get("task", "Unknown task")

        # Extract execution time
        execution_time_ms = result.get("duration_ms", 0)

        # Perform enhanced evaluation
        enhanced_metrics = evaluator.evaluate_with_memory_context(
            agent_name=agent_name,
            task=task,
            result=result,
            execution_time_ms=execution_time_ms
        )

        # Return in expected format for backward compatibility
        return {
            "correctness": enhanced_metrics.get("correctness", 1.0 if result.get("exit_code") == 0 else 0.0),
            "latency_ms": enhanced_metrics.get("latency_ms", execution_time_ms),
            "hallucination_score": enhanced_metrics.get("hallucination_score", 0.0),
            "evaluation_score": enhanced_metrics.get("evaluation_score", 0.5),
            "enhanced_metrics": enhanced_metrics
        }

    except Exception as e:
        logger.error(f"Enhanced evaluation failed, falling back to basic: {e}")

        # Fallback to basic evaluation
        metrics = {
            "correctness": 1.0 if result.get("exit_code") == 0 else 0.0,
            "latency_ms": result.get("duration_ms", 0),
            "hallucination_score": 0.0,
        }

        return metrics
