"""
Enhanced Memory Manager

High-level memory operations with health integration for options trading agents.
Provides a unified interface for memory operations across different backends.
"""

import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime

from .backends import MemoryBackend, create_memory_backend, MemoryStorageError
from ..agent_health import AgentHealthManager

# Configure logging
logger = logging.getLogger(__name__)


class MemoryManager:
    """
    High-level memory manager that integrates with agent health monitoring
    and provides a unified interface for memory operations.
    """
    
    def __init__(self, backend: str = "pgvector", config: Optional[Dict[str, Any]] = None,
                 agent_name: Optional[str] = None, health_manager: Optional[AgentHealthManager] = None):
        """
        Initialize memory manager
        
        Args:
            backend: Backend type ("pgvector" or "mem0")
            config: Backend-specific configuration
            agent_name: Name of the agent using this manager
            health_manager: Optional health manager instance
        """
        self.agent_name = agent_name
        self.backend_type = backend
        self.config = config or {}
        
        # Initialize backend
        try:
            self.backend: MemoryBackend = create_memory_backend(backend, config)
            logger.info(f"MemoryManager: Initialized with {backend} backend for agent {agent_name}")
        except Exception as e:
            logger.error(f"MemoryManager: Failed to initialize {backend} backend: {e}")
            # Fallback to basic backend if available
            if backend != "pgvector":
                try:
                    self.backend = create_memory_backend("pgvector", {})
                    self.backend_type = "pgvector"
                    logger.warning(f"MemoryManager: Fell back to pgvector backend")
                except Exception as fallback_error:
                    logger.error(f"MemoryManager: Fallback also failed: {fallback_error}")
                    raise MemoryStorageError(f"Failed to initialize any memory backend: {e}")
            else:
                raise MemoryStorageError(f"Failed to initialize memory backend: {e}")
        
        # Initialize health manager if provided
        self.health_manager = health_manager
        
        # Memory operation statistics
        self.stats = {
            "stores": 0,
            "searches": 0,
            "errors": 0,
            "last_operation": None
        }
    
    def store_memory(self, content: str, metadata: Optional[Dict[str, Any]] = None,
                    mem_type: str = "agent_action", importance: float = 0.5,
                    tags: Optional[List[str]] = None, expires_in_hours: Optional[int] = None,
                    trace_id: Optional[str] = None, span_id: Optional[str] = None) -> str:
        """
        Store a memory with enhanced metadata
        
        Args:
            content: Memory content
            metadata: Additional metadata
            mem_type: Type of memory (agent_action, market_analysis, etc.)
            importance: Importance score (0.0 to 1.0)
            tags: List of tags for categorization
            expires_in_hours: Optional expiration time in hours
            trace_id: Optional trace ID for observability
            span_id: Optional span ID for observability
            
        Returns:
            Memory ID
        """
        try:
            # Prepare enhanced metadata
            enhanced_metadata = {
                "importance": max(0.0, min(1.0, importance)),  # Clamp to valid range
                "tags": tags or [],
                "mem_type": mem_type,
                "agent_name": self.agent_name,
                "created_by": "MemoryManager",
                "backend_type": self.backend_type
            }
            
            # Add custom metadata
            if metadata:
                enhanced_metadata.update(metadata)
            
            # Add expiration if specified
            if expires_in_hours:
                enhanced_metadata["expires_in_hours"] = expires_in_hours
            
            # Store memory
            memory_id = self.backend.store_memory(
                content=content,
                metadata=enhanced_metadata,
                agent_name=self.agent_name or "unknown",
                mem_type=mem_type,
                trace_id=trace_id,
                span_id=span_id
            )
            
            # Update statistics
            self.stats["stores"] += 1
            self.stats["last_operation"] = datetime.now().isoformat()
            
            logger.debug(f"MemoryManager: Stored memory {memory_id} for agent {self.agent_name}")
            return memory_id
            
        except Exception as e:
            self.stats["errors"] += 1
            logger.error(f"MemoryManager: Failed to store memory: {e}")
            
            # Update health manager if available
            if self.health_manager and self.agent_name:
                # This will be reflected in the agent's health score
                pass
            
            raise MemoryStorageError(f"Failed to store memory: {e}")
    
    def search_memories(self, query: str, limit: int = 5, 
                       filters: Optional[Dict[str, Any]] = None,
                       agent_filter: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Search for relevant memories
        
        Args:
            query: Search query
            limit: Maximum number of results
            filters: Additional filters (mem_type, tags, etc.)
            agent_filter: Filter by specific agent (defaults to current agent)
            
        Returns:
            List of matching memories
        """
        try:
            # Use current agent if no filter specified
            if agent_filter is None:
                agent_filter = self.agent_name
            
            # Search memories
            memories = self.backend.search_memories(
                query=query,
                agent_name=agent_filter,
                filters=filters,
                limit=limit
            )
            
            # Update statistics
            self.stats["searches"] += 1
            self.stats["last_operation"] = datetime.now().isoformat()
            
            logger.debug(f"MemoryManager: Found {len(memories)} memories for query")
            return memories
            
        except Exception as e:
            self.stats["errors"] += 1
            logger.error(f"MemoryManager: Failed to search memories: {e}")
            
            # Return empty list on error to allow graceful degradation
            return []
    
    def search_by_content(self, search_text: str, limit: int = 10,
                         mem_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Fast text-based search for exact content matches
        
        Args:
            search_text: Text to search for
            limit: Maximum number of results
            mem_type: Optional memory type filter
            
        Returns:
            List of matching memories
        """
        try:
            if hasattr(self.backend, 'search_by_content'):
                memories = self.backend.search_by_content(
                    search_text=search_text,
                    agent_name=self.agent_name,
                    mem_type=mem_type,
                    limit=limit
                )
                
                self.stats["searches"] += 1
                self.stats["last_operation"] = datetime.now().isoformat()
                
                return memories
            else:
                # Fallback to regular search
                return self.search_memories(search_text, limit=limit, 
                                          filters={"mem_type": mem_type} if mem_type else None)
                
        except Exception as e:
            self.stats["errors"] += 1
            logger.error(f"MemoryManager: Failed to search by content: {e}")
            return []
    
    def get_recent_memories(self, hours: int = 24, limit: int = 10,
                           mem_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get recent memories within specified time window
        
        Args:
            hours: Time window in hours
            limit: Maximum number of results
            mem_type: Optional memory type filter
            
        Returns:
            List of recent memories
        """
        filters = {"time_window_hours": hours}
        if mem_type:
            filters["mem_type"] = mem_type
        
        return self.search_memories("", limit=limit, filters=filters)
    
    def get_important_memories(self, min_importance: float = 0.7, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get high-importance memories
        
        Args:
            min_importance: Minimum importance score
            limit: Maximum number of results
            
        Returns:
            List of important memories
        """
        filters = {"min_importance": min_importance}
        return self.search_memories("", limit=limit, filters=filters)
    
    def consolidate_memories(self, similarity_threshold: float = 0.95,
                           max_consolidations: int = 100) -> int:
        """
        Consolidate similar memories to reduce redundancy
        
        Args:
            similarity_threshold: Similarity threshold for consolidation
            max_consolidations: Maximum number of consolidations to perform
            
        Returns:
            Number of memories consolidated
        """
        try:
            if hasattr(self.backend, 'consolidate_memories'):
                count = self.backend.consolidate_memories(
                    agent_name=self.agent_name,
                    similarity_threshold=similarity_threshold,
                    max_consolidations=max_consolidations
                )
                
                logger.info(f"MemoryManager: Consolidated {count} memories for agent {self.agent_name}")
                return count
            else:
                logger.warning("MemoryManager: Backend does not support memory consolidation")
                return 0
                
        except Exception as e:
            logger.error(f"MemoryManager: Failed to consolidate memories: {e}")
            return 0
    
    def cleanup_expired_memories(self) -> int:
        """
        Remove expired memories
        
        Returns:
            Number of memories cleaned up
        """
        try:
            if hasattr(self.backend, 'cleanup_expired_memories'):
                count = self.backend.cleanup_expired_memories()
                logger.info(f"MemoryManager: Cleaned up {count} expired memories")
                return count
            else:
                logger.warning("MemoryManager: Backend does not support cleanup")
                return 0
                
        except Exception as e:
            logger.error(f"MemoryManager: Failed to cleanup expired memories: {e}")
            return 0
    
    def get_health_status(self) -> Dict[str, Any]:
        """
        Get comprehensive health status including backend and manager stats
        
        Returns:
            Health status dictionary
        """
        try:
            backend_health = self.backend.get_health_status()
            
            # Calculate manager-level health metrics
            total_ops = self.stats["stores"] + self.stats["searches"]
            success_rate = 1.0
            if total_ops > 0:
                success_rate = (total_ops - self.stats["errors"]) / total_ops
            
            return {
                "manager_healthy": success_rate > 0.9,
                "backend_health": backend_health,
                "agent_name": self.agent_name,
                "backend_type": self.backend_type,
                "manager_stats": {
                    "total_operations": total_ops,
                    "stores": self.stats["stores"],
                    "searches": self.stats["searches"],
                    "errors": self.stats["errors"],
                    "success_rate": success_rate,
                    "last_operation": self.stats["last_operation"]
                }
            }
            
        except Exception as e:
            logger.error(f"MemoryManager: Health check failed: {e}")
            return {
                "manager_healthy": False,
                "error": str(e),
                "agent_name": self.agent_name,
                "backend_type": self.backend_type
            }
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """
        Get memory usage statistics
        
        Returns:
            Memory statistics dictionary
        """
        return {
            "agent_name": self.agent_name,
            "backend_type": self.backend_type,
            "operations": self.stats.copy(),
            "backend_health": self.backend.get_health_status()
        }
