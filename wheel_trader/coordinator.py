"""
Flask Coordinator Blueprint for CoveredCalls-Agents

This module provides the Flask web interface for the trading agent system.
It handles HTTP requests and coordinates agent execution through the enhanced
SecureCoordinator with E2B integration, health monitoring, and observability.
"""

import logging
import time
import traceback

from flask import Blueprint, jsonify, request

# Import enhanced coordinator
from .secure_coordinator import SecureCoordinator

# Configure logging
logger = logging.getLogger(__name__)

# Create Flask blueprint
coordinator_app = Blueprint('coordinator', __name__)

# Global coordinator instance (enhanced version)

def analyze_task_and_execute(
    coordinator: SecureCoordinator, task: str
) -> dict:
    """
    Analyze a task and execute appropriate tools using SecureCoordinator.
    This provides backward compatibility with the SimpleCoordinator interface.
    """
    task_lower = task.lower()

    # Simple task routing logic
    if any(word in task_lower for word in ["analyze", "analysis", "rsi", "sma", "bollinger"]):
        # Extract symbol if possible
        words = task.split()
        symbol = "AAPL"  # Default symbol
        for word in words:
            if word.isupper() and len(word) <= 5:  # Likely a stock symbol
                symbol = word
                break

        return coordinator.execute_tool_directly(
            "market_analysis",
            symbol=symbol,
            analysis_type="rsi"
        )

    elif any(word in task_lower for word in ["options", "chain", "expiry", "expiration"]):
        # Extract symbol and date if possible
        words = task.split()
        symbol = "AAPL"  # Default symbol
        expiry_date = "2024-02-16"  # Default expiry

        for word in words:
            if word.isupper() and len(word) <= 5:
                symbol = word
                break

        return coordinator.execute_tool_directly(
            "options_data",
            symbol=symbol,
            expiry_date=expiry_date
        )

    elif any(word in task_lower for word in ["risk", "position", "size"]):
        return coordinator.execute_tool_directly(
            "risk_assessment",
            symbol="AAPL",
            position_size=10000,
            portfolio_value=100000
        )

    else:
        # Default to market analysis
        return coordinator.execute_tool_directly(
            "market_analysis",
            symbol="AAPL",
            analysis_type="rsi"
        )


# Initialize global coordinator with SecureCoordinator
try:
    coordinator = SecureCoordinator(
        health_threshold=0.7,
        memory_backend="pgvector",
        enable_observability=True
    )
    logger.info("SecureCoordinator initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize SecureCoordinator: {e}")
    coordinator = None


@coordinator_app.route('/execute_task', methods=['POST'])
def execute_task():
    """
    Execute a task through the agent system.

    Expected JSON payload:
    {
        "task": "Analyze AAPL for covered call opportunities"
    }

    Returns JSON response with execution results.
    """
    try:
        # Validate coordinator is available
        if coordinator is None:
            return jsonify({
                "success": False,
                "error": "Coordinator not initialized",
                "status_code": 500
            }), 500

        # Parse request
        if not request.is_json:
            return jsonify({
                "success": False,
                "error": "Request must be JSON",
                "status_code": 400
            }), 400

        data = request.get_json()
        if not data or 'task' not in data:
            return jsonify({
                "success": False,
                "error": "Missing 'task' field in request",
                "status_code": 400
            }), 400

        task = data['task']
        # Update stats (SecureCoordinator uses different stat names)
        if hasattr(coordinator.stats, 'get'):
            coordinator.stats["requests_handled"] = coordinator.stats.get("requests_handled", 0) + 1
        else:
            # For SecureCoordinator, we'll track this differently
            coordinator.stats["tools_executed"] += 1

        logger.info(f"Executing task: {task}")

        # Execute task using enhanced coordinator
        start_time = time.time()
        result = analyze_task_and_execute(coordinator, task)
        execution_time = time.time() - start_time

        # Prepare response
        response = {
            "task": task,
            "success": result.get("success", False),
            "result": result.get("result"),
            "execution_time": execution_time,
            "timestamp": time.time()
        }

        # Add error information if execution failed
        if not result.get("success", False):
            response["error"] = result.get("error", "Unknown error")
            response["exit_code"] = 1
            status_code = 500
        else:
            response["exit_code"] = 0
            status_code = 200

        # Add tool information if available
        if "tool" in result:
            response["tool_used"] = result["tool"]

        logger.info(f"Task completed: success={response['success']}, time={execution_time:.2f}s")

        return jsonify(response), status_code

    except Exception as e:
        logger.error(f"Error executing task: {e}")
        logger.error(traceback.format_exc())

        return jsonify({
            "success": False,
            "error": f"Internal server error: {str(e)}",
            "task": data.get('task', 'unknown') if 'data' in locals() else 'unknown',
            "exit_code": 1,
            "status_code": 500
        }), 500


@coordinator_app.route('/status', methods=['GET'])
def get_status():
    """Get coordinator status and health information"""
    try:
        if coordinator is None:
            return jsonify({
                "status": "error",
                "error": "Coordinator not initialized"
            }), 500

        status = coordinator.get_status()
        return jsonify(status), 200

    except Exception as e:
        logger.error(f"Error getting status: {e}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500


@coordinator_app.route('/tools', methods=['GET'])
def list_tools():
    """List available tools and their descriptions"""
    try:
        if coordinator is None:
            return jsonify({
                "error": "Coordinator not initialized"
            }), 500

        tools_info = {}
        for name, tool_func in coordinator.available_tools.items():
            # Extract docstring if available
            if hasattr(tool_func, '__doc__'):
                doc = tool_func.__doc__ or "No description available"
            else:
                doc = "Tool description not available"

            # Get function name safely
            if hasattr(tool_func, '__name__'):
                func_name = tool_func.__name__
            elif hasattr(tool_func, 'name'):
                func_name = tool_func.name
            else:
                func_name = name

            tools_info[name] = {
                "description": doc.strip(),
                "function_name": func_name
            }

        return jsonify({
            "available_tools": tools_info,
            "total_tools": len(tools_info)
        }), 200

    except Exception as e:
        logger.error(f"Error listing tools: {e}")
        return jsonify({
            "error": str(e)
        }), 500


@coordinator_app.route('/health', methods=['GET'])
def health_check():
    """Simple health check endpoint"""
    try:
        if coordinator is None:
            return jsonify({
                "healthy": False,
                "error": "Coordinator not initialized"
            }), 503

        # Basic health checks
        health_status = {
            "healthy": True,
            "coordinator": "operational",
            "tools_available": len(coordinator.available_tools),
            "uptime": time.time() - coordinator.stats["start_time"]
        }

        # Check if health manager is working
        try:
            if coordinator.health_manager:
                health_status["health_manager"] = "available"
            else:
                health_status["health_manager"] = "unavailable"
        except Exception as e:
            health_status["health_manager"] = f"error: {str(e)}"

        return jsonify(health_status), 200

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({
            "healthy": False,
            "error": str(e)
        }), 503


# Error handlers for the blueprint
@coordinator_app.errorhandler(404)
def not_found(error):
    return jsonify({
        "error": "Endpoint not found",
        "available_endpoints": ["/execute_task", "/status", "/tools", "/health"]
    }), 404


@coordinator_app.errorhandler(500)
def internal_error(error):
    return jsonify({
        "error": "Internal server error",
        "message": "Please check the server logs for details"
    }), 500
