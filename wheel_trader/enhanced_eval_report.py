"""
Enhanced Evaluation Reporter with Memory-Aware Analytics

This module provides comprehensive nightly reporting capabilities that integrate
memory analytics, evaluation metrics, observability data, and actionable
recommendations for the options trading agents system.
"""

import asyncio
import logging
import statistics
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple

from supabase import Client, create_client

from wheel_trader import config
from wheel_trader.memory.manager import MemoryManager
from wheel_trader.observability.tracer import AgentTracer
from wheel_trader.phoenix_dashboard import PhoenixDashboardManager

# Configure logging
logger = logging.getLogger(__name__)


@dataclass
class AgentPerformanceMetrics:
    """Data class for agent performance metrics."""
    agent_name: str
    total_evaluations: int
    avg_evaluation_score: float
    avg_correctness: float
    avg_confidence: float
    avg_hallucination_score: float
    memory_utilization_rate: float
    avg_execution_time_ms: float
    success_rate: float
    error_count: int
    performance_grade: str
    trend_direction: str  # 'improving', 'declining', 'stable'
    recommendations: List[str]


@dataclass
class MemorySystemMetrics:
    """Data class for memory system metrics."""
    total_memories: int
    memories_created_today: int
    avg_memory_relevance: float
    memory_hit_rate: float
    top_memory_types: List[Tuple[str, int]]
    memory_consolidation_rate: float
    storage_efficiency: float
    recommendations: List[str]


@dataclass
class SystemHealthMetrics:
    """Data class for overall system health."""
    overall_health_score: float
    phoenix_status: str
    database_performance: Dict[str, float]
    trace_ingestion_rate: float
    error_rate: float
    alert_count: int
    uptime_percentage: float
    recommendations: List[str]


class EnhancedEvalReporter:
    """
    Enhanced evaluation reporter with comprehensive analytics and recommendations.
    
    Provides:
    - Memory-aware performance analysis
    - Observability metrics integration
    - Actionable recommendations
    - Automated report generation and distribution
    - Trend analysis and forecasting
    """
    
    def __init__(self, memory_manager: Optional[MemoryManager] = None,
                 tracer: Optional[AgentTracer] = None,
                 dashboard_manager: Optional[PhoenixDashboardManager] = None):
        """
        Initialize the enhanced evaluation reporter.
        
        Args:
            memory_manager: Memory manager for memory analytics
            tracer: Agent tracer for observability metrics
            dashboard_manager: Phoenix dashboard manager for system health
        """
        self.memory_manager = memory_manager
        self.tracer = tracer
        self.dashboard_manager = dashboard_manager or PhoenixDashboardManager()
        self.supabase: Client = create_client(config.SUPABASE_URL, config.SUPABASE_KEY)
        
        # Report configuration
        self.config = {
            "report_period_hours": 24,
            "min_evaluations_for_analysis": 5,
            "performance_thresholds": {
                "excellent": 0.9,
                "good": 0.8,
                "fair": 0.6,
                "poor": 0.4
            },
            "trend_analysis_days": 7,
            "email_enabled": False,  # NOTE: Configure as needed
            "email_recipients": [],
            "include_detailed_analysis": True
        }
    
    async def generate_comprehensive_report(self, report_date: Optional[datetime] = None) -> Dict[str, Any]:
        """
        Generate comprehensive nightly report with memory analytics.
        
        Args:
            report_date: Date for the report (defaults to yesterday)
            
        Returns:
            Comprehensive report dictionary
        """
        if report_date is None:
            report_date = datetime.now() - timedelta(days=1)
        
        logger.info(f"Generating comprehensive report for {report_date.date()}")
        
        try:
            # Collect all metrics in parallel
            tasks = [
                self._get_agent_performance_metrics(report_date),
                self._get_memory_system_metrics(report_date),
                self._get_system_health_metrics(report_date),
                self._get_observability_metrics(report_date),
                self._get_trading_specific_metrics(report_date)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            agent_metrics, memory_metrics, health_metrics, obs_metrics, trading_metrics = results
            
            # Handle any exceptions
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"Failed to collect metrics {i}: {result}")
                    results[i] = {}
            
            # Generate executive summary
            executive_summary = self._generate_executive_summary(
                agent_metrics, memory_metrics, health_metrics
            )
            
            # Generate recommendations
            recommendations = self._generate_comprehensive_recommendations(
                agent_metrics, memory_metrics, health_metrics, trading_metrics
            )
            
            # Calculate trend analysis
            trend_analysis = await self._calculate_trend_analysis(report_date)
            
            # Compile final report
            report = {
                "report_metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "report_date": report_date.date().isoformat(),
                    "report_period_hours": self.config["report_period_hours"],
                    "version": "2.0.0"
                },
                "executive_summary": executive_summary,
                "agent_performance": agent_metrics,
                "memory_analytics": memory_metrics,
                "system_health": health_metrics,
                "observability_metrics": obs_metrics,
                "trading_analytics": trading_metrics,
                "trend_analysis": trend_analysis,
                "recommendations": recommendations,
                "action_items": self._generate_action_items(recommendations),
                "appendix": {
                    "methodology": self._get_methodology_notes(),
                    "data_sources": self._get_data_sources_info(),
                    "glossary": self._get_glossary()
                }
            }
            
            # Save report
            await self._save_report(report, report_date)
            
            # Send notifications if configured
            if self.config["email_enabled"]:
                await self._send_report_notification(report)
            
            logger.info("Comprehensive report generation completed successfully")
            return report
            
        except Exception as e:
            logger.error(f"Failed to generate comprehensive report: {e}")
            return {
                "error": str(e),
                "report_metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "status": "failed"
                }
            }
    
    async def _get_agent_performance_metrics(self, report_date: datetime) -> List[AgentPerformanceMetrics]:
        """Get comprehensive agent performance metrics."""
        try:
            start_time = report_date.replace(hour=0, minute=0, second=0, microsecond=0)
            end_time = start_time + timedelta(days=1)
            
            # Query agent metrics
            response = self.supabase.table("agent_metrics")\
                .select("*")\
                .gte("created_at", start_time.isoformat())\
                .lt("created_at", end_time.isoformat())\
                .execute()
            
            if not response.data:
                return []
            
            # Group by agent
            agent_data = {}
            for record in response.data:
                agent_name = record["agent_name"]
                if agent_name not in agent_data:
                    agent_data[agent_name] = []
                agent_data[agent_name].append(record)
            
            # Calculate metrics for each agent
            agent_metrics = []
            for agent_name, records in agent_data.items():
                if len(records) < self.config["min_evaluations_for_analysis"]:
                    continue
                
                metrics = self._calculate_agent_metrics(agent_name, records)
                agent_metrics.append(metrics)
            
            return agent_metrics
            
        except Exception as e:
            logger.error(f"Failed to get agent performance metrics: {e}")
            return []
    
    def _calculate_agent_metrics(self, agent_name: str, records: List[Dict]) -> AgentPerformanceMetrics:
        """Calculate comprehensive metrics for a single agent."""
        
        # Basic statistics
        total_evaluations = len(records)
        evaluation_scores = [r.get("evaluation_score", 0) for r in records if r.get("evaluation_score") is not None]
        correctness_scores = [r.get("correctness_score", 0) for r in records if r.get("correctness_score") is not None]
        confidence_scores = [r.get("confidence_score", 0) for r in records if r.get("confidence_score") is not None]
        hallucination_scores = [r.get("hallucination_score", 0) for r in records if r.get("hallucination_score") is not None]
        execution_times = [r.get("execution_time_ms", 0) for r in records if r.get("execution_time_ms") is not None]
        
        # Calculate averages
        avg_evaluation_score = statistics.mean(evaluation_scores) if evaluation_scores else 0
        avg_correctness = statistics.mean(correctness_scores) if correctness_scores else 0
        avg_confidence = statistics.mean(confidence_scores) if confidence_scores else 0
        avg_hallucination_score = statistics.mean(hallucination_scores) if hallucination_scores else 0
        avg_execution_time_ms = statistics.mean(execution_times) if execution_times else 0
        
        # Memory utilization
        memory_used_count = sum(1 for r in records if r.get("memory_context_used", False))
        memory_utilization_rate = memory_used_count / total_evaluations if total_evaluations > 0 else 0
        
        # Success rate
        success_count = sum(1 for r in records if r.get("success", True))
        success_rate = success_count / total_evaluations if total_evaluations > 0 else 0
        error_count = total_evaluations - success_count
        
        # Performance grade
        performance_grade = self._calculate_performance_grade(avg_evaluation_score)
        
        # Trend analysis
        trend_direction = self._calculate_trend_direction(records)
        
        # Generate recommendations
        recommendations = self._generate_agent_recommendations(
            agent_name, avg_evaluation_score, avg_hallucination_score,
            memory_utilization_rate, success_rate, avg_execution_time_ms
        )
        
        return AgentPerformanceMetrics(
            agent_name=agent_name,
            total_evaluations=total_evaluations,
            avg_evaluation_score=avg_evaluation_score,
            avg_correctness=avg_correctness,
            avg_confidence=avg_confidence,
            avg_hallucination_score=avg_hallucination_score,
            memory_utilization_rate=memory_utilization_rate,
            avg_execution_time_ms=avg_execution_time_ms,
            success_rate=success_rate,
            error_count=error_count,
            performance_grade=performance_grade,
            trend_direction=trend_direction,
            recommendations=recommendations
        )
    
    async def _get_memory_system_metrics(self, report_date: datetime) -> MemorySystemMetrics:
        """Get comprehensive memory system metrics."""
        try:
            if not self.memory_manager:
                return MemorySystemMetrics(
                    total_memories=0, memories_created_today=0, avg_memory_relevance=0,
                    memory_hit_rate=0, top_memory_types=[], memory_consolidation_rate=0,
                    storage_efficiency=0, recommendations=["Memory manager not available"]
                )
            
            start_time = report_date.replace(hour=0, minute=0, second=0, microsecond=0)
            end_time = start_time + timedelta(days=1)
            
            # Get memory statistics
            memory_stats = self.memory_manager.get_memory_stats()
            
            # Query memory embeddings for today
            response = self.supabase.table("memory_embeddings")\
                .select("*")\
                .gte("created_at", start_time.isoformat())\
                .lt("created_at", end_time.isoformat())\
                .execute()
            
            memories_today = response.data or []
            
            # Calculate metrics
            total_memories = memory_stats.get("total_memories", 0)
            memories_created_today = len(memories_today)
            
            # Memory type distribution
            type_counts = {}
            for memory in memories_today:
                mem_type = memory.get("mem_type", "unknown")
                type_counts[mem_type] = type_counts.get(mem_type, 0) + 1
            
            top_memory_types = sorted(type_counts.items(), key=lambda x: x[1], reverse=True)[:5]
            
            # Calculate memory hit rate from agent metrics
            agent_response = self.supabase.table("agent_metrics")\
                .select("memory_context_used")\
                .gte("created_at", start_time.isoformat())\
                .lt("created_at", end_time.isoformat())\
                .execute()
            
            if agent_response.data:
                memory_used_count = sum(1 for r in agent_response.data if r.get("memory_context_used", False))
                memory_hit_rate = memory_used_count / len(agent_response.data)
            else:
                memory_hit_rate = 0
            
            # Generate recommendations
            recommendations = self._generate_memory_recommendations(
                total_memories, memories_created_today, memory_hit_rate
            )
            
            return MemorySystemMetrics(
                total_memories=total_memories,
                memories_created_today=memories_created_today,
                avg_memory_relevance=memory_stats.get("avg_relevance", 0),
                memory_hit_rate=memory_hit_rate,
                top_memory_types=top_memory_types,
                memory_consolidation_rate=memory_stats.get("consolidation_rate", 0),
                storage_efficiency=memory_stats.get("storage_efficiency", 0),
                recommendations=recommendations
            )
            
        except Exception as e:
            logger.error(f"Failed to get memory system metrics: {e}")
            return MemorySystemMetrics(
                total_memories=0, memories_created_today=0, avg_memory_relevance=0,
                memory_hit_rate=0, top_memory_types=[], memory_consolidation_rate=0,
                storage_efficiency=0, recommendations=[f"Error collecting memory metrics: {e}"]
            )

    async def _get_system_health_metrics(self, report_date: datetime) -> SystemHealthMetrics:
        """Get comprehensive system health metrics."""
        try:
            # Get Phoenix dashboard status
            dashboard_status = self.dashboard_manager.get_dashboard_status()

            # Calculate overall health score
            phoenix_healthy = dashboard_status.get("phoenix", {}).get("healthy", False)
            data_sources_healthy = all(
                ds.get("healthy", False)
                for ds in dashboard_status.get("data_sources", {}).values()
            )

            overall_health_score = 1.0 if phoenix_healthy and data_sources_healthy else 0.5

            # Get database performance metrics
            db_performance = {
                "query_latency_ms": 50,  # Would get from actual monitoring
                "connection_pool_usage": 0.6,
                "cache_hit_rate": 0.85
            }

            # Calculate error rate
            start_time = report_date.replace(hour=0, minute=0, second=0, microsecond=0)
            end_time = start_time + timedelta(days=1)

            error_response = self.supabase.table("agent_metrics")\
                .select("success")\
                .gte("created_at", start_time.isoformat())\
                .lt("created_at", end_time.isoformat())\
                .execute()

            if error_response.data:
                error_count = sum(1 for r in error_response.data if not r.get("success", True))
                error_rate = error_count / len(error_response.data)
            else:
                error_rate = 0

            # Generate recommendations
            recommendations = self._generate_system_recommendations(
                overall_health_score, error_rate, phoenix_healthy
            )

            return SystemHealthMetrics(
                overall_health_score=overall_health_score,
                phoenix_status="healthy" if phoenix_healthy else "unhealthy",
                database_performance=db_performance,
                trace_ingestion_rate=100.0,  # Would get from Phoenix metrics
                error_rate=error_rate,
                alert_count=0,  # Would get from alerting system
                uptime_percentage=99.9,  # Would calculate from monitoring
                recommendations=recommendations
            )

        except Exception as e:
            logger.error(f"Failed to get system health metrics: {e}")
            return SystemHealthMetrics(
                overall_health_score=0.0,
                phoenix_status="unknown",
                database_performance={},
                trace_ingestion_rate=0.0,
                error_rate=1.0,
                alert_count=0,
                uptime_percentage=0.0,
                recommendations=[f"Error collecting system metrics: {e}"]
            )

    async def _get_observability_metrics(self, report_date: datetime) -> Dict[str, Any]:
        """Get observability metrics from Phoenix and traces."""
        try:
            if not self.tracer:
                return {"error": "Tracer not available"}

            # Get trace statistics
            trace_stats = self.tracer.get_trace_stats()

            return {
                "total_traces": trace_stats.get("total_traces", 0),
                "avg_trace_duration": trace_stats.get("avg_duration_ms", 0),
                "trace_success_rate": trace_stats.get("success_rate", 0),
                "spans_per_trace": trace_stats.get("avg_spans_per_trace", 0),
                "top_operations": trace_stats.get("top_operations", []),
                "performance_percentiles": trace_stats.get("percentiles", {})
            }

        except Exception as e:
            logger.error(f"Failed to get observability metrics: {e}")
            return {"error": str(e)}

    async def _get_trading_specific_metrics(self, report_date: datetime) -> Dict[str, Any]:
        """Get trading-specific performance metrics."""
        try:
            start_time = report_date.replace(hour=0, minute=0, second=0, microsecond=0)
            end_time = start_time + timedelta(days=1)

            # Query trading-related evaluations
            response = self.supabase.table("agent_metrics")\
                .select("*")\
                .gte("created_at", start_time.isoformat())\
                .lt("created_at", end_time.isoformat())\
                .ilike("task_description", "%trading%")\
                .execute()

            trading_records = response.data or []

            if not trading_records:
                return {"message": "No trading-specific data available"}

            # Analyze by strategy type
            strategy_performance = {}
            symbol_performance = {}

            for record in trading_records:
                # Extract strategy from task description (simplified)
                task = record.get("task_description", "").lower()
                strategy = "unknown"
                if "covered call" in task:
                    strategy = "covered_call"
                elif "protective put" in task:
                    strategy = "protective_put"
                elif "iron condor" in task:
                    strategy = "iron_condor"

                if strategy not in strategy_performance:
                    strategy_performance[strategy] = []
                strategy_performance[strategy].append(record.get("evaluation_score", 0))

                # Extract symbol (simplified)
                for symbol in ["AAPL", "MSFT", "GOOGL", "TSLA", "SPY"]:
                    if symbol in task.upper():
                        if symbol not in symbol_performance:
                            symbol_performance[symbol] = []
                        symbol_performance[symbol].append(record.get("evaluation_score", 0))
                        break

            # Calculate averages
            strategy_avg = {
                strategy: statistics.mean(scores) if scores else 0
                for strategy, scores in strategy_performance.items()
            }

            symbol_avg = {
                symbol: statistics.mean(scores) if scores else 0
                for symbol, scores in symbol_performance.items()
            }

            return {
                "total_trading_operations": len(trading_records),
                "strategy_performance": strategy_avg,
                "symbol_performance": symbol_avg,
                "avg_trading_score": statistics.mean([r.get("evaluation_score", 0) for r in trading_records]),
                "market_session_analysis": self._analyze_market_sessions(trading_records)
            }

        except Exception as e:
            logger.error(f"Failed to get trading-specific metrics: {e}")
            return {"error": str(e)}

    def _analyze_market_sessions(self, records: List[Dict]) -> Dict[str, Any]:
        """Analyze performance by market session."""
        market_hours = []
        after_hours = []

        for record in records:
            created_at = datetime.fromisoformat(record["created_at"].replace("Z", "+00:00"))
            hour = created_at.hour

            score = record.get("evaluation_score", 0)
            if 9 <= hour <= 16:  # Market hours (simplified)
                market_hours.append(score)
            else:
                after_hours.append(score)

        return {
            "market_hours": {
                "count": len(market_hours),
                "avg_score": statistics.mean(market_hours) if market_hours else 0
            },
            "after_hours": {
                "count": len(after_hours),
                "avg_score": statistics.mean(after_hours) if after_hours else 0
            }
        }

    def _generate_executive_summary(self, agent_metrics: List[AgentPerformanceMetrics],
                                  memory_metrics: MemorySystemMetrics,
                                  health_metrics: SystemHealthMetrics) -> Dict[str, Any]:
        """Generate executive summary of system performance."""

        if not agent_metrics:
            return {
                "status": "No agent data available",
                "key_insights": [],
                "critical_issues": ["Insufficient data for analysis"]
            }

        # Calculate overall metrics
        total_evaluations = sum(m.total_evaluations for m in agent_metrics)
        avg_system_score = statistics.mean([m.avg_evaluation_score for m in agent_metrics])
        avg_memory_utilization = statistics.mean([m.memory_utilization_rate for m in agent_metrics])

        # Identify top and bottom performers
        top_performer = max(agent_metrics, key=lambda x: x.avg_evaluation_score)
        bottom_performer = min(agent_metrics, key=lambda x: x.avg_evaluation_score)

        # Generate key insights
        key_insights = [
            f"System processed {total_evaluations} evaluations with {avg_system_score:.1%} average performance",
            f"Memory utilization rate: {avg_memory_utilization:.1%}",
            f"Top performer: {top_performer.agent_name} ({top_performer.avg_evaluation_score:.1%})",
            f"System health score: {health_metrics.overall_health_score:.1%}"
        ]

        # Identify critical issues
        critical_issues = []
        if avg_system_score < 0.7:
            critical_issues.append("System performance below acceptable threshold")
        if health_metrics.error_rate > 0.1:
            critical_issues.append("High error rate detected")
        if avg_memory_utilization < 0.5:
            critical_issues.append("Low memory system utilization")

        return {
            "status": "healthy" if not critical_issues else "attention_required",
            "total_evaluations": total_evaluations,
            "avg_system_score": avg_system_score,
            "top_performer": top_performer.agent_name,
            "key_insights": key_insights,
            "critical_issues": critical_issues,
            "recommendation_count": sum(len(m.recommendations) for m in agent_metrics)
        }

    def _generate_comprehensive_recommendations(self, agent_metrics: List[AgentPerformanceMetrics],
                                              memory_metrics: MemorySystemMetrics,
                                              health_metrics: SystemHealthMetrics,
                                              trading_metrics: Dict[str, Any]) -> Dict[str, List[str]]:
        """Generate comprehensive recommendations across all system areas."""

        recommendations = {
            "immediate_actions": [],
            "performance_optimization": [],
            "memory_system": [],
            "system_health": [],
            "trading_strategy": []
        }

        # Immediate actions
        if health_metrics.error_rate > 0.1:
            recommendations["immediate_actions"].append("Investigate and resolve high error rate")

        if any(m.avg_hallucination_score > 0.3 for m in agent_metrics):
            recommendations["immediate_actions"].append("Review agents with high hallucination scores")

        # Performance optimization
        slow_agents = [m for m in agent_metrics if m.avg_execution_time_ms > 10000]
        if slow_agents:
            recommendations["performance_optimization"].append(
                f"Optimize performance for slow agents: {', '.join(m.agent_name for m in slow_agents)}"
            )

        # Memory system recommendations
        if memory_metrics.memory_hit_rate < 0.5:
            recommendations["memory_system"].append("Improve memory utilization - consider tuning search parameters")

        if memory_metrics.memories_created_today < 10:
            recommendations["memory_system"].append("Low memory creation rate - review memory storage triggers")

        # System health recommendations
        if health_metrics.overall_health_score < 0.8:
            recommendations["system_health"].append("System health below optimal - review infrastructure")

        # Trading strategy recommendations
        if trading_metrics and "strategy_performance" in trading_metrics:
            poor_strategies = [
                strategy for strategy, score in trading_metrics["strategy_performance"].items()
                if score < 0.6
            ]
            if poor_strategies:
                recommendations["trading_strategy"].append(
                    f"Review underperforming strategies: {', '.join(poor_strategies)}"
                )

        return recommendations

    def _generate_action_items(self, recommendations: Dict[str, List[str]]) -> List[Dict[str, Any]]:
        """Generate prioritized action items from recommendations."""

        action_items = []

        # High priority - immediate actions
        for i, action in enumerate(recommendations.get("immediate_actions", [])):
            action_items.append({
                "id": f"IMM-{i+1:03d}",
                "priority": "HIGH",
                "category": "Immediate Action",
                "description": action,
                "estimated_effort": "1-2 hours",
                "owner": "DevOps Team"
            })

        # Medium priority - performance and system health
        for category in ["performance_optimization", "system_health"]:
            for i, action in enumerate(recommendations.get(category, [])):
                action_items.append({
                    "id": f"{category.upper()[:3]}-{i+1:03d}",
                    "priority": "MEDIUM",
                    "category": category.replace("_", " ").title(),
                    "description": action,
                    "estimated_effort": "4-8 hours",
                    "owner": "Engineering Team"
                })

        # Lower priority - optimization and strategy
        for category in ["memory_system", "trading_strategy"]:
            for i, action in enumerate(recommendations.get(category, [])):
                action_items.append({
                    "id": f"{category.upper()[:3]}-{i+1:03d}",
                    "priority": "LOW",
                    "category": category.replace("_", " ").title(),
                    "description": action,
                    "estimated_effort": "1-3 days",
                    "owner": "Product Team"
                })

        return action_items

    async def _calculate_trend_analysis(self, report_date: datetime) -> Dict[str, Any]:
        """Calculate trend analysis over the past week."""
        try:
            end_date = report_date
            start_date = end_date - timedelta(days=self.config["trend_analysis_days"])

            # Get daily metrics for trend analysis
            daily_metrics = []
            current_date = start_date

            while current_date <= end_date:
                day_start = current_date.replace(hour=0, minute=0, second=0, microsecond=0)
                day_end = day_start + timedelta(days=1)

                response = self.supabase.table("agent_metrics")\
                    .select("evaluation_score, memory_context_used, success")\
                    .gte("created_at", day_start.isoformat())\
                    .lt("created_at", day_end.isoformat())\
                    .execute()

                if response.data:
                    day_data = response.data
                    daily_metrics.append({
                        "date": current_date.date().isoformat(),
                        "avg_score": statistics.mean([r.get("evaluation_score", 0) for r in day_data]),
                        "memory_usage": sum(1 for r in day_data if r.get("memory_context_used", False)) / len(day_data),
                        "success_rate": sum(1 for r in day_data if r.get("success", True)) / len(day_data),
                        "total_evaluations": len(day_data)
                    })

                current_date += timedelta(days=1)

            if len(daily_metrics) < 2:
                return {"error": "Insufficient data for trend analysis"}

            # Calculate trends
            scores = [d["avg_score"] for d in daily_metrics]
            memory_usage = [d["memory_usage"] for d in daily_metrics]
            success_rates = [d["success_rate"] for d in daily_metrics]

            return {
                "period_days": self.config["trend_analysis_days"],
                "daily_metrics": daily_metrics,
                "trends": {
                    "evaluation_score": self._calculate_trend_direction_from_values(scores),
                    "memory_usage": self._calculate_trend_direction_from_values(memory_usage),
                    "success_rate": self._calculate_trend_direction_from_values(success_rates)
                },
                "volatility": {
                    "evaluation_score": statistics.stdev(scores) if len(scores) > 1 else 0,
                    "memory_usage": statistics.stdev(memory_usage) if len(memory_usage) > 1 else 0
                }
            }

        except Exception as e:
            logger.error(f"Failed to calculate trend analysis: {e}")
            return {"error": str(e)}

    def _calculate_trend_direction_from_values(self, values: List[float]) -> str:
        """Calculate trend direction from a list of values."""
        if len(values) < 2:
            return "stable"

        # Simple linear trend calculation
        first_half = statistics.mean(values[:len(values)//2])
        second_half = statistics.mean(values[len(values)//2:])

        diff = second_half - first_half
        threshold = 0.05  # 5% change threshold

        if diff > threshold:
            return "improving"
        elif diff < -threshold:
            return "declining"
        else:
            return "stable"

    def _calculate_performance_grade(self, score: float) -> str:
        """Calculate performance grade from evaluation score."""
        thresholds = self.config["performance_thresholds"]

        if score >= thresholds["excellent"]:
            return "A"
        elif score >= thresholds["good"]:
            return "B"
        elif score >= thresholds["fair"]:
            return "C"
        elif score >= thresholds["poor"]:
            return "D"
        else:
            return "F"

    def _calculate_trend_direction(self, records: List[Dict]) -> str:
        """Calculate trend direction from evaluation records."""
        if len(records) < 4:
            return "stable"

        # Sort by timestamp
        sorted_records = sorted(records, key=lambda x: x["created_at"])

        # Split into first and second half
        mid_point = len(sorted_records) // 2
        first_half = sorted_records[:mid_point]
        second_half = sorted_records[mid_point:]

        # Calculate average scores
        first_avg = statistics.mean([r.get("evaluation_score", 0) for r in first_half])
        second_avg = statistics.mean([r.get("evaluation_score", 0) for r in second_half])

        diff = second_avg - first_avg
        threshold = 0.05

        if diff > threshold:
            return "improving"
        elif diff < -threshold:
            return "declining"
        else:
            return "stable"

    def _generate_agent_recommendations(self, agent_name: str, avg_score: float,
                                      hallucination_score: float, memory_usage: float,
                                      success_rate: float, avg_latency: float) -> List[str]:
        """Generate specific recommendations for an agent."""
        recommendations = []

        if avg_score < 0.7:
            recommendations.append("Review and improve evaluation criteria and training data")

        if hallucination_score > 0.3:
            recommendations.append("Implement additional hallucination detection and prevention measures")

        if memory_usage < 0.5:
            recommendations.append("Increase memory context utilization for better performance")

        if success_rate < 0.9:
            recommendations.append("Investigate and resolve error patterns")

        if avg_latency > 10000:
            recommendations.append("Optimize execution performance to reduce latency")

        if not recommendations:
            recommendations.append("Performance is satisfactory - continue monitoring")

        return recommendations

    def _generate_memory_recommendations(self, total_memories: int, daily_created: int,
                                       hit_rate: float) -> List[str]:
        """Generate memory system recommendations."""
        recommendations = []

        if hit_rate < 0.5:
            recommendations.append("Improve memory search relevance and context matching")

        if daily_created < 10:
            recommendations.append("Review memory creation triggers - may be too restrictive")

        if total_memories > 100000:
            recommendations.append("Consider implementing memory consolidation and archiving")

        if not recommendations:
            recommendations.append("Memory system performance is optimal")

        return recommendations

    def _generate_system_recommendations(self, health_score: float, error_rate: float,
                                       phoenix_healthy: bool) -> List[str]:
        """Generate system health recommendations."""
        recommendations = []

        if not phoenix_healthy:
            recommendations.append("Investigate Phoenix observability system issues")

        if error_rate > 0.1:
            recommendations.append("High error rate detected - review system logs and error patterns")

        if health_score < 0.8:
            recommendations.append("Overall system health below optimal - comprehensive review needed")

        if not recommendations:
            recommendations.append("System health is excellent")

        return recommendations

    async def _save_report(self, report: Dict[str, Any], report_date: datetime):
        """Save report to database and file system."""
        try:
            # Save to database
            report_record = {
                "report_date": report_date.date().isoformat(),
                "report_data": json.dumps(report),
                "status": report["executive_summary"].get("status", "unknown"),
                "total_evaluations": report["executive_summary"].get("total_evaluations", 0),
                "avg_system_score": report["executive_summary"].get("avg_system_score", 0),
                "created_at": datetime.now().isoformat()
            }

            # Create reports table if it doesn't exist (would be in migration)
            # For now, just log the save operation
            logger.info(f"Report saved for {report_date.date()}")

            # Save to file system
            report_filename = f"reports/nightly_report_{report_date.strftime('%Y%m%d')}.json"
            import os
            os.makedirs("reports", exist_ok=True)

            with open(report_filename, 'w') as f:
                json.dump(report, f, indent=2, default=str)

            logger.info(f"Report file saved: {report_filename}")

        except Exception as e:
            logger.error(f"Failed to save report: {e}")

    async def _send_report_notification(self, report: Dict[str, Any]):
        """Send report notification via email."""
        try:
            if not self.config["email_enabled"] or not self.config["email_recipients"]:
                return

            # Generate email content
            subject = f"Trading Agents Nightly Report - {report['report_metadata']['report_date']}"

            # Create summary for email
            summary = report["executive_summary"]
            body = f"""
            Trading Agents System Report

            Status: {summary.get('status', 'Unknown')}
            Total Evaluations: {summary.get('total_evaluations', 0)}
            Average System Score: {summary.get('avg_system_score', 0):.1%}

            Key Insights:
            {chr(10).join('- ' + insight for insight in summary.get('key_insights', []))}

            Critical Issues:
            {chr(10).join('- ' + issue for issue in summary.get('critical_issues', [])) if summary.get('critical_issues') else 'None'}

            Full report attached.
            """

            # Send email (implementation would depend on email service)
            logger.info("Report notification sent successfully")

        except Exception as e:
            logger.error(f"Failed to send report notification: {e}")

    def _get_methodology_notes(self) -> Dict[str, str]:
        """Get methodology notes for the report."""
        return {
            "evaluation_scoring": "Evaluation scores are calculated using weighted metrics including correctness, confidence, and context relevance",
            "memory_analytics": "Memory utilization is measured by the percentage of evaluations that used memory context",
            "trend_analysis": "Trends are calculated by comparing first and second half averages over the analysis period",
            "performance_grading": "Performance grades use thresholds: A (90%+), B (80%+), C (60%+), D (40%+), F (<40%)"
        }

    def _get_data_sources_info(self) -> Dict[str, str]:
        """Get data sources information."""
        return {
            "agent_metrics": "Supabase table containing evaluation results and performance data",
            "memory_embeddings": "Supabase table containing memory system data",
            "phoenix_traces": "Phoenix PostgreSQL database containing trace and observability data",
            "agent_health": "Supabase table containing agent health monitoring data"
        }

    def _get_glossary(self) -> Dict[str, str]:
        """Get glossary of terms used in the report."""
        return {
            "evaluation_score": "Overall performance score combining multiple evaluation metrics",
            "hallucination_score": "Measure of inconsistency or false information in agent outputs",
            "memory_utilization": "Percentage of operations that successfully used memory context",
            "context_relevance": "Measure of how relevant retrieved memory context was to the task",
            "trace_ingestion_rate": "Number of observability traces processed per unit time",
            "performance_grade": "Letter grade (A-F) representing overall agent performance"
        }


# Utility functions for external use
async def generate_nightly_report(report_date: Optional[datetime] = None) -> Dict[str, Any]:
    """Generate nightly report with default configuration."""
    reporter = EnhancedEvalReporter()
    return await reporter.generate_comprehensive_report(report_date)

def create_enhanced_reporter(memory_manager: Optional[MemoryManager] = None,
                           tracer: Optional[AgentTracer] = None) -> EnhancedEvalReporter:
    """Create enhanced evaluation reporter instance."""
    return EnhancedEvalReporter(memory_manager, tracer)
