"""
Enhanced Native smolagents + E2B Integration

This module provides health-aware smolagents agents that use
smolagents' built-in E2B support with added health monitoring, audit logging,
memory capabilities, and comprehensive observability.
"""

import logging
import time
from typing import Any, Dict, List, Optional

from smolagents import CodeAgent, InferenceClientModel

from wheel_trader.exec_manager_e2b import log_execution_audit

from .agent_health import AgentHealthManager
from .memory.manager import MemoryManager
from .observability import ObservabilityManager
from .observability.tracer import trace_execution

# Configure logging
logger = logging.getLogger(__name__)


class HealthAwareAgent:
    """
    Enhanced wrapper for smolagents CodeAgent with health monitoring,
    memory capabilities, and comprehensive observability.
    Uses native smolagents E2B support with added intelligence features.
    """

    def __init__(self, name: str, model=None, tools=None,
                 health_threshold: float = 0.7,
                 memory_backend: str = "pgvector",
                 memory_config: Optional[Dict] = None,
                 enable_observability: bool = True):
        """
        Initialize enhanced health-aware agent

        Args:
            name: Agent name
            model: Language model (defaults to InferenceClientModel)
            tools: List of tools for the agent
            health_threshold: Health threshold for execution gating
            memory_backend: Memory backend type ("pgvector" or "mem0")
            memory_config: Backend-specific memory configuration
            enable_observability: Whether to enable observability features
        """
        self.name = name
        self.health_manager = AgentHealthManager(health_threshold)

        # Initialize memory system
        try:
            self.memory_manager = MemoryManager(
                backend=memory_backend,
                config=memory_config or {},
                agent_name=name,
                health_manager=self.health_manager
            )
            logger.info(f"HealthAwareAgent: Memory system initialized for {name}")
        except Exception as e:
            logger.warning(f"HealthAwareAgent: Memory system failed to initialize: {e}")
            self.memory_manager = None

        # Initialize observability system
        self.observability_manager = None
        if enable_observability:
            try:
                self.observability_manager = ObservabilityManager(
                    agent_name=name,
                    memory_manager=self.memory_manager
                )
                logger.info(f"HealthAwareAgent: Observability initialized for {name}")
            except Exception as e:
                logger.warning(f"HealthAwareAgent: Observability failed to initialize: {e}")

        # Use default model if none provided
        if model is None:
            model = InferenceClientModel()

        # Create agent with native E2B support
        self.agent = CodeAgent(
            model=model,
            tools=tools or [],
            executor_type="e2b",
            add_base_tools=False
        )

    @trace_execution(operation_name="agent_execution")
    def run(self, task: str, **kwargs) -> Any:
        """
        Enhanced run method with health checking, memory integration,
        and comprehensive observability.
        """
        # Check agent health before execution
        if not self.health_manager.is_agent_healthy(self.name):
            health_metrics = self.health_manager.get_health_metrics(self.name)
            health_score = health_metrics.health_score if health_metrics else 0.0
            error_msg = f"Agent '{self.name}' is unhealthy (score: {health_score:.2f})"
            self._log_blocked_execution(self.name, error_msg)
            raise RuntimeError(error_msg)

        # Retrieve relevant memories if memory system is available
        memories = []
        if self.memory_manager:
            try:
                memories = self.memory_manager.search_memories(
                    query=task,
                    limit=5,
                    filters={
                        "mem_type": ["agent_action", "market_analysis", "execution_result"],
                        "min_importance": 0.6
                    }
                )
                logger.debug(f"HealthAwareAgent: Retrieved {len(memories)} relevant memories")
            except Exception as e:
                logger.warning(f"HealthAwareAgent: Memory retrieval failed: {e}")

        # Enhance task with memory context if available
        enhanced_task = self._enhance_task_with_memories(task, memories)

        start_time = time.time()

        # Execute with observability if available
        if self.observability_manager:
            with self.observability_manager.trace_with_memory("agent_execution"):
                return self._execute_with_memory_storage(enhanced_task, memories, start_time, **kwargs)
        else:
            return self._execute_basic(enhanced_task, start_time, **kwargs)

    def _enhance_task_with_memories(self, task: str, memories: List[Dict]) -> str:
        """Enhance task description with relevant memory context"""
        if not memories:
            return task

        # Create memory context from top 3 most relevant memories
        memory_context = "\n".join([
            f"- {mem.get('content', '')[:100]}..."
            for mem in memories[:3]
        ])

        return f"""
Task: {task}

Relevant past experiences:
{memory_context}

Please consider this context when executing the task.
"""

    def _execute_with_memory_storage(self, task: str, memories: List[Dict],
                                   start_time: float, **kwargs) -> Any:
        """Execute task with memory storage of results"""
        try:
            # Use context manager for automatic E2B cleanup
            with self.agent:
                result = self.agent.run(task, **kwargs)

            # Store successful execution in memory
            if self.memory_manager:
                self._store_execution_memory(task, result, memories, start_time, True)

            # Update health after successful execution
            self.health_manager.update_agent_health(self.name)

            return result

        except Exception as e:
            # Store error context in memory
            if self.memory_manager:
                self._store_execution_memory(task, str(e), memories, start_time, False)

            # Update health after failed execution
            self.health_manager.update_agent_health(self.name)
            raise

    def _execute_basic(self, task: str, start_time: float, **kwargs) -> Any:
        """Basic execution without memory storage (fallback)"""
        try:
            # Use context manager for automatic E2B cleanup
            with self.agent:
                result = self.agent.run(task, **kwargs)

            # Update health after successful execution
            self.health_manager.update_agent_health(self.name)

            return result

        except Exception:
            # Update health after failed execution
            self.health_manager.update_agent_health(self.name)
            raise

    def _store_execution_memory(self, task: str, result: Any,
                              memories: List[Dict], start_time: float,
                              success: bool):
        """Store execution result as memory"""
        try:
            execution_time = int((time.time() - start_time) * 1000)

            # Create memory content
            status = "SUCCESS" if success else "FAILED"
            content = f"Task: {task} | Status: {status} | Duration: {execution_time}ms"

            if success:
                # Add result summary (truncated for readability)
                result_str = str(result)[:200] if result else "No output"
                content += f" | Result: {result_str}"
            else:
                content += f" | Error: {result}"

            # Prepare metadata
            metadata = {
                "task": task,
                "execution_time_ms": execution_time,
                "success": success,
                "related_memories": [mem.get("id") for mem in memories if mem.get("id")],
                "result_type": type(result).__name__ if result else None
            }

            # Determine importance based on success and performance
            importance = 0.7 if success else 0.8  # Failures are more important for learning
            if execution_time > 5000:  # Slow executions are important
                importance += 0.1
            importance = min(1.0, importance)

            # Store memory
            self.memory_manager.store_memory(
                content=content,
                metadata=metadata,
                mem_type="execution_result",
                importance=importance,
                tags=["execution", "agent_action"]
            )

        except Exception as e:
            logger.warning(f"HealthAwareAgent: Failed to store execution memory: {e}")

    def _log_blocked_execution(self, agent_name: str, error_msg: str):
        """Log when execution is blocked due to health issues"""
        log_execution_audit(
            container_id="blocked",
            agent_name=agent_name,
            job_name=self.name,
            cpu_ms=0,
            mem_peak_mb=0,
            duration_ms=0,
            exit_code=-2,
            success=False,
            error_message=error_msg
        )

    def get_health_status(self) -> Dict[str, Any]:
        """Get comprehensive health status including memory and observability"""
        metrics = self.health_manager.get_health_metrics(self.name)

        base_status = {
            "agent_name": self.name,
            "health_score": metrics.health_score if metrics else 1.0,
            "total_runs": metrics.total_runs if metrics else 0,
            "success_rate": metrics.success_rate if metrics else 1.0,
            "avg_duration_ms": metrics.avg_duration_ms if metrics else 0,
            "is_healthy": (metrics.health_score >= self.health_manager.health_threshold) if metrics else True,
            "status": "active" if metrics else "new_agent"
        }

        # Add memory system status
        if self.memory_manager:
            try:
                memory_health = self.memory_manager.get_health_status()
                base_status["memory_system"] = memory_health
            except Exception as e:
                base_status["memory_system"] = {"error": str(e), "healthy": False}
        else:
            base_status["memory_system"] = {"enabled": False}

        # Add observability status
        if self.observability_manager:
            try:
                obs_stats = self.observability_manager.get_observability_stats()
                base_status["observability"] = obs_stats
            except Exception as e:
                base_status["observability"] = {"error": str(e), "healthy": False}
        else:
            base_status["observability"] = {"enabled": False}

        return base_status

    def get_all_agent_health(self) -> Dict[str, Any]:
        """Get health status of all agents"""
        return self.health_manager.get_all_agent_health()

    def search_memories(self, query: str, limit: int = 5,
                       filters: Optional[Dict] = None) -> List[Dict]:
        """Search agent memories"""
        if not self.memory_manager:
            logger.warning("HealthAwareAgent: Memory system not available")
            return []

        try:
            return self.memory_manager.search_memories(
                query=query,
                limit=limit,
                filters=filters
            )
        except Exception as e:
            logger.error(f"HealthAwareAgent: Memory search failed: {e}")
            return []

    def store_memory(self, content: str, metadata: Optional[Dict] = None,
                    mem_type: str = "agent_action", importance: float = 0.5) -> Optional[str]:
        """Store a memory"""
        if not self.memory_manager:
            logger.warning("HealthAwareAgent: Memory system not available")
            return None

        try:
            return self.memory_manager.store_memory(
                content=content,
                metadata=metadata or {},
                mem_type=mem_type,
                importance=importance
            )
        except Exception as e:
            logger.error(f"HealthAwareAgent: Memory storage failed: {e}")
            return None

    def get_memory_stats(self) -> Dict[str, Any]:
        """Get memory usage statistics"""
        if not self.memory_manager:
            return {"enabled": False}

        try:
            return self.memory_manager.get_memory_stats()
        except Exception as e:
            return {"error": str(e), "enabled": True}

    def consolidate_memories(self, similarity_threshold: float = 0.95) -> int:
        """Consolidate similar memories to reduce redundancy"""
        if not self.memory_manager:
            logger.warning("HealthAwareAgent: Memory system not available")
            return 0

        try:
            return self.memory_manager.consolidate_memories(
                similarity_threshold=similarity_threshold
            )
        except Exception as e:
            logger.error(f"HealthAwareAgent: Memory consolidation failed: {e}")
            return 0
