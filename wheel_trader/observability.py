"""
Enhanced Observability Module for Options Trading Agents

Comprehensive observability system with Phoenix tracing, memory integration,
and financial market monitoring capabilities.
"""

import logging
import os
import time
from datetime import datetime
from functools import wraps
from typing import Any, Dict, Optional

# Configure logging
logger = logging.getLogger(__name__)

# Phoenix observability with proper OTEL setup
try:
    from opentelemetry import trace
    from opentelemetry.trace import Status, StatusCode

    from phoenix.otel import register

    # Configure Phoenix OTEL with environment-based settings
    tracer_provider = register(
        project_name=os.getenv("PHOENIX_PROJECT_NAME", "coveredcalls-agents"),
        auto_instrument=True,
        batch=True,
        endpoint=os.getenv("PHOENIX_COLLECTOR_ENDPOINT", "http://localhost:6006/v1/traces")
    )

    # Get tracer for manual instrumentation
    tracer = tracer_provider.get_tracer(__name__)
    PHOENIX_AVAILABLE = True

    logger.info("Phoenix OTEL configured successfully with auto-instrumentation enabled")

except (ImportError, Exception) as e:
    logger.warning(f"Phoenix OTEL setup failed, using fallback: {e}")
    PHOENIX_AVAILABLE = False
    tracer = None

    def trace(*args, **kwargs):
        """Fallback trace decorator that does nothing"""
        def decorator(func):
            return func
        return decorator if args else decorator

# Import local modules after Phoenix setup
try:
    from .memory_tool import save_memory
except ImportError as e:
    logger.warning(f"Failed to import memory_tool: {e}")
    save_memory = None

try:
    from .observability.memory_bridge import MemoryObservabilityBridge
except ImportError as e:
    logger.warning(f"Failed to import MemoryObservabilityBridge: {e}")
    MemoryObservabilityBridge = None

try:
    from .observability.tracer import AgentTracer
except ImportError as e:
    logger.warning(f"Failed to import AgentTracer: {e}")
    AgentTracer = None


class ObservabilityManager:
    """
    Centralized observability management for trading agents
    Integrates Phoenix tracing with memory storage and health monitoring
    """

    def __init__(self, agent_name: str, memory_manager=None):
        """
        Initialize observability manager

        Args:
            agent_name: Name of the agent
            memory_manager: Optional memory manager for enhanced observability
        """
        self.agent_name = agent_name
        self.memory_manager = memory_manager

        # Initialize tracer
        if AgentTracer:
            self.tracer = AgentTracer(agent_name)
        else:
            self.tracer = None
            logger.warning(f"AgentTracer not available for {agent_name}")

        # Initialize memory bridge if memory manager is available
        self.memory_bridge = None
        if memory_manager and self.tracer and MemoryObservabilityBridge:
            self.memory_bridge = MemoryObservabilityBridge(
                memory_manager, self.tracer
            )

        # Observability statistics
        self.stats = {
            "sessions_created": 0,
            "traces_completed": 0,
            "memory_integrations": 0,
            "errors_tracked": 0,
            "start_time": datetime.now().isoformat()
        }

        logger.info(
            f"ObservabilityManager: Initialized for agent {agent_name}"
        )

    def start_session(self, session_name: Optional[str] = None) -> bool:
        """
        Start a new observability session

        Args:
            session_name: Optional custom session name

        Returns:
            True if session started successfully
        """
        try:
            session_name = (
                session_name or f"{self.agent_name}_session_{int(time.time())}"
            )

            # With Phoenix OTEL, sessions are managed automatically
            # Just log the session start for tracking
            if PHOENIX_AVAILABLE:
                logger.info(
                    f"ObservabilityManager: Phoenix session active for {session_name}"
                )
            else:
                logger.info(
                    f"ObservabilityManager: Fallback session for {session_name}"
                )

            self.stats["sessions_created"] += 1
            logger.info(
                f"ObservabilityManager: Started session {session_name}"
            )
            return True

        except Exception as e:
            logger.error(f"ObservabilityManager: Failed to start session: {e}")
            return False

    def trace_agent_operation(self, operation_name: str, metadata: Optional[Dict] = None):
        """
        Context manager for tracing agent operations with memory integration

        Args:
            operation_name: Name of the operation
            metadata: Additional metadata
        """
        if self.tracer:
            return self.tracer.trace_execution(operation_name, metadata)
        else:
            # Return a fallback context manager with mock span
            from contextlib import contextmanager

            @contextmanager
            def mock_trace():
                class MockSpan:
                    def set_attribute(self, key, value):
                        pass
                    def set_status(self, status):
                        pass
                    def record_exception(self, exception):
                        pass

                yield MockSpan()

            return mock_trace()

    def trace_with_memory(self, operation_name: str, store_result: bool = True):
        """
        Context manager for tracing with automatic memory storage

        Args:
            operation_name: Name of the operation
            store_result: Whether to store the result in memory
        """
        class TracingContext:
            def __init__(self, manager, op_name, store_mem):
                self.manager = manager
                self.op_name = op_name
                self.store_memory = store_mem
                self.span = None
                self.start_time = None

            def __enter__(self):
                self.start_time = time.time()
                self.span = self.manager.tracer.trace_execution(self.op_name)
                return self.span.__enter__()

            def __exit__(self, exc_type, exc_val, exc_tb):
                try:
                    execution_time = int((time.time() - self.start_time) * 1000)

                    if self.store_memory and self.manager.memory_bridge:
                        if exc_type is None:
                            # Store successful execution
                            self.manager.memory_bridge.store_execution_result(
                                operation=self.op_name,
                                result="Operation completed successfully",
                                execution_time_ms=execution_time,
                                success=True
                            )
                        else:
                            # Store error context
                            self.manager.memory_bridge.store_error_context(
                                operation=self.op_name,
                                error=exc_val,
                                context={"execution_time_ms": execution_time}
                            )

                    self.manager.stats["traces_completed"] += 1

                finally:
                    return self.span.__exit__(exc_type, exc_val, exc_tb)

        return TracingContext(self, operation_name, store_result)

    def get_observability_stats(self) -> Dict[str, Any]:
        """Get comprehensive observability statistics"""
        stats = {
            "agent_name": self.agent_name,
            "manager_stats": self.stats.copy(),
            "tracer_stats": self.tracer.get_trace_stats() if self.tracer else {},
            "memory_bridge_enabled": self.memory_bridge is not None
        }

        if self.memory_bridge:
            stats["memory_bridge_stats"] = self.memory_bridge.get_bridge_stats()

        return stats


def trace_and_remember(func):
    """
    Enhanced decorator for tracing with memory integration
    Maintains backward compatibility while adding new features
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        # Get observability manager from self if available
        obs_manager = None
        if args and hasattr(args[0], 'observability_manager'):
            obs_manager = args[0].observability_manager

        if obs_manager:
            # Use enhanced tracing with memory integration
            with obs_manager.trace_with_memory(func.__name__):
                result = func(*args, **kwargs)
                return result
        else:
            # Fallback to basic Phoenix tracing
            with trace(f"{func.__module__}.{func.__name__}") as span:
                result = func(*args, **kwargs)
                # Try to save memory using the old method
                try:
                    save_memory(span.context.trace_id, result)
                except Exception as e:
                    logger.warning(f"Failed to save memory in trace_and_remember: {e}")
                return result

    return wrapper
