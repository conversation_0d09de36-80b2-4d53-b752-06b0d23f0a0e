"""
Advanced Monitoring & Alerts System

This module provides comprehensive monitoring, alerting, and performance
optimization for the options trading agents system with memory integration.
"""

import asyncio
import logging
import statistics
import time
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional

from supabase import Client, create_client

from wheel_trader import config
from wheel_trader.memory.manager import MemoryManager
from wheel_trader.observability.tracer import AgentTracer
from wheel_trader.phoenix_dashboard import PhoenixDashboardManager

# Configure logging
logger = logging.getLogger(__name__)


class AlertSeverity(Enum):
    """Alert severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AlertStatus(Enum):
    """Alert status."""
    ACTIVE = "active"
    RESOLVED = "resolved"
    ACKNOWLEDGED = "acknowledged"
    SUPPRESSED = "suppressed"


@dataclass
class Alert:
    """Alert data structure."""
    id: str
    name: str
    description: str
    severity: AlertSeverity
    status: AlertStatus
    triggered_at: datetime
    resolved_at: Optional[datetime]
    agent_name: Optional[str]
    metric_name: str
    metric_value: float
    threshold: float
    metadata: Dict[str, Any]


@dataclass
class MonitoringRule:
    """Monitoring rule configuration."""
    name: str
    description: str
    metric_query: str
    threshold: float
    comparison: str  # 'gt', 'lt', 'eq', 'gte', 'lte'
    severity: AlertSeverity
    evaluation_window: int  # seconds
    cooldown_period: int  # seconds
    enabled: bool
    agent_filter: Optional[str]
    notification_channels: List[str]


class AdvancedMonitoringSystem:
    """
    Advanced monitoring system with intelligent alerting and performance optimization.
    
    Features:
    - Real-time metric monitoring
    - Intelligent alerting with suppression
    - Performance trend analysis
    - Automated optimization recommendations
    - Memory-aware monitoring
    - Trading-specific metrics
    """
    
    def __init__(self, memory_manager: Optional[MemoryManager] = None,
                 tracer: Optional[AgentTracer] = None,
                 dashboard_manager: Optional[PhoenixDashboardManager] = None):
        """Initialize the advanced monitoring system."""
        self.memory_manager = memory_manager
        self.tracer = tracer
        self.dashboard_manager = dashboard_manager or PhoenixDashboardManager()
        self.supabase: Client = create_client(config.SUPABASE_URL, config.SUPABASE_KEY)
        
        # Monitoring state
        self.active_alerts: Dict[str, Alert] = {}
        self.monitoring_rules: List[MonitoringRule] = []
        self.last_evaluation_times: Dict[str, datetime] = {}
        self.performance_baselines: Dict[str, float] = {}
        
        # Configuration
        self.config = {
            "monitoring_interval": 30,  # seconds
            "alert_retention_days": 30,
            "performance_baseline_days": 7,
            "enable_auto_optimization": True,
            "notification_enabled": True,
            "max_alerts_per_rule": 5
        }
        
        # Initialize default monitoring rules
        self._initialize_default_rules()
        
        # Start monitoring loop
        self._monitoring_task = None
        self._running = False
    
    def _initialize_default_rules(self):
        """Initialize default monitoring rules."""
        default_rules = [
            MonitoringRule(
                name="high_evaluation_score_drop",
                description="Alert when evaluation scores drop significantly",
                metric_query="avg_evaluation_score",
                threshold=0.7,
                comparison="lt",
                severity=AlertSeverity.HIGH,
                evaluation_window=300,  # 5 minutes
                cooldown_period=900,    # 15 minutes
                enabled=True,
                agent_filter=None,
                notification_channels=["email", "dashboard"]
            ),
            MonitoringRule(
                name="high_hallucination_rate",
                description="Alert when hallucination scores are too high",
                metric_query="avg_hallucination_score",
                threshold=0.3,
                comparison="gt",
                severity=AlertSeverity.CRITICAL,
                evaluation_window=180,  # 3 minutes
                cooldown_period=600,    # 10 minutes
                enabled=True,
                agent_filter=None,
                notification_channels=["email", "dashboard", "slack"]
            ),
            MonitoringRule(
                name="low_memory_utilization",
                description="Alert when memory utilization is too low",
                metric_query="memory_utilization_rate",
                threshold=0.3,
                comparison="lt",
                severity=AlertSeverity.MEDIUM,
                evaluation_window=600,  # 10 minutes
                cooldown_period=1800,   # 30 minutes
                enabled=True,
                agent_filter=None,
                notification_channels=["dashboard"]
            ),
            MonitoringRule(
                name="high_error_rate",
                description="Alert when error rate exceeds threshold",
                metric_query="error_rate",
                threshold=0.1,
                comparison="gt",
                severity=AlertSeverity.HIGH,
                evaluation_window=300,  # 5 minutes
                cooldown_period=600,    # 10 minutes
                enabled=True,
                agent_filter=None,
                notification_channels=["email", "dashboard"]
            ),
            MonitoringRule(
                name="high_latency",
                description="Alert when execution latency is too high",
                metric_query="avg_execution_time",
                threshold=30000,  # 30 seconds
                comparison="gt",
                severity=AlertSeverity.MEDIUM,
                evaluation_window=300,  # 5 minutes
                cooldown_period=900,    # 15 minutes
                enabled=True,
                agent_filter=None,
                notification_channels=["dashboard"]
            ),
            MonitoringRule(
                name="system_health_degradation",
                description="Alert when overall system health degrades",
                metric_query="system_health_score",
                threshold=0.8,
                comparison="lt",
                severity=AlertSeverity.HIGH,
                evaluation_window=180,  # 3 minutes
                cooldown_period=600,    # 10 minutes
                enabled=True,
                agent_filter=None,
                notification_channels=["email", "dashboard"]
            )
        ]
        
        self.monitoring_rules.extend(default_rules)
    
    async def start_monitoring(self):
        """Start the monitoring system."""
        if self._running:
            logger.warning("Monitoring system is already running")
            return
        
        self._running = True
        logger.info("Starting advanced monitoring system...")
        
        # Start monitoring loop
        self._monitoring_task = asyncio.create_task(self._monitoring_loop())
        
        # Initialize performance baselines
        await self._initialize_performance_baselines()
        
        logger.info("Advanced monitoring system started successfully")
    
    async def stop_monitoring(self):
        """Stop the monitoring system."""
        if not self._running:
            return
        
        self._running = False
        logger.info("Stopping advanced monitoring system...")
        
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Advanced monitoring system stopped")
    
    async def _monitoring_loop(self):
        """Main monitoring loop."""
        while self._running:
            try:
                start_time = time.time()
                
                # Evaluate all monitoring rules
                await self._evaluate_monitoring_rules()
                
                # Clean up old alerts
                await self._cleanup_old_alerts()
                
                # Update performance baselines
                await self._update_performance_baselines()
                
                # Calculate sleep time to maintain interval
                elapsed = time.time() - start_time
                sleep_time = max(0, self.config["monitoring_interval"] - elapsed)
                
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(self.config["monitoring_interval"])
    
    async def _evaluate_monitoring_rules(self):
        """Evaluate all monitoring rules and trigger alerts."""
        current_time = datetime.now()
        
        for rule in self.monitoring_rules:
            if not rule.enabled:
                continue
            
            # Check cooldown period
            last_eval = self.last_evaluation_times.get(rule.name)
            if last_eval and (current_time - last_eval).total_seconds() < rule.cooldown_period:
                continue
            
            try:
                # Get metric value
                metric_value = await self._get_metric_value(rule)
                
                # Evaluate threshold
                if self._evaluate_threshold(metric_value, rule.threshold, rule.comparison):
                    await self._trigger_alert(rule, metric_value, current_time)
                else:
                    await self._resolve_alert(rule.name, current_time)
                
                self.last_evaluation_times[rule.name] = current_time
                
            except Exception as e:
                logger.error(f"Error evaluating rule {rule.name}: {e}")
    
    async def _get_metric_value(self, rule: MonitoringRule) -> float:
        """Get current metric value for a monitoring rule."""
        end_time = datetime.now()
        start_time = end_time - timedelta(seconds=rule.evaluation_window)
        
        # Query based on metric type
        if rule.metric_query == "avg_evaluation_score":
            response = self.supabase.table("agent_metrics")\
                .select("evaluation_score")\
                .gte("created_at", start_time.isoformat())\
                .lte("created_at", end_time.isoformat())
            
            if rule.agent_filter:
                response = response.eq("agent_name", rule.agent_filter)
            
            result = response.execute()
            
            if result.data:
                scores = [r["evaluation_score"] for r in result.data if r["evaluation_score"] is not None]
                return statistics.mean(scores) if scores else 0.0
            return 0.0
        
        elif rule.metric_query == "avg_hallucination_score":
            response = self.supabase.table("agent_metrics")\
                .select("hallucination_score")\
                .gte("created_at", start_time.isoformat())\
                .lte("created_at", end_time.isoformat())
            
            if rule.agent_filter:
                response = response.eq("agent_name", rule.agent_filter)
            
            result = response.execute()
            
            if result.data:
                scores = [r["hallucination_score"] for r in result.data if r["hallucination_score"] is not None]
                return statistics.mean(scores) if scores else 0.0
            return 0.0
        
        elif rule.metric_query == "memory_utilization_rate":
            response = self.supabase.table("agent_metrics")\
                .select("memory_context_used")\
                .gte("created_at", start_time.isoformat())\
                .lte("created_at", end_time.isoformat())
            
            if rule.agent_filter:
                response = response.eq("agent_name", rule.agent_filter)
            
            result = response.execute()
            
            if result.data:
                used_count = sum(1 for r in result.data if r["memory_context_used"])
                return used_count / len(result.data) if result.data else 0.0
            return 0.0
        
        elif rule.metric_query == "error_rate":
            response = self.supabase.table("agent_metrics")\
                .select("success")\
                .gte("created_at", start_time.isoformat())\
                .lte("created_at", end_time.isoformat())
            
            if rule.agent_filter:
                response = response.eq("agent_name", rule.agent_filter)
            
            result = response.execute()
            
            if result.data:
                error_count = sum(1 for r in result.data if not r["success"])
                return error_count / len(result.data) if result.data else 0.0
            return 0.0
        
        elif rule.metric_query == "avg_execution_time":
            response = self.supabase.table("agent_metrics")\
                .select("execution_time_ms")\
                .gte("created_at", start_time.isoformat())\
                .lte("created_at", end_time.isoformat())
            
            if rule.agent_filter:
                response = response.eq("agent_name", rule.agent_filter)
            
            result = response.execute()
            
            if result.data:
                times = [r["execution_time_ms"] for r in result.data if r["execution_time_ms"] is not None]
                return statistics.mean(times) if times else 0.0
            return 0.0
        
        elif rule.metric_query == "system_health_score":
            # Get system health from dashboard manager
            health_status = self.dashboard_manager.get_dashboard_status()
            return health_status.get("phoenix", {}).get("healthy", 0) * 1.0
        
        else:
            logger.warning(f"Unknown metric query: {rule.metric_query}")
            return 0.0

    def _evaluate_threshold(self, value: float, threshold: float, comparison: str) -> bool:
        """Evaluate if a value meets the threshold condition."""
        if comparison == "gt":
            return value > threshold
        elif comparison == "lt":
            return value < threshold
        elif comparison == "eq":
            return value == threshold
        elif comparison == "gte":
            return value >= threshold
        elif comparison == "lte":
            return value <= threshold
        else:
            logger.warning(f"Unknown comparison operator: {comparison}")
            return False

    async def _trigger_alert(self, rule: MonitoringRule, metric_value: float, timestamp: datetime):
        """Trigger an alert for a monitoring rule."""
        alert_id = f"{rule.name}_{timestamp.strftime('%Y%m%d_%H%M%S')}"

        # Check if we already have too many active alerts for this rule
        active_count = sum(1 for alert in self.active_alerts.values()
                          if alert.name == rule.name and alert.status == AlertStatus.ACTIVE)

        if active_count >= self.config["max_alerts_per_rule"]:
            logger.debug(f"Suppressing alert {rule.name} - too many active alerts")
            return

        alert = Alert(
            id=alert_id,
            name=rule.name,
            description=rule.description,
            severity=rule.severity,
            status=AlertStatus.ACTIVE,
            triggered_at=timestamp,
            resolved_at=None,
            agent_name=rule.agent_filter,
            metric_name=rule.metric_query,
            metric_value=metric_value,
            threshold=rule.threshold,
            metadata={
                "comparison": rule.comparison,
                "evaluation_window": rule.evaluation_window,
                "notification_channels": rule.notification_channels
            }
        )

        self.active_alerts[alert_id] = alert

        # Send notifications
        await self._send_alert_notification(alert)

        # Log alert
        logger.warning(f"Alert triggered: {rule.name} - {rule.description} "
                      f"(value: {metric_value}, threshold: {threshold})")

    async def _resolve_alert(self, rule_name: str, timestamp: datetime):
        """Resolve active alerts for a monitoring rule."""
        resolved_alerts = []

        for alert_id, alert in self.active_alerts.items():
            if alert.name == rule_name and alert.status == AlertStatus.ACTIVE:
                alert.status = AlertStatus.RESOLVED
                alert.resolved_at = timestamp
                resolved_alerts.append(alert)

        # Send resolution notifications
        for alert in resolved_alerts:
            await self._send_resolution_notification(alert)
            logger.info(f"Alert resolved: {alert.name}")

    async def _send_alert_notification(self, alert: Alert):
        """Send alert notification through configured channels."""
        if not self.config["notification_enabled"]:
            return

        try:
            notification_data = {
                "alert_id": alert.id,
                "name": alert.name,
                "description": alert.description,
                "severity": alert.severity.value,
                "metric_value": alert.metric_value,
                "threshold": alert.threshold,
                "agent_name": alert.agent_name,
                "triggered_at": alert.triggered_at.isoformat()
            }

            # Send to configured channels
            for channel in alert.metadata.get("notification_channels", []):
                if channel == "email":
                    await self._send_email_notification(notification_data)
                elif channel == "dashboard":
                    await self._send_dashboard_notification(notification_data)
                elif channel == "slack":
                    await self._send_slack_notification(notification_data)

        except Exception as e:
            logger.error(f"Failed to send alert notification: {e}")

    async def _send_resolution_notification(self, alert: Alert):
        """Send alert resolution notification."""
        if not self.config["notification_enabled"]:
            return

        try:
            notification_data = {
                "alert_id": alert.id,
                "name": alert.name,
                "resolved_at": alert.resolved_at.isoformat(),
                "duration": (alert.resolved_at - alert.triggered_at).total_seconds()
            }

            # Send resolution notifications (typically less urgent)
            for channel in alert.metadata.get("notification_channels", []):
                if channel == "dashboard":
                    await self._send_dashboard_notification(notification_data, resolved=True)

        except Exception as e:
            logger.error(f"Failed to send resolution notification: {e}")

    async def _send_email_notification(self, notification_data: Dict[str, Any]):
        """Send email notification (placeholder implementation)."""
        # Implementation would depend on email service configuration
        logger.info(f"Email notification: {notification_data['name']}")

    async def _send_dashboard_notification(self, notification_data: Dict[str, Any], resolved: bool = False):
        """Send dashboard notification."""
        # Update dashboard with alert information
        logger.info(f"Dashboard notification: {notification_data['name']} {'resolved' if resolved else 'triggered'}")

    async def _send_slack_notification(self, notification_data: Dict[str, Any]):
        """Send Slack notification (placeholder implementation)."""
        # Implementation would depend on Slack webhook configuration
        logger.info(f"Slack notification: {notification_data['name']}")

    async def _cleanup_old_alerts(self):
        """Clean up old resolved alerts."""
        cutoff_date = datetime.now() - timedelta(days=self.config["alert_retention_days"])

        alerts_to_remove = []
        for alert_id, alert in self.active_alerts.items():
            if (alert.status == AlertStatus.RESOLVED and
                alert.resolved_at and alert.resolved_at < cutoff_date):
                alerts_to_remove.append(alert_id)

        for alert_id in alerts_to_remove:
            del self.active_alerts[alert_id]

        if alerts_to_remove:
            logger.info(f"Cleaned up {len(alerts_to_remove)} old alerts")

    async def _initialize_performance_baselines(self):
        """Initialize performance baselines from historical data."""
        try:
            end_time = datetime.now()
            start_time = end_time - timedelta(days=self.config["performance_baseline_days"])

            # Get historical evaluation scores
            response = self.supabase.table("agent_metrics")\
                .select("agent_name, evaluation_score, execution_time_ms")\
                .gte("created_at", start_time.isoformat())\
                .lte("created_at", end_time.isoformat())\
                .execute()

            if response.data:
                # Calculate baselines by agent
                agent_data = {}
                for record in response.data:
                    agent_name = record["agent_name"]
                    if agent_name not in agent_data:
                        agent_data[agent_name] = {"scores": [], "times": []}

                    if record["evaluation_score"] is not None:
                        agent_data[agent_name]["scores"].append(record["evaluation_score"])
                    if record["execution_time_ms"] is not None:
                        agent_data[agent_name]["times"].append(record["execution_time_ms"])

                # Calculate baseline metrics
                for agent_name, data in agent_data.items():
                    if data["scores"]:
                        self.performance_baselines[f"{agent_name}_avg_score"] = statistics.mean(data["scores"])
                    if data["times"]:
                        self.performance_baselines[f"{agent_name}_avg_time"] = statistics.mean(data["times"])

            logger.info(f"Initialized {len(self.performance_baselines)} performance baselines")

        except Exception as e:
            logger.error(f"Failed to initialize performance baselines: {e}")

    async def _update_performance_baselines(self):
        """Update performance baselines with recent data."""
        # This would be called periodically to update baselines
        # Implementation would use a sliding window approach
        pass

    def get_active_alerts(self) -> List[Dict[str, Any]]:
        """Get all active alerts."""
        return [asdict(alert) for alert in self.active_alerts.values()
                if alert.status == AlertStatus.ACTIVE]

    def get_alert_history(self, hours: int = 24) -> List[Dict[str, Any]]:
        """Get alert history for the specified time period."""
        cutoff_time = datetime.now() - timedelta(hours=hours)

        return [asdict(alert) for alert in self.active_alerts.values()
                if alert.triggered_at >= cutoff_time]

    def acknowledge_alert(self, alert_id: str) -> bool:
        """Acknowledge an alert."""
        if alert_id in self.active_alerts:
            self.active_alerts[alert_id].status = AlertStatus.ACKNOWLEDGED
            logger.info(f"Alert acknowledged: {alert_id}")
            return True
        return False

    def suppress_alert(self, alert_id: str) -> bool:
        """Suppress an alert."""
        if alert_id in self.active_alerts:
            self.active_alerts[alert_id].status = AlertStatus.SUPPRESSED
            logger.info(f"Alert suppressed: {alert_id}")
            return True
        return False

    def add_monitoring_rule(self, rule: MonitoringRule) -> bool:
        """Add a new monitoring rule."""
        try:
            # Validate rule
            if not rule.name or not rule.metric_query:
                raise ValueError("Rule name and metric query are required")

            # Check for duplicate names
            if any(r.name == rule.name for r in self.monitoring_rules):
                raise ValueError(f"Rule with name '{rule.name}' already exists")

            self.monitoring_rules.append(rule)
            logger.info(f"Added monitoring rule: {rule.name}")
            return True

        except Exception as e:
            logger.error(f"Failed to add monitoring rule: {e}")
            return False

    def remove_monitoring_rule(self, rule_name: str) -> bool:
        """Remove a monitoring rule."""
        try:
            self.monitoring_rules = [r for r in self.monitoring_rules if r.name != rule_name]
            logger.info(f"Removed monitoring rule: {rule_name}")
            return True
        except Exception as e:
            logger.error(f"Failed to remove monitoring rule: {e}")
            return False

    def get_monitoring_rules(self) -> List[Dict[str, Any]]:
        """Get all monitoring rules."""
        return [asdict(rule) for rule in self.monitoring_rules]

    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system monitoring status."""
        active_alerts = self.get_active_alerts()

        return {
            "monitoring_active": self._running,
            "total_rules": len(self.monitoring_rules),
            "enabled_rules": sum(1 for r in self.monitoring_rules if r.enabled),
            "active_alerts": len(active_alerts),
            "critical_alerts": sum(1 for a in active_alerts if a["severity"] == "critical"),
            "high_alerts": sum(1 for a in active_alerts if a["severity"] == "high"),
            "performance_baselines": len(self.performance_baselines),
            "last_evaluation": max(self.last_evaluation_times.values()) if self.last_evaluation_times else None
        }


# Utility functions for external use
def create_monitoring_system(memory_manager: Optional[MemoryManager] = None,
                           tracer: Optional[AgentTracer] = None) -> AdvancedMonitoringSystem:
    """Create advanced monitoring system instance."""
    return AdvancedMonitoringSystem(memory_manager, tracer)

async def start_system_monitoring() -> AdvancedMonitoringSystem:
    """Start system monitoring with default configuration."""
    monitoring = AdvancedMonitoringSystem()
    await monitoring.start_monitoring()
    return monitoring
