#!/usr/bin/env python3
"""
Migration Verification Test Suite

Comprehensive tests to verify that the migration from SimpleCoordinator 
to SecureCoordinator was successful and all functionality works as expected.
"""

import os
import sys
import json
from flask import Flask

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from wheel_trader.coordinator import coordinator_app, coordinator
from wheel_trader.secure_coordinator import SecureCoordinator


def test_coordinator_type():
    """Verify that the coordinator is now SecureCoordinator"""
    print("=== Testing Coordinator Type ===")
    assert isinstance(coordinator, SecureCoordinator), f"Expected SecureCoordinator, got {type(coordinator)}"
    print("✅ Coordinator is SecureCoordinator")


def test_coordinator_functionality():
    """Test core SecureCoordinator functionality"""
    print("\n=== Testing SecureCoordinator Functionality ===")
    
    # Test tool execution
    result = coordinator.execute_tool_directly("market_analysis", symbol="AAPL", analysis_type="rsi")
    assert result["success"] is True, f"Tool execution failed: {result.get('error')}"
    print("✅ Direct tool execution works")
    
    # Test agent creation
    agent = coordinator.create_secure_agent("test_migration_agent")
    assert agent is not None, "Agent creation failed"
    assert "test_migration_agent" in coordinator.agents, "Agent not registered"
    print("✅ Agent creation works")
    
    # Test status
    status = coordinator.get_status()
    assert status["mode"] == "secure_coordinator", f"Wrong mode: {status['mode']}"
    assert len(status["available_tools"]) == 3, f"Wrong tool count: {len(status['available_tools'])}"
    print("✅ Status reporting works")


def test_flask_integration():
    """Test Flask integration with SecureCoordinator"""
    print("\n=== Testing Flask Integration ===")
    
    # Create Flask app
    app = Flask(__name__)
    app.register_blueprint(coordinator_app)
    client = app.test_client()
    
    # Test status endpoint
    response = client.get('/status')
    assert response.status_code == 200, f"Status endpoint failed: {response.status_code}"
    data = json.loads(response.data)
    assert data["mode"] == "secure_coordinator", f"Wrong mode in Flask: {data['mode']}"
    print("✅ Flask status endpoint works")
    
    # Test tools endpoint
    response = client.get('/tools')
    assert response.status_code == 200, f"Tools endpoint failed: {response.status_code}"
    data = json.loads(response.data)
    assert data["total_tools"] == 3, f"Wrong tool count in Flask: {data['total_tools']}"
    print("✅ Flask tools endpoint works")
    
    # Test task execution
    task_data = {"task": "Analyze AAPL for RSI indicators"}
    response = client.post('/execute_task', data=json.dumps(task_data), content_type='application/json')
    assert response.status_code == 200, f"Task execution failed: {response.status_code}"
    data = json.loads(response.data)
    assert "success" in data, "No success field in response"
    print("✅ Flask task execution works")


def test_backward_compatibility():
    """Test that the new system maintains backward compatibility"""
    print("\n=== Testing Backward Compatibility ===")
    
    # Test that all expected tools are available
    expected_tools = ["options_data", "market_analysis", "risk_assessment"]
    available_tools = list(coordinator.available_tools.keys())
    
    for tool in expected_tools:
        assert tool in available_tools, f"Missing tool: {tool}"
    print("✅ All expected tools are available")
    
    # Test that the coordinator has expected methods
    expected_methods = ["execute_tool_directly", "get_status", "create_secure_agent"]
    for method in expected_methods:
        assert hasattr(coordinator, method), f"Missing method: {method}"
    print("✅ All expected methods are available")


def test_enhanced_features():
    """Test enhanced features that SecureCoordinator provides"""
    print("\n=== Testing Enhanced Features ===")
    
    # Test health monitoring
    assert hasattr(coordinator, 'health_manager'), "Missing health manager"
    assert coordinator.health_manager is not None, "Health manager not initialized"
    print("✅ Health monitoring available")
    
    # Test agent management
    initial_agent_count = len(coordinator.agents)
    agent = coordinator.create_secure_agent("enhanced_test_agent")
    assert len(coordinator.agents) == initial_agent_count + 1, "Agent count not updated"
    print("✅ Enhanced agent management works")
    
    # Test statistics
    assert hasattr(coordinator, 'stats'), "Missing stats"
    assert "agents_created" in coordinator.stats, "Missing agents_created stat"
    assert "start_time" in coordinator.stats, "Missing start_time stat"
    print("✅ Enhanced statistics available")


def test_memory_and_observability():
    """Test memory and observability features (graceful degradation)"""
    print("\n=== Testing Memory & Observability ===")
    
    # These should be available but may not be functional without database
    assert hasattr(coordinator, 'shared_memory_manager'), "Missing shared memory manager attribute"
    assert hasattr(coordinator, 'observability_manager'), "Missing observability manager attribute"
    
    # Test that the system works even if memory/observability are not available
    result = coordinator.execute_tool_directly("market_analysis", symbol="AAPL", analysis_type="rsi")
    assert result["success"] is True, "System should work without memory/observability"
    print("✅ Memory & observability graceful degradation works")


def run_migration_verification():
    """Run all migration verification tests"""
    print("🚀 Starting Migration Verification Tests...")
    print("=" * 60)
    
    tests = [
        test_coordinator_type,
        test_coordinator_functionality,
        test_flask_integration,
        test_backward_compatibility,
        test_enhanced_features,
        test_memory_and_observability,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            test()
            passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print("📊 Migration Verification Results:")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 MIGRATION SUCCESSFUL! 🎉")
        print("SecureCoordinator is fully operational and replaces SimpleCoordinator.")
    else:
        print(f"\n⚠️  Migration partially successful with {failed} issues to address.")
    
    return passed, failed


if __name__ == "__main__":
    run_migration_verification()
