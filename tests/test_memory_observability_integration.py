#!/usr/bin/env python3
"""
Integration Tests for Memory & Observability (MEM-O11) System

Comprehensive tests for the integrated memory, observability, and health monitoring system.
"""

import os
import sys
import time
import pytest
from unittest.mock import Mock, patch

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from wheel_trader.memory.manager import MemoryManager
from wheel_trader.observability import ObservabilityManager
from wheel_trader.observability.tracer import AgentTracer
from wheel_trader.observability.memory_bridge import MemoryObservabilityBridge
from wheel_trader.smolagents_e2b import HealthAwareAgent, create_trading_agent
from wheel_trader.secure_coordinator import SecureCoordinator
from wheel_trader.agent_health import AgentHealthManager


class TestMemoryObservabilityIntegration:
    """Test suite for integrated memory and observability system"""
    
    def setup_method(self):
        """Setup test environment"""
        self.test_agent_name = "test_integration_agent"
        self.test_coordinator_name = "test_coordinator"
        
    def test_memory_manager_initialization(self):
        """Test memory manager initialization with different backends"""
        print("\n=== Testing Memory Manager Initialization ===")
        
        # Test with mock backend (fallback)
        try:
            memory_manager = MemoryManager(
                backend="mock",
                config={},
                agent_name=self.test_agent_name
            )
            assert memory_manager is not None
            print("✅ Memory manager initialized with mock backend")
            
            # Test health status
            health = memory_manager.get_health_status()
            assert isinstance(health, dict)
            print(f"✅ Memory health status: {health}")
            
        except Exception as e:
            print(f"❌ Memory manager initialization failed: {e}")
            raise
    
    def test_observability_tracer(self):
        """Test observability tracer functionality"""
        print("\n=== Testing Observability Tracer ===")
        
        try:
            tracer = AgentTracer(agent_name=self.test_agent_name)
            assert tracer is not None
            print("✅ Agent tracer initialized")
            
            # Test trace execution context
            with tracer.trace_execution("test_operation") as span:
                time.sleep(0.1)  # Simulate work
                assert span is not None
                print("✅ Trace execution context working")
            
            # Test trace statistics
            stats = tracer.get_trace_stats()
            assert stats["traces_created"] >= 1
            print(f"✅ Trace stats: {stats}")
            
        except Exception as e:
            print(f"❌ Observability tracer test failed: {e}")
            raise
    
    def test_memory_observability_bridge(self):
        """Test memory-observability bridge integration"""
        print("\n=== Testing Memory-Observability Bridge ===")
        
        try:
            # Initialize components
            memory_manager = MemoryManager(
                backend="mock",
                config={},
                agent_name=self.test_agent_name
            )
            tracer = AgentTracer(agent_name=self.test_agent_name)
            bridge = MemoryObservabilityBridge(memory_manager, tracer)
            
            assert bridge is not None
            print("✅ Memory-observability bridge initialized")
            
            # Test storing trace memory
            memory_id = bridge.store_trace_memory(
                trace_content="Test execution completed successfully",
                trace_metadata={"test": True, "operation": "integration_test"},
                mem_type="test_execution",
                importance=0.7
            )
            
            if memory_id:
                print(f"✅ Trace memory stored with ID: {memory_id}")
            else:
                print("⚠️ Trace memory storage returned None (expected with mock backend)")
            
            # Test bridge statistics
            bridge_stats = bridge.get_bridge_stats()
            assert isinstance(bridge_stats, dict)
            print(f"✅ Bridge stats: {bridge_stats}")
            
        except Exception as e:
            print(f"❌ Memory-observability bridge test failed: {e}")
            raise
    
    def test_enhanced_health_aware_agent(self):
        """Test enhanced HealthAwareAgent with memory and observability"""
        print("\n=== Testing Enhanced HealthAwareAgent ===")
        
        try:
            # Create enhanced agent with memory and observability
            agent = HealthAwareAgent(
                name=self.test_agent_name,
                model=None,  # Will use default
                tools=[],
                health_threshold=0.7,
                memory_backend="mock",
                memory_config={},
                enable_observability=True
            )
            
            assert agent is not None
            print("✅ Enhanced HealthAwareAgent created")
            
            # Test health status (should include memory and observability info)
            health_status = agent.get_health_status()
            assert "memory_system" in health_status
            assert "observability" in health_status
            print(f"✅ Enhanced health status: {health_status}")
            
            # Test memory operations
            if agent.memory_manager:
                memories = agent.search_memories("test query", limit=3)
                assert isinstance(memories, list)
                print(f"✅ Memory search returned {len(memories)} results")
                
                memory_stats = agent.get_memory_stats()
                assert isinstance(memory_stats, dict)
                print(f"✅ Memory stats: {memory_stats}")
            
        except Exception as e:
            print(f"❌ Enhanced HealthAwareAgent test failed: {e}")
            raise
    
    def test_enhanced_secure_coordinator(self):
        """Test enhanced SecureCoordinator with memory coordination"""
        print("\n=== Testing Enhanced SecureCoordinator ===")
        
        try:
            # Create enhanced coordinator
            coordinator = SecureCoordinator(
                health_threshold=0.7,
                memory_backend="mock",
                memory_config={},
                enable_observability=True
            )
            
            assert coordinator is not None
            print("✅ Enhanced SecureCoordinator created")
            
            # Test agent creation with enhanced features
            agent = coordinator.create_secure_agent(
                name=self.test_agent_name,
                model=None,
                tools=[],
                memory_backend="mock",
                enable_observability=True
            )
            
            assert agent is not None
            print(f"✅ Enhanced agent created: {agent.name}")
            
            # Test comprehensive status
            status = coordinator.get_agent_status()
            assert "coordination_stats" in status
            assert "shared_memory" in status
            assert "observability" in status
            assert "enhanced_agent_status" in status
            print(f"✅ Comprehensive coordinator status retrieved")
            
            # Test coordination statistics
            coord_stats = coordinator.get_coordination_stats()
            assert isinstance(coord_stats, dict)
            assert "memory_enabled" in coord_stats
            assert "observability_enabled" in coord_stats
            print(f"✅ Coordination stats: {coord_stats}")
            
        except Exception as e:
            print(f"❌ Enhanced SecureCoordinator test failed: {e}")
            raise
    
    def test_end_to_end_integration(self):
        """Test complete end-to-end integration"""
        print("\n=== Testing End-to-End Integration ===")
        
        try:
            # Create coordinator with full integration
            coordinator = SecureCoordinator(
                health_threshold=0.7,
                memory_backend="mock",
                enable_observability=True
            )
            
            # Create multiple enhanced agents
            data_agent = coordinator.create_secure_agent("data_agent")
            analysis_agent = coordinator.create_secure_agent("analysis_agent")
            
            print(f"✅ Created {len(coordinator.agents)} enhanced agents")
            
            # Test memory search across coordination
            if coordinator.shared_memory_manager:
                memories = coordinator.search_coordination_memories("agent", limit=5)
                assert isinstance(memories, list)
                print(f"✅ Coordination memory search returned {len(memories)} results")
            
            # Test agent memory summaries
            for agent_name in coordinator.agents.keys():
                summary = coordinator.get_agent_memory_summary(agent_name)
                assert isinstance(summary, dict)
                assert "agent_name" in summary
                print(f"✅ Memory summary for {agent_name}: {summary}")
            
            # Test memory consolidation
            consolidation_results = coordinator.consolidate_all_memories()
            assert isinstance(consolidation_results, dict)
            print(f"✅ Memory consolidation results: {consolidation_results}")
            
            print("✅ End-to-end integration test completed successfully")
            
        except Exception as e:
            print(f"❌ End-to-end integration test failed: {e}")
            raise


def run_integration_tests():
    """Run all integration tests"""
    print("🚀 Starting Memory & Observability Integration Tests...")
    
    test_suite = TestMemoryObservabilityIntegration()
    test_methods = [
        test_suite.test_memory_manager_initialization,
        test_suite.test_observability_tracer,
        test_suite.test_memory_observability_bridge,
        test_suite.test_enhanced_health_aware_agent,
        test_suite.test_enhanced_secure_coordinator,
        test_suite.test_end_to_end_integration
    ]
    
    passed = 0
    failed = 0
    
    for test_method in test_methods:
        try:
            test_suite.setup_method()
            test_method()
            passed += 1
        except Exception as e:
            print(f"❌ Test {test_method.__name__} failed: {e}")
            failed += 1
    
    total = passed + failed
    success_rate = (passed / total * 100) if total > 0 else 0
    
    print(f"\n📊 Integration Test Results:")
    print(f"✅ Passed: {passed}/{total}")
    print(f"❌ Failed: {failed}/{total}")
    print(f"📈 Success Rate: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("🎉 Integration tests PASSED!")
        return True
    else:
        print("⚠️ Integration tests need attention")
        return False


if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)
