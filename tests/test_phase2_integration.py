"""
Phase 2 Integration Tests: Phoenix Observability Infrastructure

Tests the Phoenix docker-compose setup, custom dashboard configuration,
and integration with the enhanced evaluation system.
"""

import os
import json
import time
import requests
from unittest.mock import Mock, patch
import pytest
from typing import Dict, Any

# Import components to test
from wheel_trader.phoenix_dashboard import PhoenixDashboardManager, setup_phoenix_dashboard, get_dashboard_health


class TestPhoenixDockerSetup:
    """Test Phoenix docker-compose configuration and setup."""
    
    def test_docker_compose_file_exists(self):
        """Test that docker-compose.phoenix.yml exists and is valid."""
        compose_file = "docker-compose.phoenix.yml"
        assert os.path.exists(compose_file), "docker-compose.phoenix.yml not found"
        
        # Check file is not empty
        with open(compose_file, 'r') as f:
            content = f.read()
        assert len(content) > 0, "docker-compose.phoenix.yml is empty"
        assert "phoenix:" in content, "Phoenix service not defined"
        assert "phoenix-postgres:" in content, "PostgreSQL service not defined"
    
    def test_phoenix_configuration_files(self):
        """Test that Phoenix configuration files exist."""
        required_files = [
            "phoenix/custom_config/phoenix.yaml",
            "phoenix/init-scripts/01-init-phoenix.sql",
            "phoenix/grafana/datasources/datasources.yaml",
            "phoenix/grafana/dashboards/dashboards.yaml",
            "phoenix/grafana/dashboards/trading-agents-dashboard.json"
        ]
        
        for file_path in required_files:
            assert os.path.exists(file_path), f"Required configuration file not found: {file_path}"
    
    def test_setup_script_exists(self):
        """Test that setup script exists and is executable."""
        setup_script = "phoenix/setup-phoenix.sh"
        assert os.path.exists(setup_script), "Setup script not found"
        
        # Check if script is executable
        assert os.access(setup_script, os.X_OK), "Setup script is not executable"
    
    def test_phoenix_yaml_configuration(self):
        """Test Phoenix YAML configuration structure."""
        config_file = "phoenix/custom_config/phoenix.yaml"
        
        with open(config_file, 'r') as f:
            content = f.read()
        
        # Check for required sections
        required_sections = [
            "server:", "database:", "otlp:", "traces:", "trading:", "monitoring:"
        ]
        
        for section in required_sections:
            assert section in content, f"Required configuration section not found: {section}"
    
    def test_grafana_dashboard_configuration(self):
        """Test Grafana dashboard configuration."""
        dashboard_file = "phoenix/grafana/dashboards/trading-agents-dashboard.json"
        
        with open(dashboard_file, 'r') as f:
            dashboard_config = json.load(f)
        
        # Validate dashboard structure
        assert "dashboard" in dashboard_config
        dashboard = dashboard_config["dashboard"]
        
        assert "title" in dashboard
        assert "panels" in dashboard
        assert len(dashboard["panels"]) > 0, "No panels configured in dashboard"
        
        # Check for required panels
        panel_titles = [panel.get("title", "") for panel in dashboard["panels"]]
        required_panels = [
            "Agent Health Scores",
            "Evaluation Scores Over Time", 
            "Memory Usage Trends",
            "Phoenix Trace Performance"
        ]
        
        for required_panel in required_panels:
            assert any(required_panel in title for title in panel_titles), \
                f"Required panel not found: {required_panel}"


class TestPhoenixDashboardManager:
    """Test the Phoenix Dashboard Manager functionality."""
    
    @pytest.fixture
    def mock_supabase(self):
        """Mock Supabase client for testing."""
        mock_client = Mock()
        mock_response = Mock()
        mock_response.data = [
            {"evaluation_score": 0.85, "created_at": "2025-01-11T10:00:00Z"},
            {"evaluation_score": 0.92, "created_at": "2025-01-11T10:05:00Z"}
        ]
        mock_response.count = 2
        mock_client.table.return_value.select.return_value.gte.return_value.execute.return_value = mock_response
        mock_client.table.return_value.select.return_value.execute.return_value = mock_response
        return mock_client
    
    @pytest.fixture
    def dashboard_manager(self, mock_supabase):
        """Create dashboard manager for testing."""
        return PhoenixDashboardManager(
            phoenix_url="http://localhost:6006",
            supabase_client=mock_supabase
        )
    
    def test_dashboard_manager_initialization(self, dashboard_manager):
        """Test dashboard manager initializes correctly."""
        assert dashboard_manager.phoenix_url == "http://localhost:6006"
        assert dashboard_manager.dashboard_config["refresh_interval"] == 30
        assert dashboard_manager.dashboard_config["default_time_range"] == "1h"
        assert len(dashboard_manager.custom_panels) > 0
    
    def test_custom_panels_configuration(self, dashboard_manager):
        """Test custom panels are properly configured."""
        panels = dashboard_manager.custom_panels
        
        # Check required panels exist
        required_panels = [
            "agent_health_summary",
            "evaluation_scores_trend",
            "memory_utilization",
            "hallucination_detection",
            "trading_operations_table"
        ]
        
        for panel_name in required_panels:
            assert panel_name in panels, f"Required panel not configured: {panel_name}"
            
            panel_config = panels[panel_name]
            assert "type" in panel_config
            assert "title" in panel_config
            assert "query" in panel_config
    
    def test_dashboard_layout_creation(self, dashboard_manager):
        """Test dashboard layout creation."""
        layout = dashboard_manager._create_dashboard_layout()
        
        assert "title" in layout
        assert "layout" in layout
        assert "rows" in layout["layout"]
        
        # Check rows are properly configured
        rows = layout["layout"]["rows"]
        assert len(rows) >= 3, "Insufficient dashboard rows configured"
        
        # Check each row has panels
        for row in rows:
            assert "title" in row
            assert "panels" in row
            assert len(row["panels"]) > 0
    
    def test_data_sources_configuration(self, dashboard_manager):
        """Test data sources configuration."""
        data_sources = dashboard_manager._configure_data_sources()
        
        assert len(data_sources) >= 2, "Insufficient data sources configured"
        
        # Check for required data sources
        source_names = [ds["name"] for ds in data_sources]
        assert "supabase_main" in source_names
        assert "phoenix_postgres" in source_names
        
        # Validate data source structure
        for ds in data_sources:
            assert "name" in ds
            assert "type" in ds
            assert "url" in ds
    
    @patch('requests.get')
    def test_phoenix_health_check(self, mock_get, dashboard_manager):
        """Test Phoenix health check functionality."""
        # Mock successful health check
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.elapsed.total_seconds.return_value = 0.1
        mock_get.return_value = mock_response
        
        health = dashboard_manager._check_phoenix_health()
        
        assert health["healthy"] is True
        assert health["url"] == "http://localhost:6006"
        assert "response_time_ms" in health
    
    @patch('requests.get')
    def test_phoenix_health_check_failure(self, mock_get, dashboard_manager):
        """Test Phoenix health check failure handling."""
        # Mock failed health check
        mock_get.side_effect = requests.exceptions.ConnectionError("Connection failed")
        
        health = dashboard_manager._check_phoenix_health()
        
        assert health["healthy"] is False
        assert "error" in health
    
    def test_recent_metrics_retrieval(self, dashboard_manager, mock_supabase):
        """Test recent metrics retrieval."""
        metrics = dashboard_manager._get_recent_metrics()
        
        assert "total_evaluations_last_hour" in metrics
        assert "average_evaluation_score" in metrics
        assert metrics["total_evaluations_last_hour"] == 2
        assert metrics["average_evaluation_score"] == 0.885  # (0.85 + 0.92) / 2
    
    def test_setup_custom_dashboard(self, dashboard_manager):
        """Test custom dashboard setup."""
        with patch.object(dashboard_manager, '_check_phoenix_health') as mock_health:
            mock_health.return_value = {"healthy": True}
            
            result = dashboard_manager.setup_custom_dashboard()
            
            assert result["status"] == "success"
            assert "dashboard_url" in result
            assert "panels_configured" in result
            assert result["panels_configured"] > 0


class TestPhoenixIntegration:
    """Test Phoenix integration with existing systems."""
    
    def test_dashboard_config_file_creation(self):
        """Test that dashboard configuration file is created."""
        # This would be created by the dashboard manager
        config_file = "phoenix/dashboard_config.json"
        
        # Create a mock configuration for testing
        mock_config = {
            "dashboard": {"title": "Test Dashboard"},
            "data_sources": [{"name": "test_source"}],
            "panels": {"test_panel": {"type": "stat"}},
            "timestamp": "2025-01-11T10:00:00Z"
        }
        
        # Save mock configuration
        os.makedirs("phoenix", exist_ok=True)
        with open(config_file, 'w') as f:
            json.dump(mock_config, f)
        
        # Verify file exists and is valid JSON
        assert os.path.exists(config_file)
        
        with open(config_file, 'r') as f:
            loaded_config = json.load(f)
        
        assert loaded_config["dashboard"]["title"] == "Test Dashboard"
        
        # Cleanup
        if os.path.exists(config_file):
            os.remove(config_file)
    
    @patch('wheel_trader.phoenix_dashboard.PhoenixDashboardManager')
    def test_setup_phoenix_dashboard_function(self, mock_manager_class):
        """Test the setup_phoenix_dashboard utility function."""
        # Mock dashboard manager
        mock_manager = Mock()
        mock_manager.setup_custom_dashboard.return_value = {
            "status": "success",
            "dashboard_url": "http://localhost:6006/dashboard/custom",
            "panels_configured": 8
        }
        mock_manager_class.return_value = mock_manager
        
        # Test the function
        result = setup_phoenix_dashboard()
        
        assert result["status"] == "success"
        assert "dashboard_url" in result
        assert result["panels_configured"] == 8
    
    @patch('wheel_trader.phoenix_dashboard.PhoenixDashboardManager')
    def test_get_dashboard_health_function(self, mock_manager_class):
        """Test the get_dashboard_health utility function."""
        # Mock dashboard manager
        mock_manager = Mock()
        mock_manager.get_dashboard_status.return_value = {
            "status": "healthy",
            "phoenix": {"healthy": True},
            "data_sources": {"supabase": {"healthy": True}},
            "recent_metrics": {"total_evaluations_last_hour": 10}
        }
        mock_manager_class.return_value = mock_manager
        
        # Test the function
        health = get_dashboard_health()
        
        assert health["status"] == "healthy"
        assert health["phoenix"]["healthy"] is True
        assert health["recent_metrics"]["total_evaluations_last_hour"] == 10


class TestPhoenixDocumentation:
    """Test Phoenix documentation and setup guides."""
    
    def test_documentation_exists(self):
        """Test that Phoenix documentation exists."""
        doc_file = "docs/phoenix_setup.md"
        assert os.path.exists(doc_file), "Phoenix setup documentation not found"
        
        with open(doc_file, 'r') as f:
            content = f.read()
        
        # Check for required sections
        required_sections = [
            "# Phoenix Observability Setup Guide",
            "## 🎯 Overview",
            "## 🚀 Quick Start",
            "## 📊 Service Architecture",
            "## 🔧 Configuration",
            "## 🛠️ Troubleshooting"
        ]
        
        for section in required_sections:
            assert section in content, f"Required documentation section not found: {section}"
    
    def test_setup_script_documentation(self):
        """Test that setup script has proper documentation."""
        setup_script = "phoenix/setup-phoenix.sh"
        
        with open(setup_script, 'r') as f:
            content = f.read()
        
        # Check for documentation elements
        assert "#!/bin/bash" in content
        assert "Phoenix Observability Setup Script" in content
        assert "print_status" in content  # Function for status output
        assert "check_docker" in content  # Docker validation


class TestPhase2Validation:
    """Comprehensive Phase 2 validation tests."""
    
    def test_all_phase2_files_exist(self):
        """Test that all Phase 2 files are created."""
        required_files = [
            "docker-compose.phoenix.yml",
            "phoenix/setup-phoenix.sh",
            "phoenix/custom_config/phoenix.yaml",
            "phoenix/init-scripts/01-init-phoenix.sql",
            "phoenix/grafana/datasources/datasources.yaml",
            "phoenix/grafana/dashboards/dashboards.yaml",
            "phoenix/grafana/dashboards/trading-agents-dashboard.json",
            "wheel_trader/phoenix_dashboard.py",
            "docs/phoenix_setup.md"
        ]
        
        missing_files = []
        for file_path in required_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)
        
        assert len(missing_files) == 0, f"Missing Phase 2 files: {missing_files}"
    
    def test_integration_with_phase1(self):
        """Test integration between Phase 2 and Phase 1 components."""
        # Test that Phoenix dashboard can reference Phase 1 tables
        dashboard_file = "phoenix/grafana/dashboards/trading-agents-dashboard.json"
        
        with open(dashboard_file, 'r') as f:
            content = f.read()
        
        # Check for references to Phase 1 database tables
        phase1_tables = ["agent_metrics", "memory_embeddings", "agent_health"]
        
        for table in phase1_tables:
            assert table in content, f"Phase 1 table '{table}' not referenced in dashboard"
    
    def test_configuration_consistency(self):
        """Test configuration consistency across files."""
        # Check that database names are consistent
        compose_file = "docker-compose.phoenix.yml"
        phoenix_config = "phoenix/custom_config/phoenix.yaml"
        
        with open(compose_file, 'r') as f:
            compose_content = f.read()
        
        with open(phoenix_config, 'r') as f:
            phoenix_content = f.read()
        
        # Check database name consistency
        assert "phoenix_db" in compose_content
        assert "phoenix_db" in phoenix_content
        
        # Check user consistency
        assert "phoenix_user" in compose_content
        assert "phoenix_user" in phoenix_content


if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v"])
