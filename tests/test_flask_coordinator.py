#!/usr/bin/env python3
"""
Test suite for Flask Coordinator with SecureCoordinator integration

Tests the Flask endpoints to ensure they work correctly with the new
SecureCoordinator instead of the old SimpleCoordinator.
"""

import json
import os
import sys

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flask import Flask

from wheel_trader.coordinator import coordinator_app


class TestFlaskCoordinator:
    """Test suite for Flask Coordinator endpoints"""

    def setup_method(self):
        """Set up test environment before each test"""
        # Create a Flask app and register the blueprint
        self.app = Flask(__name__)
        self.app.register_blueprint(coordinator_app)
        self.client = self.app.test_client()
        self.app.config['TESTING'] = True

    def test_status_endpoint(self):
        """Test the /status endpoint"""
        print("\n=== Testing /status Endpoint ===")
        
        response = self.client.get('/status')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert "status" in data
        assert "mode" in data
        assert data["mode"] == "secure_coordinator"
        assert "available_tools" in data
        
        expected_tools = ["options_data", "market_analysis", "risk_assessment"]
        for tool in expected_tools:
            assert tool in data["available_tools"]
        
        print("✅ Status endpoint test passed!")

    def test_tools_endpoint(self):
        """Test the /tools endpoint"""
        print("\n=== Testing /tools Endpoint ===")
        
        response = self.client.get('/tools')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert "available_tools" in data
        assert "total_tools" in data
        assert data["total_tools"] == 3
        
        expected_tools = ["options_data", "market_analysis", "risk_assessment"]
        for tool in expected_tools:
            assert tool in data["available_tools"]
            assert "description" in data["available_tools"][tool]
        
        print("✅ Tools endpoint test passed!")

    def test_health_endpoint(self):
        """Test the /health endpoint"""
        print("\n=== Testing /health Endpoint ===")
        
        response = self.client.get('/health')
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert "healthy" in data
        assert data["healthy"] is True
        assert "coordinator" in data
        
        print("✅ Health endpoint test passed!")

    def test_execute_task_endpoint(self):
        """Test the /execute_task endpoint"""
        print("\n=== Testing /execute_task Endpoint ===")
        
        # Test with a market analysis task
        task_data = {
            "task": "Analyze AAPL for RSI indicators"
        }
        
        response = self.client.post(
            '/execute_task',
            data=json.dumps(task_data),
            content_type='application/json'
        )
        
        assert response.status_code == 200
        
        data = json.loads(response.data)
        assert "success" in data
        assert "task" in data
        assert data["task"] == task_data["task"]
        assert "execution_time" in data
        
        if data["success"]:
            assert "result" in data
            print(f"Task executed successfully in {data['execution_time']:.2f}s")
        else:
            print(f"Task failed: {data.get('error', 'Unknown error')}")
        
        print("✅ Execute task endpoint test passed!")

    def test_execute_task_invalid_json(self):
        """Test the /execute_task endpoint with invalid JSON"""
        print("\n=== Testing /execute_task with Invalid JSON ===")
        
        response = self.client.post(
            '/execute_task',
            data="invalid json",
            content_type='application/json'
        )
        
        assert response.status_code == 400
        
        data = json.loads(response.data)
        assert "success" in data
        assert data["success"] is False
        assert "error" in data
        
        print("✅ Invalid JSON test passed!")

    def test_execute_task_missing_task(self):
        """Test the /execute_task endpoint with missing task field"""
        print("\n=== Testing /execute_task with Missing Task ===")
        
        task_data = {
            "not_task": "This should fail"
        }
        
        response = self.client.post(
            '/execute_task',
            data=json.dumps(task_data),
            content_type='application/json'
        )
        
        assert response.status_code == 400
        
        data = json.loads(response.data)
        assert "success" in data
        assert data["success"] is False
        assert "error" in data
        assert "task" in data["error"]
        
        print("✅ Missing task test passed!")

    def test_404_endpoint(self):
        """Test 404 error handling"""
        print("\n=== Testing 404 Error Handling ===")
        
        response = self.client.get('/nonexistent')
        assert response.status_code == 404
        
        data = json.loads(response.data)
        assert "error" in data
        assert "available_endpoints" in data
        
        print("✅ 404 error handling test passed!")


def run_flask_tests():
    """Run all Flask coordinator tests"""
    print("🚀 Starting Flask Coordinator Tests...")
    
    test_instance = TestFlaskCoordinator()
    tests = [
        test_instance.test_status_endpoint,
        test_instance.test_tools_endpoint,
        test_instance.test_health_endpoint,
        test_instance.test_execute_task_endpoint,
        test_instance.test_execute_task_invalid_json,
        test_instance.test_execute_task_missing_task,
        test_instance.test_404_endpoint,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            test_instance.setup_method()
            test()
            passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed: {e}")
            failed += 1
    
    print("\n📊 Flask Test Results:")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    return passed, failed


if __name__ == "__main__":
    run_flask_tests()
