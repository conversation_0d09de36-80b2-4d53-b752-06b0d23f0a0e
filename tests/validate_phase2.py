#!/usr/bin/env python3
"""
Phase 2 Validation Script

Validates Phoenix Observability Infrastructure implementation
without requiring actual Docker services to be running.
"""

import os
import json
import yaml
import sys
from datetime import datetime
from typing import Dict, Any, List

def validate_docker_compose():
    """Validate docker-compose.phoenix.yml configuration."""
    print("🔍 Validating Docker Compose configuration...")
    
    compose_file = "docker-compose.phoenix.yml"
    if not os.path.exists(compose_file):
        print("❌ docker-compose.phoenix.yml not found")
        return False
    
    with open(compose_file, 'r') as f:
        content = f.read()
    
    # Check for required services
    required_services = ["phoenix:", "phoenix-postgres:", "phoenix-redis:", "phoenix-grafana:"]
    for service in required_services:
        if service not in content:
            print(f"❌ Required service not found: {service}")
            return False
    
    # Check for required ports
    required_ports = ["6006:6006", "5433:5432", "6380:6379", "3001:3000"]
    for port in required_ports:
        if port not in content:
            print(f"❌ Required port mapping not found: {port}")
            return False
    
    # Check for volumes
    if "volumes:" not in content:
        print("❌ No volumes section found")
        return False
    
    print("✅ Docker Compose configuration validated")
    return True

def validate_phoenix_config():
    """Validate Phoenix YAML configuration."""
    print("🔍 Validating Phoenix configuration...")
    
    config_file = "phoenix/custom_config/phoenix.yaml"
    if not os.path.exists(config_file):
        print("❌ Phoenix configuration file not found")
        return False
    
    try:
        with open(config_file, 'r') as f:
            config = yaml.safe_load(f)
    except yaml.YAMLError as e:
        print(f"❌ Invalid YAML in Phoenix configuration: {e}")
        return False
    
    # Check required sections
    required_sections = ["server", "database", "otlp", "traces", "trading", "monitoring"]
    for section in required_sections:
        if section not in config:
            print(f"❌ Required configuration section not found: {section}")
            return False
    
    # Validate server configuration
    if "host" not in config["server"] or "port" not in config["server"]:
        print("❌ Server configuration incomplete")
        return False
    
    # Validate OTLP configuration
    if "grpc" not in config["otlp"] or "http" not in config["otlp"]:
        print("❌ OTLP configuration incomplete")
        return False
    
    print("✅ Phoenix configuration validated")
    return True

def validate_database_init():
    """Validate database initialization scripts."""
    print("🔍 Validating database initialization...")
    
    init_file = "phoenix/init-scripts/01-init-phoenix.sql"
    if not os.path.exists(init_file):
        print("❌ Database initialization script not found")
        return False
    
    with open(init_file, 'r') as f:
        content = f.read()
    
    # Check for required SQL elements
    required_elements = [
        "CREATE EXTENSION IF NOT EXISTS",
        "CREATE SCHEMA IF NOT EXISTS",
        "CREATE TABLE IF NOT EXISTS trading_analytics.agent_trace_summary",
        "CREATE OR REPLACE FUNCTION trading_analytics.link_trace_evaluation",
        "CREATE OR REPLACE VIEW trading_analytics.dashboard_metrics"
    ]
    
    for element in required_elements:
        if element not in content:
            print(f"❌ Required SQL element not found: {element}")
            return False
    
    print("✅ Database initialization validated")
    return True

def validate_grafana_config():
    """Validate Grafana configuration files."""
    print("🔍 Validating Grafana configuration...")
    
    # Check datasources configuration
    datasources_file = "phoenix/grafana/datasources/datasources.yaml"
    if not os.path.exists(datasources_file):
        print("❌ Grafana datasources configuration not found")
        return False
    
    try:
        with open(datasources_file, 'r') as f:
            datasources = yaml.safe_load(f)
    except yaml.YAMLError as e:
        print(f"❌ Invalid YAML in datasources configuration: {e}")
        return False
    
    # Check for required datasources
    if "datasources" not in datasources:
        print("❌ No datasources section found")
        return False
    
    datasource_names = [ds["name"] for ds in datasources["datasources"]]
    required_datasources = ["Phoenix PostgreSQL", "Supabase Main"]
    
    for ds_name in required_datasources:
        if ds_name not in datasource_names:
            print(f"❌ Required datasource not found: {ds_name}")
            return False
    
    # Check dashboard configuration
    dashboard_config_file = "phoenix/grafana/dashboards/dashboards.yaml"
    if not os.path.exists(dashboard_config_file):
        print("❌ Grafana dashboard configuration not found")
        return False
    
    # Check dashboard JSON
    dashboard_file = "phoenix/grafana/dashboards/trading-agents-dashboard.json"
    if not os.path.exists(dashboard_file):
        print("❌ Trading agents dashboard not found")
        return False
    
    try:
        with open(dashboard_file, 'r') as f:
            dashboard = json.load(f)
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in dashboard file: {e}")
        return False
    
    # Validate dashboard structure
    if "dashboard" not in dashboard:
        print("❌ Invalid dashboard structure")
        return False
    
    dashboard_obj = dashboard["dashboard"]
    if "panels" not in dashboard_obj or len(dashboard_obj["panels"]) == 0:
        print("❌ No panels configured in dashboard")
        return False
    
    print("✅ Grafana configuration validated")
    return True

def validate_phoenix_dashboard_manager():
    """Validate Phoenix Dashboard Manager implementation."""
    print("🔍 Validating Phoenix Dashboard Manager...")
    
    manager_file = "wheel_trader/phoenix_dashboard.py"
    if not os.path.exists(manager_file):
        print("❌ Phoenix Dashboard Manager not found")
        return False
    
    with open(manager_file, 'r') as f:
        content = f.read()
    
    # Check for required class and methods
    required_elements = [
        "class PhoenixDashboardManager:",
        "def setup_custom_dashboard(",
        "def _create_dashboard_layout(",
        "def _configure_data_sources(",
        "def get_dashboard_status(",
        "def create_dashboard_manager(",
        "def setup_phoenix_dashboard(",
        "def get_dashboard_health("
    ]
    
    for element in required_elements:
        if element not in content:
            print(f"❌ Required element not found: {element}")
            return False
    
    print("✅ Phoenix Dashboard Manager validated")
    return True

def validate_setup_script():
    """Validate setup script."""
    print("🔍 Validating setup script...")
    
    setup_script = "phoenix/setup-phoenix.sh"
    if not os.path.exists(setup_script):
        print("❌ Setup script not found")
        return False
    
    # Check if script is executable
    if not os.access(setup_script, os.X_OK):
        print("❌ Setup script is not executable")
        return False
    
    with open(setup_script, 'r') as f:
        content = f.read()
    
    # Check for required functions
    required_functions = [
        "check_docker()",
        "check_docker_compose()",
        "create_directories()",
        "start_services()",
        "wait_for_services()"
    ]
    
    for function in required_functions:
        if function not in content:
            print(f"❌ Required function not found: {function}")
            return False
    
    print("✅ Setup script validated")
    return True

def validate_documentation():
    """Validate documentation."""
    print("🔍 Validating documentation...")
    
    doc_file = "docs/phoenix_setup.md"
    if not os.path.exists(doc_file):
        print("❌ Phoenix setup documentation not found")
        return False
    
    with open(doc_file, 'r') as f:
        content = f.read()
    
    # Check for required sections
    required_sections = [
        "# Phoenix Observability Setup Guide",
        "## 🎯 Overview",
        "## 🚀 Quick Start",
        "## 📊 Service Architecture",
        "## 🔧 Configuration",
        "## 📈 Custom Dashboards",
        "## 🔗 Integration with Trading Agents",
        "## 🛠️ Troubleshooting"
    ]
    
    for section in required_sections:
        if section not in content:
            print(f"❌ Required documentation section not found: {section}")
            return False
    
    print("✅ Documentation validated")
    return True

def validate_directory_structure():
    """Validate directory structure."""
    print("🔍 Validating directory structure...")
    
    required_dirs = [
        "phoenix",
        "phoenix/custom_config",
        "phoenix/init-scripts",
        "phoenix/grafana",
        "phoenix/grafana/datasources",
        "phoenix/grafana/dashboards"
    ]
    
    for directory in required_dirs:
        if not os.path.exists(directory):
            print(f"❌ Required directory not found: {directory}")
            return False
    
    print("✅ Directory structure validated")
    return True

def validate_integration_points():
    """Validate integration with Phase 1 components."""
    print("🔍 Validating integration points...")
    
    # Check that dashboard references Phase 1 tables
    dashboard_file = "phoenix/grafana/dashboards/trading-agents-dashboard.json"
    
    with open(dashboard_file, 'r') as f:
        content = f.read()
    
    # Check for Phase 1 table references
    phase1_tables = ["agent_metrics", "memory_embeddings", "agent_health"]
    
    for table in phase1_tables:
        if table not in content:
            print(f"⚠️ Phase 1 table '{table}' not referenced in dashboard")
    
    # Check Phoenix Dashboard Manager imports
    manager_file = "wheel_trader/phoenix_dashboard.py"
    
    with open(manager_file, 'r') as f:
        content = f.read()
    
    # Check for Supabase integration
    if "from supabase import" not in content:
        print("❌ Supabase integration not found in dashboard manager")
        return False
    
    if "config.SUPABASE_URL" not in content:
        print("❌ Supabase configuration not referenced")
        return False
    
    print("✅ Integration points validated")
    return True

def generate_phase2_report():
    """Generate comprehensive Phase 2 validation report."""
    print("\n" + "="*60)
    print("📊 PHASE 2 VALIDATION REPORT")
    print("="*60)
    
    validation_functions = [
        ("Directory Structure", validate_directory_structure),
        ("Docker Compose Config", validate_docker_compose),
        ("Phoenix Configuration", validate_phoenix_config),
        ("Database Initialization", validate_database_init),
        ("Grafana Configuration", validate_grafana_config),
        ("Dashboard Manager", validate_phoenix_dashboard_manager),
        ("Setup Script", validate_setup_script),
        ("Documentation", validate_documentation),
        ("Integration Points", validate_integration_points)
    ]
    
    print("\n📋 VALIDATION RESULTS:")
    print("-" * 40)
    
    passed = 0
    total = len(validation_functions)
    results = {}
    
    for test_name, test_function in validation_functions:
        try:
            result = test_function()
            results[test_name] = result
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{test_name:<25} {status}")
            if result:
                passed += 1
        except Exception as e:
            results[test_name] = False
            print(f"{test_name:<25} ❌ ERROR: {e}")
    
    print("-" * 40)
    print(f"Overall Success Rate: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 PHASE 2 IMPLEMENTATION COMPLETE!")
        print("✅ All validation checks passed")
        print("✅ Phoenix observability infrastructure ready")
        print("✅ Ready to proceed to Phase 3")
    else:
        print(f"\n⚠️ PHASE 2 PARTIALLY COMPLETE")
        print(f"✅ {passed} checks passed, ❌ {total-passed} checks failed")
        print("🔧 Review failed checks before proceeding")
    
    # Generate summary
    summary = {
        "validation_timestamp": datetime.now().isoformat(),
        "phase": "Phase 2: Observability Infrastructure",
        "total_checks": total,
        "passed_checks": passed,
        "success_rate": passed/total,
        "status": "COMPLETE" if passed == total else "PARTIAL",
        "results": results,
        "components_validated": [
            "Docker Compose configuration",
            "Phoenix YAML configuration", 
            "Database initialization scripts",
            "Grafana dashboards and datasources",
            "Phoenix Dashboard Manager",
            "Setup automation script",
            "Comprehensive documentation",
            "Integration with Phase 1 components"
        ]
    }
    
    # Save validation report
    os.makedirs("activity", exist_ok=True)
    with open("activity/phase2_validation_report.json", "w") as f:
        json.dump(summary, f, indent=2)
    
    print(f"\n📄 Detailed report saved to: activity/phase2_validation_report.json")
    
    return passed == total

if __name__ == "__main__":
    print("🚀 Starting Phase 2 Validation...")
    print("=" * 60)
    
    success = generate_phase2_report()
    
    if success:
        print("\n🎯 NEXT STEPS:")
        print("1. Run './phoenix/setup-phoenix.sh' to start Phoenix services")
        print("2. Access Phoenix UI at http://localhost:6006")
        print("3. Access Grafana at http://localhost:3001")
        print("4. Proceed to Phase 3: Monitoring & Reporting")
        exit(0)
    else:
        print("\n🔧 REQUIRED ACTIONS:")
        print("1. Review and fix failed validation checks")
        print("2. Re-run validation before proceeding")
        exit(1)
