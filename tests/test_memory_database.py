#!/usr/bin/env python3
"""
Test suite for memory database functions

Tests the SQL functions and schema defined in 003_memory_system.sql
including similarity search, consolidation, and analytics functions.

Note: These tests require a PostgreSQL database with pgvector extension.
They can be run against a test database or mocked for CI/CD environments.
"""

import os
import sys
import unittest
from unittest.mock import Mock, patch
import json
from datetime import datetime, timedelta

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    import psycopg2
    from psycopg2.extras import RealDictCursor
    PSYCOPG2_AVAILABLE = True
except ImportError:
    PSYCOPG2_AVAILABLE = False
    print("Warning: psycopg2 not available. Database tests will be mocked.")


class TestMemoryDatabaseSchema(unittest.TestCase):
    """Test memory database schema and functions"""
    
    @classmethod
    def setUpClass(cls):
        """Set up test database connection"""
        cls.db_available = False
        cls.conn = None
        
        if PSYCOPG2_AVAILABLE:
            try:
                # Try to connect to test database
                # In production, use environment variables for connection
                cls.conn = psycopg2.connect(
                    host=os.getenv('TEST_DB_HOST', 'localhost'),
                    database=os.getenv('TEST_DB_NAME', 'test_memory'),
                    user=os.getenv('TEST_DB_USER', 'postgres'),
                    password=os.getenv('TEST_DB_PASSWORD', 'password'),
                    port=os.getenv('TEST_DB_PORT', '5432')
                )
                cls.db_available = True
                print("✅ Connected to test database")
            except Exception as e:
                print(f"⚠️  Could not connect to test database: {e}")
                print("   Tests will run in mock mode")
    
    @classmethod
    def tearDownClass(cls):
        """Clean up database connection"""
        if cls.conn:
            cls.conn.close()
    
    def setUp(self):
        """Set up test data"""
        if self.db_available:
            self.cursor = self.conn.cursor(cursor_factory=RealDictCursor)
            # Clean up any existing test data
            self.cursor.execute("DELETE FROM memory_embeddings WHERE agent_name LIKE 'test_%'")
            self.conn.commit()
    
    def tearDown(self):
        """Clean up after each test"""
        if self.db_available:
            # Clean up test data
            self.cursor.execute("DELETE FROM memory_embeddings WHERE agent_name LIKE 'test_%'")
            self.conn.commit()
            self.cursor.close()
    
    def test_memory_embeddings_table_structure(self):
        """Test that memory_embeddings table has correct structure"""
        if not self.db_available:
            self.skipTest("Database not available")
        
        # Check table exists and has expected columns
        self.cursor.execute("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns
            WHERE table_name = 'memory_embeddings'
            ORDER BY ordinal_position
        """)
        
        columns = {row['column_name']: row for row in self.cursor.fetchall()}
        
        # Verify key columns exist
        expected_columns = [
            'id', 'trace_id', 'span_id', 'agent_name', 'mem_type',
            'content', 'embedding', 'json_blob', 'metadata',
            'importance_score', 'created_at', 'updated_at', 'expires_at', 'tags'
        ]
        
        for col in expected_columns:
            self.assertIn(col, columns, f"Column {col} should exist")
        
        # Verify specific column properties
        self.assertEqual(columns['agent_name']['is_nullable'], 'NO')
        self.assertEqual(columns['content']['is_nullable'], 'NO')
        self.assertEqual(columns['importance_score']['data_type'], 'numeric')
    
    def test_memory_embeddings_indexes(self):
        """Test that required indexes exist"""
        if not self.db_available:
            self.skipTest("Database not available")
        
        # Check for key indexes
        self.cursor.execute("""
            SELECT indexname FROM pg_indexes 
            WHERE tablename = 'memory_embeddings'
        """)
        
        indexes = [row['indexname'] for row in self.cursor.fetchall()]
        
        expected_indexes = [
            'idx_memory_embeddings_agent_name',
            'idx_memory_embeddings_mem_type',
            'idx_memory_embeddings_created_at',
            'idx_memory_embeddings_importance',
            'idx_memory_embeddings_vector'
        ]
        
        for idx in expected_indexes:
            self.assertIn(idx, indexes, f"Index {idx} should exist")
    
    def test_insert_memory_embedding(self):
        """Test inserting memory embeddings"""
        if not self.db_available:
            self.skipTest("Database not available")
        
        # Test data
        test_embedding = [0.1] * 1536  # 1536-dimensional vector
        test_data = {
            'agent_name': 'test_agent',
            'mem_type': 'test_action',
            'content': 'Test memory content',
            'embedding': test_embedding,
            'metadata': json.dumps({'test': 'data'}),
            'importance_score': 0.8,
            'tags': ['test', 'memory']
        }
        
        # Insert test memory
        self.cursor.execute("""
            INSERT INTO memory_embeddings 
            (agent_name, mem_type, content, embedding, metadata, importance_score, tags)
            VALUES (%(agent_name)s, %(mem_type)s, %(content)s, %(embedding)s, 
                   %(metadata)s, %(importance_score)s, %(tags)s)
            RETURNING id
        """, test_data)
        
        memory_id = self.cursor.fetchone()['id']
        self.conn.commit()
        
        # Verify insertion
        self.cursor.execute("SELECT * FROM memory_embeddings WHERE id = %s", (memory_id,))
        result = self.cursor.fetchone()
        
        self.assertEqual(result['agent_name'], 'test_agent')
        self.assertEqual(result['mem_type'], 'test_action')
        self.assertEqual(result['content'], 'Test memory content')
        self.assertEqual(result['importance_score'], 0.8)
        self.assertEqual(result['tags'], ['test', 'memory'])
    
    def test_match_memories_function(self):
        """Test match_memories function"""
        if not self.db_available:
            self.skipTest("Database not available")
        
        # Insert test memories
        test_memories = [
            {
                'agent_name': 'test_agent',
                'mem_type': 'market_analysis',
                'content': 'AAPL stock analysis',
                'embedding': [0.1] * 1536,
                'importance_score': 0.9,
                'tags': ['AAPL', 'analysis']
            },
            {
                'agent_name': 'test_agent',
                'mem_type': 'options_data',
                'content': 'AAPL options chain data',
                'embedding': [0.2] * 1536,
                'importance_score': 0.7,
                'tags': ['AAPL', 'options']
            },
            {
                'agent_name': 'other_agent',
                'mem_type': 'market_analysis',
                'content': 'TSLA stock analysis',
                'embedding': [0.9] * 1536,
                'importance_score': 0.8,
                'tags': ['TSLA', 'analysis']
            }
        ]
        
        for memory in test_memories:
            self.cursor.execute("""
                INSERT INTO memory_embeddings 
                (agent_name, mem_type, content, embedding, importance_score, tags)
                VALUES (%(agent_name)s, %(mem_type)s, %(content)s, %(embedding)s, 
                       %(importance_score)s, %(tags)s)
            """, memory)
        
        self.conn.commit()
        
        # Test match_memories function
        query_embedding = [0.15] * 1536  # Should be similar to first two memories
        
        self.cursor.execute("""
            SELECT * FROM match_memories(
                query_embedding := %s::vector,
                agent_filter := 'test_agent',
                match_threshold := 0.5,
                match_count := 5
            )
        """, (query_embedding,))
        
        results = self.cursor.fetchall()
        
        # Should return memories for test_agent only, ordered by similarity
        self.assertGreater(len(results), 0)
        for result in results:
            self.assertEqual(result['agent_name'], 'test_agent')
            self.assertGreater(result['similarity'], 0.5)
    
    def test_search_memories_by_content_function(self):
        """Test search_memories_by_content function"""
        if not self.db_available:
            self.skipTest("Database not available")
        
        # Insert test memory
        self.cursor.execute("""
            INSERT INTO memory_embeddings 
            (agent_name, mem_type, content, embedding, importance_score)
            VALUES ('test_agent', 'market_analysis', 'Apple stock showing strong momentum', 
                   %s, 0.8)
        """, ([0.1] * 1536,))
        
        self.conn.commit()
        
        # Test content search
        self.cursor.execute("""
            SELECT * FROM search_memories_by_content(
                search_text := 'Apple',
                agent_filter := 'test_agent'
            )
        """)
        
        results = self.cursor.fetchall()
        
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]['agent_name'], 'test_agent')
        self.assertIn('Apple', results[0]['content'])
    
    def test_consolidate_memories_function(self):
        """Test consolidate_memories function"""
        if not self.db_available:
            self.skipTest("Database not available")
        
        # Insert similar memories
        similar_embedding = [0.1] * 1536
        
        memories = [
            {
                'agent_name': 'test_agent',
                'content': 'AAPL analysis version 1',
                'embedding': similar_embedding,
                'created_at': datetime.now() - timedelta(hours=2)
            },
            {
                'agent_name': 'test_agent', 
                'content': 'AAPL analysis version 2',
                'embedding': [x + 0.001 for x in similar_embedding],  # Very similar
                'created_at': datetime.now() - timedelta(hours=1)
            }
        ]
        
        for memory in memories:
            self.cursor.execute("""
                INSERT INTO memory_embeddings 
                (agent_name, content, embedding, created_at)
                VALUES (%(agent_name)s, %(content)s, %(embedding)s, %(created_at)s)
            """, memory)
        
        self.conn.commit()
        
        # Count memories before consolidation
        self.cursor.execute("SELECT COUNT(*) FROM memory_embeddings WHERE agent_name = 'test_agent'")
        count_before = self.cursor.fetchone()[0]
        
        # Test consolidation
        self.cursor.execute("""
            SELECT consolidate_memories(
                similarity_threshold := 0.99,
                agent_filter := 'test_agent'
            )
        """)
        
        consolidated_count = self.cursor.fetchone()[0]
        self.conn.commit()
        
        # Count memories after consolidation
        self.cursor.execute("SELECT COUNT(*) FROM memory_embeddings WHERE agent_name = 'test_agent'")
        count_after = self.cursor.fetchone()[0]
        
        # Should have consolidated at least one memory
        self.assertGreater(consolidated_count, 0)
        self.assertLess(count_after, count_before)
    
    def test_cleanup_expired_memories_function(self):
        """Test cleanup_expired_memories function"""
        if not self.db_available:
            self.skipTest("Database not available")
        
        # Insert expired memory
        expired_time = datetime.now() - timedelta(hours=1)
        
        self.cursor.execute("""
            INSERT INTO memory_embeddings 
            (agent_name, content, embedding, expires_at)
            VALUES ('test_agent', 'Expired memory', %s, %s)
        """, ([0.1] * 1536, expired_time))
        
        self.conn.commit()
        
        # Test cleanup
        self.cursor.execute("SELECT cleanup_expired_memories()")
        deleted_count = self.cursor.fetchone()[0]
        self.conn.commit()
        
        # Should have deleted the expired memory
        self.assertGreater(deleted_count, 0)
        
        # Verify memory was deleted
        self.cursor.execute("""
            SELECT COUNT(*) FROM memory_embeddings 
            WHERE agent_name = 'test_agent' AND expires_at <= NOW()
        """)
        remaining_expired = self.cursor.fetchone()[0]
        self.assertEqual(remaining_expired, 0)
    
    def test_memory_stats_view(self):
        """Test memory_stats view"""
        if not self.db_available:
            self.skipTest("Database not available")
        
        # Insert test memories with different types and importance
        test_memories = [
            ('test_agent', 'market_analysis', 0.9, ['AAPL']),
            ('test_agent', 'market_analysis', 0.8, ['TSLA']),
            ('test_agent', 'options_data', 0.7, ['AAPL', 'options']),
        ]
        
        for agent, mem_type, importance, tags in test_memories:
            self.cursor.execute("""
                INSERT INTO memory_embeddings 
                (agent_name, mem_type, content, embedding, importance_score, tags)
                VALUES (%s, %s, 'Test content', %s, %s, %s)
            """, (agent, mem_type, 'Test content', [0.1] * 1536, importance, tags))
        
        self.conn.commit()
        
        # Test memory stats view
        self.cursor.execute("""
            SELECT * FROM memory_stats 
            WHERE agent_name = 'test_agent'
            ORDER BY memory_count DESC
        """)
        
        results = self.cursor.fetchall()
        
        # Should have stats for both memory types
        self.assertGreater(len(results), 0)
        
        # Check market_analysis stats (should have 2 memories)
        market_analysis_stats = next(
            (r for r in results if r['mem_type'] == 'market_analysis'), None
        )
        self.assertIsNotNone(market_analysis_stats)
        self.assertEqual(market_analysis_stats['memory_count'], 2)
        self.assertEqual(market_analysis_stats['avg_importance'], 0.85)  # (0.9 + 0.8) / 2


class TestMemoryDatabaseMocked(unittest.TestCase):
    """Mocked tests for database functions when database is not available"""
    
    def test_database_schema_validation(self):
        """Test database schema validation logic"""
        # Mock database schema validation
        expected_columns = [
            'id', 'agent_name', 'mem_type', 'content', 'embedding',
            'metadata', 'importance_score', 'created_at', 'tags'
        ]
        
        # Simulate schema check
        schema_valid = all(col in expected_columns for col in expected_columns)
        self.assertTrue(schema_valid)
    
    def test_sql_function_syntax(self):
        """Test SQL function syntax validation"""
        # Read the SQL migration file
        sql_file_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            'sql', 'migrations', '003_memory_system.sql'
        )
        
        if os.path.exists(sql_file_path):
            with open(sql_file_path, 'r') as f:
                sql_content = f.read()
            
            # Basic syntax checks
            self.assertIn('CREATE TABLE IF NOT EXISTS memory_embeddings', sql_content)
            self.assertIn('CREATE OR REPLACE FUNCTION match_memories', sql_content)
            self.assertIn('CREATE OR REPLACE FUNCTION consolidate_memories', sql_content)
            self.assertIn('CREATE OR REPLACE FUNCTION cleanup_expired_memories', sql_content)
            self.assertIn('CREATE OR REPLACE VIEW memory_stats', sql_content)
            
            # Check for pgvector usage
            self.assertIn('vector(1536)', sql_content)
            self.assertIn('vector_cosine_ops', sql_content)
        else:
            self.skipTest("SQL migration file not found")


if __name__ == "__main__":
    print("🧪 Running Memory Database Test Suite...")
    print("=" * 60)
    
    if PSYCOPG2_AVAILABLE:
        print("📊 Database tests will run against real database if available")
    else:
        print("🔧 Database tests will run in mock mode")
    
    # Run tests
    unittest.main(verbosity=2)
