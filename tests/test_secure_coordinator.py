#!/usr/bin/env python3
"""
Comprehensive test suite for SecureCoordinator

Tests the enhanced coordinator with E2B integration, health monitoring,
memory coordination, and observability features.
"""

import os
import sys
from unittest.mock import patch

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from wheel_trader.agent_health import AgentHealthManager
from wheel_trader.secure_coordinator import SecureCoordinator
from wheel_trader.smolagents_e2b import HealthAwareAgent


class TestSecureCoordinator:
    """Test suite for SecureCoordinator functionality"""

    def setup_method(self):
        """Set up test environment before each test"""
        # Create coordinator with memory/observability disabled for basic tests
        self.coordinator = SecureCoordinator(
            health_threshold=0.7,
            memory_backend="pgvector",
            enable_observability=False  # Disable for testing
        )

    def test_coordinator_initialization(self):
        """Test SecureCoordinator initialization"""
        print("\n=== Testing SecureCoordinator Initialization ===")
        
        assert self.coordinator is not None
        assert isinstance(self.coordinator.health_manager, AgentHealthManager)
        assert self.coordinator.health_manager.health_threshold == 0.7
        
        # Check available tools
        expected_tools = ["options_data", "market_analysis", "risk_assessment"]
        for tool_name in expected_tools:
            assert tool_name in self.coordinator.available_tools
        
        # Check initial stats
        assert self.coordinator.stats["agents_created"] == 0
        assert self.coordinator.stats["executions_coordinated"] == 0
        
        print("✅ SecureCoordinator initialization test passed!")

    def test_agent_creation(self):
        """Test secure agent creation"""
        print("\n=== Testing Secure Agent Creation ===")
        
        # Create a secure agent
        agent_name = "test_agent"
        agent = self.coordinator.create_secure_agent(
            name=agent_name,
            memory_backend="pgvector",
            enable_observability=False
        )
        
        assert agent is not None
        assert isinstance(agent, HealthAwareAgent)
        assert agent_name in self.coordinator.agents
        assert self.coordinator.stats["agents_created"] == 1
        
        print(f"✅ Created secure agent '{agent_name}' successfully!")

    def test_tool_execution_directly(self):
        """Test direct tool execution without agents"""
        print("\n=== Testing Direct Tool Execution ===")
        
        # Test market analysis tool
        result = self.coordinator.execute_tool_directly(
            "market_analysis",
            symbol="AAPL",
            analysis_type="rsi"
        )
        
        assert result["success"] is True
        assert "result" in result
        assert result["tool"] == "market_analysis"
        
        print("✅ Direct tool execution test passed!")

    def test_tool_execution_invalid_tool(self):
        """Test direct tool execution with invalid tool"""
        print("\n=== Testing Invalid Tool Execution ===")

        result = self.coordinator.execute_tool_directly(
            "nonexistent_tool",
            param="value"
        )

        assert result["success"] is False
        assert "error" in result
        assert "not found" in result["error"]

        print("✅ Invalid tool execution test passed!")

    def test_health_monitoring(self):
        """Test health monitoring functionality"""
        print("\n=== Testing Health Monitoring ===")
        
        # Create an agent
        agent_name = "health_test_agent"
        agent = self.coordinator.create_secure_agent(agent_name)
        
        # Check initial health (should be healthy for new agent)
        is_healthy = self.coordinator.health_manager.is_agent_healthy(agent_name)
        print(f"Agent '{agent_name}' initial health: {is_healthy}")
        
        # Get health metrics
        metrics = self.coordinator.health_manager.get_health_metrics(agent_name)
        if metrics:
            print(f"Health score: {metrics.health_score}")
        
        print("✅ Health monitoring test passed!")

    def test_coordinator_status(self):
        """Test coordinator status reporting"""
        print("\n=== Testing Coordinator Status ===")
        
        status = self.coordinator.get_status()
        
        assert "status" in status
        assert "mode" in status
        assert "available_tools" in status
        assert "stats" in status
        assert "uptime_seconds" in status
        
        # Check that all expected tools are listed
        expected_tools = ["options_data", "market_analysis", "risk_assessment"]
        for tool in expected_tools:
            assert tool in status["available_tools"]
        
        print("✅ Coordinator status test passed!")

    @patch('wheel_trader.secure_coordinator.MEMORY_AVAILABLE', False)
    def test_coordinator_without_memory(self):
        """Test coordinator functionality without memory/observability"""
        print("\n=== Testing Coordinator Without Memory ===")
        
        # Create coordinator without memory
        coordinator = SecureCoordinator(enable_observability=False)
        
        assert coordinator.shared_memory_manager is None
        assert coordinator.observability_manager is None
        
        # Should still be able to create agents and execute tools
        agent = coordinator.create_secure_agent("no_memory_agent")
        assert agent is not None
        
        result = coordinator.execute_tool_directly(
            "market_analysis",
            symbol="AAPL",
            analysis_type="rsi"
        )
        assert result["success"] is True
        
        print("✅ Coordinator without memory test passed!")

    def test_error_handling(self):
        """Test error handling in various scenarios"""
        print("\n=== Testing Error Handling ===")
        
        # Test executing non-existent agent
        result = self.coordinator.execute_agent("nonexistent_agent", "test task")
        assert result["success"] is False
        assert "not found" in result["error"]
        
        # Test tool execution with invalid parameters
        result = self.coordinator.execute_tool_directly(
            "market_analysis",
            invalid_param="value"
        )
        # Should handle gracefully (may succeed or fail depending on tool implementation)
        assert "success" in result
        
        print("✅ Error handling test passed!")


def run_comprehensive_tests():
    """Run all SecureCoordinator tests"""
    print("🚀 Starting SecureCoordinator Comprehensive Tests...")
    
    test_instance = TestSecureCoordinator()
    tests = [
        test_instance.test_coordinator_initialization,
        test_instance.test_agent_creation,
        test_instance.test_tool_execution_directly,
        test_instance.test_tool_execution_invalid_tool,
        test_instance.test_health_monitoring,
        test_instance.test_coordinator_status,
        test_instance.test_coordinator_without_memory,
        test_instance.test_error_handling,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            test_instance.setup_method()
            test()
            passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed: {e}")
            failed += 1
    
    print("\n📊 Test Results:")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    return passed, failed


if __name__ == "__main__":
    run_comprehensive_tests()
