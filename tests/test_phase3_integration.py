"""
Phase 3 Integration Tests: Monitoring & Reporting

Tests the enhanced Grafana dashboard, enhanced nightly reporter,
and advanced monitoring system with comprehensive validation.
"""

import pytest
import asyncio
import json
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timedelta
from typing import Dict, Any, List

# Import components to test
from wheel_trader.enhanced_eval_report import (
    EnhancedEvalReporter, AgentPerformanceMetrics, MemorySystemMetrics,
    SystemHealthMetrics, generate_nightly_report, create_enhanced_reporter
)
from wheel_trader.advanced_monitoring import (
    AdvancedMonitoringSystem, MonitoringRule, Alert, AlertSeverity, AlertStatus,
    create_monitoring_system, start_system_monitoring
)


class TestEnhancedGrafanaDashboard:
    """Test enhanced Grafana dashboard configuration."""
    
    def test_enhanced_dashboard_file_exists(self):
        """Test that enhanced dashboard file exists and is valid JSON."""
        import os
        dashboard_file = "phoenix/grafana/dashboards/enhanced-trading-analytics.json"
        
        assert os.path.exists(dashboard_file), "Enhanced dashboard file not found"
        
        with open(dashboard_file, 'r') as f:
            dashboard_config = json.load(f)
        
        # Validate structure
        assert "dashboard" in dashboard_config
        dashboard = dashboard_config["dashboard"]
        
        assert "title" in dashboard
        assert "Enhanced Trading Analytics" in dashboard["title"]
        assert "panels" in dashboard
        assert len(dashboard["panels"]) >= 10, "Insufficient panels in enhanced dashboard"
    
    def test_enhanced_dashboard_panels(self):
        """Test enhanced dashboard panels configuration."""
        import os
        dashboard_file = "phoenix/grafana/dashboards/enhanced-trading-analytics.json"
        
        with open(dashboard_file, 'r') as f:
            dashboard_config = json.load(f)
        
        panels = dashboard_config["dashboard"]["panels"]
        
        # Check for required enhanced panels
        panel_titles = [panel.get("title", "") for panel in panels]
        
        required_panels = [
            "Trading Strategy Performance Matrix",
            "Memory Effectiveness by Symbol",
            "Real-Time Agent Execution Flow",
            "Hallucination Detection Trends",
            "Memory Context Quality Distribution",
            "Performance vs Memory Usage Correlation",
            "Trading Session Performance",
            "Error Pattern Analysis",
            "Memory System Health",
            "Agent Workload Distribution",
            "Latency Percentiles by Operation"
        ]
        
        for required_panel in required_panels:
            assert any(required_panel in title for title in panel_titles), \
                f"Required enhanced panel not found: {required_panel}"
    
    def test_dashboard_templating(self):
        """Test dashboard templating variables."""
        import os
        dashboard_file = "phoenix/grafana/dashboards/enhanced-trading-analytics.json"
        
        with open(dashboard_file, 'r') as f:
            dashboard_config = json.load(f)
        
        templating = dashboard_config["dashboard"].get("templating", {})
        variables = templating.get("list", [])
        
        # Check for required template variables
        variable_names = [var.get("name", "") for var in variables]
        required_variables = ["agent", "symbol", "strategy"]
        
        for var_name in required_variables:
            assert var_name in variable_names, f"Required template variable not found: {var_name}"
    
    def test_dashboard_annotations(self):
        """Test dashboard annotations configuration."""
        import os
        dashboard_file = "phoenix/grafana/dashboards/enhanced-trading-analytics.json"
        
        with open(dashboard_file, 'r') as f:
            dashboard_config = json.load(f)
        
        annotations = dashboard_config["dashboard"].get("annotations", {})
        annotation_list = annotations.get("list", [])
        
        # Check for required annotations
        annotation_names = [ann.get("name", "") for ann in annotation_list]
        required_annotations = ["High Error Rate Events", "Memory System Updates"]
        
        for ann_name in required_annotations:
            assert ann_name in annotation_names, f"Required annotation not found: {ann_name}"


class TestEnhancedEvalReporter:
    """Test enhanced evaluation reporter functionality."""
    
    @pytest.fixture
    def mock_supabase(self):
        """Mock Supabase client for testing."""
        mock_client = Mock()
        
        # Mock agent metrics response
        mock_response = Mock()
        mock_response.data = [
            {
                "agent_name": "test_agent",
                "evaluation_score": 0.85,
                "correctness_score": 0.9,
                "confidence_score": 0.8,
                "hallucination_score": 0.1,
                "memory_context_used": True,
                "execution_time_ms": 5000,
                "success": True,
                "created_at": "2025-01-11T10:00:00Z"
            },
            {
                "agent_name": "test_agent",
                "evaluation_score": 0.92,
                "correctness_score": 0.95,
                "confidence_score": 0.85,
                "hallucination_score": 0.05,
                "memory_context_used": True,
                "execution_time_ms": 4500,
                "success": True,
                "created_at": "2025-01-11T10:05:00Z"
            }
        ]
        
        mock_client.table.return_value.select.return_value.gte.return_value.lt.return_value.execute.return_value = mock_response
        mock_client.table.return_value.select.return_value.gte.return_value.lte.return_value.execute.return_value = mock_response
        mock_client.table.return_value.select.return_value.gte.return_value.lt.return_value.ilike.return_value.execute.return_value = mock_response
        
        return mock_client
    
    @pytest.fixture
    def mock_memory_manager(self):
        """Mock memory manager for testing."""
        mock_manager = Mock()
        mock_manager.get_memory_stats.return_value = {
            "total_memories": 1000,
            "avg_relevance": 0.75,
            "consolidation_rate": 0.1,
            "storage_efficiency": 0.85
        }
        return mock_manager
    
    @pytest.fixture
    def mock_dashboard_manager(self):
        """Mock dashboard manager for testing."""
        mock_manager = Mock()
        mock_manager.get_dashboard_status.return_value = {
            "status": "healthy",
            "phoenix": {"healthy": True},
            "data_sources": {
                "supabase": {"healthy": True},
                "phoenix_postgres": {"healthy": True}
            }
        }
        return mock_manager
    
    @pytest.fixture
    def enhanced_reporter(self, mock_memory_manager, mock_dashboard_manager):
        """Create enhanced reporter for testing."""
        with patch('wheel_trader.enhanced_eval_report.create_client') as mock_create_client:
            mock_create_client.return_value = Mock()
            reporter = EnhancedEvalReporter(mock_memory_manager, None, mock_dashboard_manager)
            return reporter
    
    def test_reporter_initialization(self, enhanced_reporter):
        """Test reporter initializes correctly."""
        assert enhanced_reporter.memory_manager is not None
        assert enhanced_reporter.dashboard_manager is not None
        assert enhanced_reporter.config["report_period_hours"] == 24
        assert enhanced_reporter.config["min_evaluations_for_analysis"] == 5
    
    def test_agent_performance_metrics_calculation(self, enhanced_reporter):
        """Test agent performance metrics calculation."""
        # Mock data for a single agent
        records = [
            {"evaluation_score": 0.85, "correctness_score": 0.9, "confidence_score": 0.8,
             "hallucination_score": 0.1, "memory_context_used": True, "execution_time_ms": 5000,
             "success": True, "created_at": "2025-01-11T10:00:00Z"},
            {"evaluation_score": 0.92, "correctness_score": 0.95, "confidence_score": 0.85,
             "hallucination_score": 0.05, "memory_context_used": True, "execution_time_ms": 4500,
             "success": True, "created_at": "2025-01-11T10:05:00Z"}
        ]
        
        metrics = enhanced_reporter._calculate_agent_metrics("test_agent", records)
        
        assert isinstance(metrics, AgentPerformanceMetrics)
        assert metrics.agent_name == "test_agent"
        assert metrics.total_evaluations == 2
        assert 0.8 < metrics.avg_evaluation_score < 1.0
        assert metrics.memory_utilization_rate == 1.0  # Both records used memory
        assert metrics.success_rate == 1.0  # Both successful
        assert metrics.performance_grade in ["A", "B", "C", "D", "F"]
    
    def test_performance_grade_calculation(self, enhanced_reporter):
        """Test performance grade calculation."""
        assert enhanced_reporter._calculate_performance_grade(0.95) == "A"
        assert enhanced_reporter._calculate_performance_grade(0.85) == "B"
        assert enhanced_reporter._calculate_performance_grade(0.65) == "C"
        assert enhanced_reporter._calculate_performance_grade(0.45) == "D"
        assert enhanced_reporter._calculate_performance_grade(0.25) == "F"
    
    def test_trend_direction_calculation(self, enhanced_reporter):
        """Test trend direction calculation."""
        # Improving trend
        improving_records = [
            {"evaluation_score": 0.6, "created_at": "2025-01-11T10:00:00Z"},
            {"evaluation_score": 0.65, "created_at": "2025-01-11T10:05:00Z"},
            {"evaluation_score": 0.8, "created_at": "2025-01-11T10:10:00Z"},
            {"evaluation_score": 0.85, "created_at": "2025-01-11T10:15:00Z"}
        ]
        
        trend = enhanced_reporter._calculate_trend_direction(improving_records)
        assert trend == "improving"
        
        # Stable trend
        stable_records = [
            {"evaluation_score": 0.8, "created_at": "2025-01-11T10:00:00Z"},
            {"evaluation_score": 0.82, "created_at": "2025-01-11T10:05:00Z"},
            {"evaluation_score": 0.81, "created_at": "2025-01-11T10:10:00Z"},
            {"evaluation_score": 0.83, "created_at": "2025-01-11T10:15:00Z"}
        ]
        
        trend = enhanced_reporter._calculate_trend_direction(stable_records)
        assert trend == "stable"
    
    def test_agent_recommendations_generation(self, enhanced_reporter):
        """Test agent recommendations generation."""
        # Poor performance scenario
        recommendations = enhanced_reporter._generate_agent_recommendations(
            "test_agent", 0.5, 0.4, 0.3, 0.8, 15000
        )
        
        assert len(recommendations) > 0
        assert any("improve evaluation criteria" in rec.lower() for rec in recommendations)
        assert any("hallucination" in rec.lower() for rec in recommendations)
        assert any("memory" in rec.lower() for rec in recommendations)
        assert any("performance" in rec.lower() for rec in recommendations)
        
        # Good performance scenario
        good_recommendations = enhanced_reporter._generate_agent_recommendations(
            "test_agent", 0.9, 0.1, 0.8, 0.95, 3000
        )
        
        assert len(good_recommendations) > 0
        assert any("satisfactory" in rec.lower() for rec in good_recommendations)
    
    @pytest.mark.asyncio
    async def test_comprehensive_report_generation(self, enhanced_reporter, mock_supabase):
        """Test comprehensive report generation."""
        with patch.object(enhanced_reporter, 'supabase', mock_supabase):
            # Mock the async methods
            enhanced_reporter._get_agent_performance_metrics = AsyncMock(return_value=[])
            enhanced_reporter._get_memory_system_metrics = AsyncMock(return_value=MemorySystemMetrics(
                total_memories=1000, memories_created_today=50, avg_memory_relevance=0.75,
                memory_hit_rate=0.6, top_memory_types=[("agent_action", 20)],
                memory_consolidation_rate=0.1, storage_efficiency=0.85, recommendations=[]
            ))
            enhanced_reporter._get_system_health_metrics = AsyncMock(return_value=SystemHealthMetrics(
                overall_health_score=0.9, phoenix_status="healthy", database_performance={},
                trace_ingestion_rate=100.0, error_rate=0.05, alert_count=0,
                uptime_percentage=99.9, recommendations=[]
            ))
            enhanced_reporter._get_observability_metrics = AsyncMock(return_value={})
            enhanced_reporter._get_trading_specific_metrics = AsyncMock(return_value={})
            enhanced_reporter._calculate_trend_analysis = AsyncMock(return_value={})
            enhanced_reporter._save_report = AsyncMock()
            
            report = await enhanced_reporter.generate_comprehensive_report()
            
            assert "report_metadata" in report
            assert "executive_summary" in report
            assert "memory_analytics" in report
            assert "system_health" in report
            assert "recommendations" in report
            assert "action_items" in report


class TestAdvancedMonitoringSystem:
    """Test advanced monitoring system functionality."""
    
    @pytest.fixture
    def mock_supabase(self):
        """Mock Supabase client for testing."""
        mock_client = Mock()
        mock_response = Mock()
        mock_response.data = [
            {"evaluation_score": 0.85, "memory_context_used": True, "success": True},
            {"evaluation_score": 0.92, "memory_context_used": True, "success": True}
        ]
        mock_client.table.return_value.select.return_value.gte.return_value.lte.return_value.execute.return_value = mock_response
        return mock_client
    
    @pytest.fixture
    def monitoring_system(self):
        """Create monitoring system for testing."""
        with patch('wheel_trader.advanced_monitoring.create_client') as mock_create_client:
            mock_create_client.return_value = Mock()
            system = AdvancedMonitoringSystem()
            return system
    
    def test_monitoring_system_initialization(self, monitoring_system):
        """Test monitoring system initializes correctly."""
        assert len(monitoring_system.monitoring_rules) > 0
        assert monitoring_system.config["monitoring_interval"] == 30
        assert not monitoring_system._running
        assert len(monitoring_system.active_alerts) == 0
    
    def test_default_monitoring_rules(self, monitoring_system):
        """Test default monitoring rules are created."""
        rule_names = [rule.name for rule in monitoring_system.monitoring_rules]
        
        expected_rules = [
            "high_evaluation_score_drop",
            "high_hallucination_rate",
            "low_memory_utilization",
            "high_error_rate",
            "high_latency",
            "system_health_degradation"
        ]
        
        for expected_rule in expected_rules:
            assert expected_rule in rule_names, f"Expected monitoring rule not found: {expected_rule}"
    
    def test_threshold_evaluation(self, monitoring_system):
        """Test threshold evaluation logic."""
        assert monitoring_system._evaluate_threshold(0.5, 0.7, "lt") is True
        assert monitoring_system._evaluate_threshold(0.8, 0.7, "gt") is True
        assert monitoring_system._evaluate_threshold(0.7, 0.7, "eq") is True
        assert monitoring_system._evaluate_threshold(0.7, 0.7, "gte") is True
        assert monitoring_system._evaluate_threshold(0.7, 0.7, "lte") is True
        assert monitoring_system._evaluate_threshold(0.5, 0.7, "gt") is False
    
    @pytest.mark.asyncio
    async def test_metric_value_retrieval(self, monitoring_system, mock_supabase):
        """Test metric value retrieval."""
        with patch.object(monitoring_system, 'supabase', mock_supabase):
            rule = MonitoringRule(
                name="test_rule",
                description="Test rule",
                metric_query="avg_evaluation_score",
                threshold=0.7,
                comparison="lt",
                severity=AlertSeverity.HIGH,
                evaluation_window=300,
                cooldown_period=900,
                enabled=True,
                agent_filter=None,
                notification_channels=[]
            )
            
            metric_value = await monitoring_system._get_metric_value(rule)
            assert isinstance(metric_value, float)
            assert 0.0 <= metric_value <= 1.0
    
    @pytest.mark.asyncio
    async def test_alert_triggering(self, monitoring_system):
        """Test alert triggering mechanism."""
        rule = MonitoringRule(
            name="test_alert",
            description="Test alert rule",
            metric_query="avg_evaluation_score",
            threshold=0.7,
            comparison="lt",
            severity=AlertSeverity.HIGH,
            evaluation_window=300,
            cooldown_period=900,
            enabled=True,
            agent_filter=None,
            notification_channels=["dashboard"]
        )
        
        # Mock notification sending
        monitoring_system._send_alert_notification = AsyncMock()
        
        await monitoring_system._trigger_alert(rule, 0.5, datetime.now())
        
        # Check that alert was created
        assert len(monitoring_system.active_alerts) == 1
        alert = list(monitoring_system.active_alerts.values())[0]
        assert alert.name == "test_alert"
        assert alert.severity == AlertSeverity.HIGH
        assert alert.status == AlertStatus.ACTIVE
        assert alert.metric_value == 0.5
    
    def test_alert_management(self, monitoring_system):
        """Test alert acknowledgment and suppression."""
        # Create a test alert
        alert = Alert(
            id="test_alert_001",
            name="test_alert",
            description="Test alert",
            severity=AlertSeverity.HIGH,
            status=AlertStatus.ACTIVE,
            triggered_at=datetime.now(),
            resolved_at=None,
            agent_name=None,
            metric_name="test_metric",
            metric_value=0.5,
            threshold=0.7,
            metadata={}
        )
        
        monitoring_system.active_alerts["test_alert_001"] = alert
        
        # Test acknowledgment
        result = monitoring_system.acknowledge_alert("test_alert_001")
        assert result is True
        assert monitoring_system.active_alerts["test_alert_001"].status == AlertStatus.ACKNOWLEDGED
        
        # Test suppression
        result = monitoring_system.suppress_alert("test_alert_001")
        assert result is True
        assert monitoring_system.active_alerts["test_alert_001"].status == AlertStatus.SUPPRESSED
    
    def test_monitoring_rule_management(self, monitoring_system):
        """Test adding and removing monitoring rules."""
        initial_count = len(monitoring_system.monitoring_rules)
        
        # Add new rule
        new_rule = MonitoringRule(
            name="custom_rule",
            description="Custom monitoring rule",
            metric_query="custom_metric",
            threshold=0.5,
            comparison="gt",
            severity=AlertSeverity.MEDIUM,
            evaluation_window=300,
            cooldown_period=600,
            enabled=True,
            agent_filter=None,
            notification_channels=["dashboard"]
        )
        
        result = monitoring_system.add_monitoring_rule(new_rule)
        assert result is True
        assert len(monitoring_system.monitoring_rules) == initial_count + 1
        
        # Remove rule
        result = monitoring_system.remove_monitoring_rule("custom_rule")
        assert result is True
        assert len(monitoring_system.monitoring_rules) == initial_count
    
    def test_system_status(self, monitoring_system):
        """Test system status reporting."""
        status = monitoring_system.get_system_status()
        
        assert "monitoring_active" in status
        assert "total_rules" in status
        assert "enabled_rules" in status
        assert "active_alerts" in status
        assert "critical_alerts" in status
        assert "high_alerts" in status
        assert "performance_baselines" in status
        
        assert isinstance(status["total_rules"], int)
        assert isinstance(status["enabled_rules"], int)
        assert isinstance(status["active_alerts"], int)


class TestUtilityFunctions:
    """Test utility functions for Phase 3 components."""
    
    @pytest.mark.asyncio
    async def test_generate_nightly_report_function(self):
        """Test the generate_nightly_report utility function."""
        with patch('wheel_trader.enhanced_eval_report.EnhancedEvalReporter') as mock_reporter_class:
            mock_reporter = Mock()
            mock_reporter.generate_comprehensive_report = AsyncMock(return_value={"status": "success"})
            mock_reporter_class.return_value = mock_reporter
            
            result = await generate_nightly_report()
            
            assert result["status"] == "success"
            mock_reporter.generate_comprehensive_report.assert_called_once()
    
    def test_create_enhanced_reporter_function(self):
        """Test the create_enhanced_reporter utility function."""
        with patch('wheel_trader.enhanced_eval_report.EnhancedEvalReporter') as mock_reporter_class:
            mock_reporter = Mock()
            mock_reporter_class.return_value = mock_reporter
            
            result = create_enhanced_reporter()
            
            assert result == mock_reporter
            mock_reporter_class.assert_called_once()
    
    def test_create_monitoring_system_function(self):
        """Test the create_monitoring_system utility function."""
        with patch('wheel_trader.advanced_monitoring.AdvancedMonitoringSystem') as mock_system_class:
            mock_system = Mock()
            mock_system_class.return_value = mock_system
            
            result = create_monitoring_system()
            
            assert result == mock_system
            mock_system_class.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_start_system_monitoring_function(self):
        """Test the start_system_monitoring utility function."""
        with patch('wheel_trader.advanced_monitoring.AdvancedMonitoringSystem') as mock_system_class:
            mock_system = Mock()
            mock_system.start_monitoring = AsyncMock()
            mock_system_class.return_value = mock_system
            
            result = await start_system_monitoring()
            
            assert result == mock_system
            mock_system.start_monitoring.assert_called_once()


if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v"])
