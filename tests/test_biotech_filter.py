#!/usr/bin/env python3
"""
Simple test to verify biotech filtering works
"""

import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agents.options_data import (
    is_biotech_stock, 
    market_analysis_tool, 
    filtered_market_analysis_tool
)

def test_biotech_detection():
    """Test that biotech detection works"""
    print("🧪 Testing Biotech Detection...")
    
    # Test biotech stocks
    biotech_stocks = ['MRNA', 'GILD', 'BIIB']
    for stock in biotech_stocks:
        result = is_biotech_stock(stock)
        print(f"   {stock}: {'✅ Biotech' if result else '❌ Not biotech'}")
        assert result == True, f"{stock} should be detected as biotech"
    
    # Test non-biotech stocks  
    regular_stocks = ['AAPL', 'TSLA', 'MSFT']
    for stock in regular_stocks:
        result = is_biotech_stock(stock)
        print(f"   {stock}: {'❌ Biotech' if result else '✅ Not biotech'}")
        assert result == False, f"{stock} should NOT be detected as biotech"
    
    print("✅ Biotech detection working correctly!\n")

def test_filtered_analysis():
    """Test that filtered analysis works"""
    print("🧪 Testing Filtered Analysis...")
    
    # Test 1: Regular stock should work normally
    print("   Testing AAPL (regular stock)...")
    result = filtered_market_analysis_tool("AAPL", "rsi", exclude_biotech=True)
    print(f"   AAPL result: {result.get('status', 'success')}")
    assert result.get('status') != 'filtered_out', "AAPL should not be filtered"
    
    # Test 2: Biotech stock should be filtered when exclude_biotech=True
    print("   Testing MRNA (biotech) with filtering ON...")
    result = filtered_market_analysis_tool("MRNA", "rsi", exclude_biotech=True)
    print(f"   MRNA result: {result.get('status', 'unknown')}")
    assert result.get('status') == 'filtered_out', "MRNA should be filtered out"
    
    # Test 3: Biotech stock should work when exclude_biotech=False
    print("   Testing MRNA (biotech) with filtering OFF...")
    result = filtered_market_analysis_tool("MRNA", "rsi", exclude_biotech=False)
    print(f"   MRNA result: {result.get('status', 'success')}")
    assert result.get('status') != 'filtered_out', "MRNA should not be filtered when filtering is off"
    
    print("✅ Filtered analysis working correctly!\n")

def main():
    """Run all tests"""
    print("🚀 Testing Biotech Filtering System")
    print("=" * 40)
    
    try:
        test_biotech_detection()
        test_filtered_analysis()
        
        print("🎉 All tests passed!")
        print("\n📋 Summary:")
        print("   ✅ Biotech detection works")
        print("   ✅ Filtering works when enabled")
        print("   ✅ Analysis works when filtering disabled")
        print("\n🎯 Ready for next step!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
