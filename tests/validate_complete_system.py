#!/usr/bin/env python3
"""
Complete System Validation Script

Validates the entire Memory & Observability (MEM-O11) implementation
across all three phases with comprehensive compliance checking.
"""

import os
import json
import yaml
import sys
from datetime import datetime
from typing import Dict, Any, List, <PERSON><PERSON>

def validate_original_task_compliance() -> Tuple[bool, List[str]]:
    """Validate compliance with original task.md requirements."""
    print("🔍 Validating original task compliance...")
    
    issues = []
    
    # Phase 1: Database & Core Implementation
    phase1_requirements = [
        ("agent_metrics database table", "sql/migrations/004_agent_metrics.sql"),
        ("Enhanced evaluation harness", "wheel_trader/enhanced_evaluator.py"),
        ("Memory-context integration", "wheel_trader/enhanced_evaluator.py"),
        ("Hallucination detection", "wheel_trader/enhanced_evaluator.py"),
        ("Phoenix trace integration", "wheel_trader/enhanced_evaluator.py")
    ]
    
    for requirement, file_path in phase1_requirements:
        if not os.path.exists(file_path):
            issues.append(f"Phase 1: {requirement} - file not found: {file_path}")
        else:
            with open(file_path, 'r') as f:
                content = f.read()
                if requirement == "agent_metrics database table":
                    if "CREATE TABLE IF NOT EXISTS agent_metrics" not in content:
                        issues.append(f"Phase 1: {requirement} - table creation not found")
                elif requirement == "Enhanced evaluation harness":
                    if "class EnhancedEvaluator" not in content:
                        issues.append(f"Phase 1: {requirement} - main class not found")
                elif requirement == "Memory-context integration":
                    if "_get_memory_context" not in content:
                        issues.append(f"Phase 1: {requirement} - memory integration not found")
                elif requirement == "Hallucination detection":
                    if "_detect_hallucination" not in content:
                        issues.append(f"Phase 1: {requirement} - hallucination detection not found")
                elif requirement == "Phoenix trace integration":
                    if "trace_id" not in content:
                        issues.append(f"Phase 1: {requirement} - trace integration not found")
    
    # Phase 2: Observability Infrastructure
    phase2_requirements = [
        ("Phoenix docker-compose service", "docker-compose.phoenix.yml"),
        ("Custom dashboard configuration", "phoenix/custom_config/phoenix.yaml"),
        ("Grafana integration", "phoenix/grafana/dashboards/trading-agents-dashboard.json"),
        ("Dashboard manager", "wheel_trader/phoenix_dashboard.py"),
        ("Setup automation", "phoenix/setup-phoenix.sh")
    ]
    
    for requirement, file_path in phase2_requirements:
        if not os.path.exists(file_path):
            issues.append(f"Phase 2: {requirement} - file not found: {file_path}")
        else:
            if requirement == "Phoenix docker-compose service":
                with open(file_path, 'r') as f:
                    content = f.read()
                    if "phoenix:" not in content or "phoenix-postgres:" not in content:
                        issues.append(f"Phase 2: {requirement} - required services not found")
            elif requirement == "Custom dashboard configuration":
                try:
                    with open(file_path, 'r') as f:
                        config = yaml.safe_load(f)
                        if "server" not in config or "otlp" not in config:
                            issues.append(f"Phase 2: {requirement} - required config sections missing")
                except yaml.YAMLError:
                    issues.append(f"Phase 2: {requirement} - invalid YAML")
            elif requirement == "Grafana integration":
                try:
                    with open(file_path, 'r') as f:
                        dashboard = json.load(f)
                        if "dashboard" not in dashboard or "panels" not in dashboard["dashboard"]:
                            issues.append(f"Phase 2: {requirement} - invalid dashboard structure")
                except json.JSONDecodeError:
                    issues.append(f"Phase 2: {requirement} - invalid JSON")
            elif requirement == "Dashboard manager":
                with open(file_path, 'r') as f:
                    content = f.read()
                    if "class PhoenixDashboardManager" not in content:
                        issues.append(f"Phase 2: {requirement} - main class not found")
    
    # Phase 3: Monitoring & Reporting
    phase3_requirements = [
        ("Enhanced Grafana dashboard", "phoenix/grafana/dashboards/enhanced-trading-analytics.json"),
        ("Enhanced nightly reporter", "wheel_trader/enhanced_eval_report.py"),
        ("Advanced monitoring system", "wheel_trader/advanced_monitoring.py"),
        ("Memory-aware reporting", "wheel_trader/enhanced_eval_report.py"),
        ("Alert rules and optimization", "wheel_trader/advanced_monitoring.py")
    ]
    
    for requirement, file_path in phase3_requirements:
        if not os.path.exists(file_path):
            issues.append(f"Phase 3: {requirement} - file not found: {file_path}")
        else:
            if requirement == "Enhanced Grafana dashboard":
                try:
                    with open(file_path, 'r') as f:
                        dashboard = json.load(f)
                        panels = dashboard.get("dashboard", {}).get("panels", [])
                        if len(panels) < 10:
                            issues.append(f"Phase 3: {requirement} - insufficient panels ({len(panels)} < 10)")
                except json.JSONDecodeError:
                    issues.append(f"Phase 3: {requirement} - invalid JSON")
            elif requirement == "Enhanced nightly reporter":
                with open(file_path, 'r') as f:
                    content = f.read()
                    if "class EnhancedEvalReporter" not in content:
                        issues.append(f"Phase 3: {requirement} - main class not found")
                    if "generate_comprehensive_report" not in content:
                        issues.append(f"Phase 3: {requirement} - main method not found")
            elif requirement == "Advanced monitoring system":
                with open(file_path, 'r') as f:
                    content = f.read()
                    if "class AdvancedMonitoringSystem" not in content:
                        issues.append(f"Phase 3: {requirement} - main class not found")
                    if "MonitoringRule" not in content:
                        issues.append(f"Phase 3: {requirement} - monitoring rules not found")
            elif requirement == "Memory-aware reporting":
                with open(file_path, 'r') as f:
                    content = f.read()
                    if "_get_memory_system_metrics" not in content:
                        issues.append(f"Phase 3: {requirement} - memory metrics not found")
            elif requirement == "Alert rules and optimization":
                with open(file_path, 'r') as f:
                    content = f.read()
                    if "_initialize_default_rules" not in content:
                        issues.append(f"Phase 3: {requirement} - default rules not found")
    
    success = len(issues) == 0
    if success:
        print("✅ Original task compliance validated")
    else:
        print(f"❌ {len(issues)} compliance issues found")
        for issue in issues:
            print(f"  - {issue}")
    
    return success, issues

def validate_integration_points() -> Tuple[bool, List[str]]:
    """Validate integration between all phases."""
    print("🔍 Validating integration points...")
    
    issues = []
    
    # Check Phase 1 -> Phase 2 integration
    try:
        # Enhanced evaluator should reference Phoenix traces
        with open("wheel_trader/enhanced_evaluator.py", 'r') as f:
            evaluator_content = f.read()
            if "trace_id" not in evaluator_content:
                issues.append("Phase 1->2: Enhanced evaluator missing Phoenix trace integration")
        
        # Dashboard should reference agent_metrics table
        with open("phoenix/grafana/dashboards/trading-agents-dashboard.json", 'r') as f:
            dashboard_content = f.read()
            if "agent_metrics" not in dashboard_content:
                issues.append("Phase 1->2: Dashboard missing agent_metrics integration")
    except FileNotFoundError as e:
        issues.append(f"Phase 1->2: Integration file not found: {e}")
    
    # Check Phase 2 -> Phase 3 integration
    try:
        # Enhanced reporter should use Phoenix dashboard manager
        with open("wheel_trader/enhanced_eval_report.py", 'r') as f:
            reporter_content = f.read()
            if "PhoenixDashboardManager" not in reporter_content:
                issues.append("Phase 2->3: Enhanced reporter missing Phoenix dashboard integration")
        
        # Enhanced dashboard should have advanced panels
        with open("phoenix/grafana/dashboards/enhanced-trading-analytics.json", 'r') as f:
            enhanced_dashboard = json.load(f)
            panels = enhanced_dashboard.get("dashboard", {}).get("panels", [])
            if len(panels) < 10:
                issues.append("Phase 2->3: Enhanced dashboard insufficient advanced panels")
    except (FileNotFoundError, json.JSONDecodeError) as e:
        issues.append(f"Phase 2->3: Integration validation error: {e}")
    
    # Check Phase 1 -> Phase 3 integration
    try:
        # Monitoring system should use agent_metrics
        with open("wheel_trader/advanced_monitoring.py", 'r') as f:
            monitoring_content = f.read()
            if "agent_metrics" not in monitoring_content:
                issues.append("Phase 1->3: Monitoring system missing agent_metrics integration")
        
        # Enhanced reporter should use enhanced evaluator data
        with open("wheel_trader/enhanced_eval_report.py", 'r') as f:
            reporter_content = f.read()
            if "evaluation_score" not in reporter_content:
                issues.append("Phase 1->3: Enhanced reporter missing evaluation data integration")
    except FileNotFoundError as e:
        issues.append(f"Phase 1->3: Integration file not found: {e}")
    
    success = len(issues) == 0
    if success:
        print("✅ Integration points validated")
    else:
        print(f"❌ {len(issues)} integration issues found")
        for issue in issues:
            print(f"  - {issue}")
    
    return success, issues

def validate_testing_coverage() -> Tuple[bool, List[str]]:
    """Validate comprehensive testing coverage."""
    print("🔍 Validating testing coverage...")
    
    issues = []
    
    required_test_files = [
        "tests/test_phase1_integration.py",
        "tests/test_phase2_integration.py",
        "tests/test_phase3_integration.py",
        "tests/test_complete_system_integration.py",
        "tests/validate_phase1.py",
        "tests/validate_phase2.py"
    ]
    
    for test_file in required_test_files:
        if not os.path.exists(test_file):
            issues.append(f"Missing test file: {test_file}")
        else:
            with open(test_file, 'r') as f:
                content = f.read()
                if "def test_" not in content and "def validate_" not in content:
                    issues.append(f"Test file has no test functions: {test_file}")
    
    # Check for specific test coverage
    test_coverage_requirements = [
        ("Enhanced evaluator tests", "tests/test_phase1_integration.py", "TestEnhancedEvaluator"),
        ("Phoenix dashboard tests", "tests/test_phase2_integration.py", "TestPhoenixDashboardManager"),
        ("Enhanced reporter tests", "tests/test_phase3_integration.py", "TestEnhancedEvalReporter"),
        ("Monitoring system tests", "tests/test_phase3_integration.py", "TestAdvancedMonitoringSystem"),
        ("Complete integration tests", "tests/test_complete_system_integration.py", "TestCompleteSystemIntegration")
    ]
    
    for requirement, file_path, test_class in test_coverage_requirements:
        if os.path.exists(file_path):
            with open(file_path, 'r') as f:
                content = f.read()
                if test_class not in content:
                    issues.append(f"Missing test class: {requirement} ({test_class})")
        else:
            issues.append(f"Missing test file for: {requirement}")
    
    success = len(issues) == 0
    if success:
        print("✅ Testing coverage validated")
    else:
        print(f"❌ {len(issues)} testing coverage issues found")
        for issue in issues:
            print(f"  - {issue}")
    
    return success, issues

def validate_documentation() -> Tuple[bool, List[str]]:
    """Validate documentation completeness."""
    print("🔍 Validating documentation...")
    
    issues = []
    
    required_docs = [
        "docs/phoenix_setup.md",
        "activity/phase1_completion_summary.md",
        "activity/phase2_completion_summary.md"
    ]
    
    for doc_file in required_docs:
        if not os.path.exists(doc_file):
            issues.append(f"Missing documentation: {doc_file}")
        else:
            with open(doc_file, 'r') as f:
                content = f.read()
                if len(content) < 1000:  # Minimum content length
                    issues.append(f"Documentation too brief: {doc_file}")
    
    # Check for specific documentation sections
    if os.path.exists("docs/phoenix_setup.md"):
        with open("docs/phoenix_setup.md", 'r') as f:
            content = f.read()
            required_sections = ["Overview", "Quick Start", "Configuration", "Troubleshooting"]
            for section in required_sections:
                if section not in content:
                    issues.append(f"Missing documentation section: {section}")
    
    success = len(issues) == 0
    if success:
        print("✅ Documentation validated")
    else:
        print(f"❌ {len(issues)} documentation issues found")
        for issue in issues:
            print(f"  - {issue}")
    
    return success, issues

def validate_production_readiness() -> Tuple[bool, List[str]]:
    """Validate production readiness."""
    print("🔍 Validating production readiness...")
    
    issues = []
    
    # Check for error handling
    critical_files = [
        "wheel_trader/enhanced_evaluator.py",
        "wheel_trader/enhanced_eval_report.py",
        "wheel_trader/advanced_monitoring.py",
        "wheel_trader/phoenix_dashboard.py"
    ]
    
    for file_path in critical_files:
        if os.path.exists(file_path):
            with open(file_path, 'r') as f:
                content = f.read()
                if "try:" not in content or "except" not in content:
                    issues.append(f"Missing error handling: {file_path}")
                if "logger" not in content:
                    issues.append(f"Missing logging: {file_path}")
        else:
            issues.append(f"Critical file missing: {file_path}")
    
    # Check for configuration management
    config_files = [
        "phoenix/custom_config/phoenix.yaml",
        "docker-compose.phoenix.yml"
    ]
    
    for config_file in config_files:
        if not os.path.exists(config_file):
            issues.append(f"Missing configuration: {config_file}")
    
    # Check for setup automation
    if not os.path.exists("phoenix/setup-phoenix.sh"):
        issues.append("Missing setup automation script")
    elif not os.access("phoenix/setup-phoenix.sh", os.X_OK):
        issues.append("Setup script not executable")
    
    success = len(issues) == 0
    if success:
        print("✅ Production readiness validated")
    else:
        print(f"❌ {len(issues)} production readiness issues found")
        for issue in issues:
            print(f"  - {issue}")
    
    return success, issues

def generate_final_report() -> Dict[str, Any]:
    """Generate final validation report."""
    print("\n" + "="*80)
    print("📊 COMPLETE SYSTEM VALIDATION REPORT")
    print("="*80)
    
    validation_results = {}
    
    # Run all validations
    validations = [
        ("Original Task Compliance", validate_original_task_compliance),
        ("Integration Points", validate_integration_points),
        ("Testing Coverage", validate_testing_coverage),
        ("Documentation", validate_documentation),
        ("Production Readiness", validate_production_readiness)
    ]
    
    print("\n📋 VALIDATION RESULTS:")
    print("-" * 50)
    
    total_passed = 0
    total_validations = len(validations)
    all_issues = []
    
    for validation_name, validation_func in validations:
        success, issues = validation_func()
        validation_results[validation_name] = {
            "success": success,
            "issues": issues
        }
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{validation_name:<25} {status}")
        
        if success:
            total_passed += 1
        else:
            all_issues.extend(issues)
    
    print("-" * 50)
    print(f"Overall Success Rate: {total_passed}/{total_validations} ({total_passed/total_validations*100:.1f}%)")
    
    # Generate summary
    if total_passed == total_validations:
        print("\n🎉 COMPLETE SYSTEM VALIDATION SUCCESSFUL!")
        print("✅ All validation checks passed")
        print("✅ System is ready for production deployment")
        print("✅ Original task requirements 100% satisfied")
        status = "COMPLETE"
    else:
        print(f"\n⚠️ SYSTEM VALIDATION PARTIALLY COMPLETE")
        print(f"✅ {total_passed} validations passed, ❌ {total_validations-total_passed} validations failed")
        print("🔧 Review failed validations before deployment")
        status = "PARTIAL"
    
    # Create comprehensive report
    report = {
        "validation_timestamp": datetime.now().isoformat(),
        "system": "Memory & Observability (MEM-O11) Implementation",
        "total_validations": total_validations,
        "passed_validations": total_passed,
        "success_rate": total_passed/total_validations,
        "status": status,
        "validation_results": validation_results,
        "all_issues": all_issues,
        "phases_completed": [
            "Phase 1: Database & Core Implementation",
            "Phase 2: Observability Infrastructure", 
            "Phase 3: Monitoring & Reporting"
        ],
        "components_delivered": [
            "agent_metrics database table with memory integration",
            "Enhanced evaluation harness with hallucination detection",
            "Phoenix docker-compose service with custom configuration",
            "Grafana dashboards with trading-specific visualizations",
            "Enhanced nightly reporter with memory-aware analytics",
            "Advanced monitoring system with intelligent alerting",
            "Comprehensive testing suite with integration validation",
            "Complete documentation and deployment guides"
        ]
    }
    
    # Save final report
    os.makedirs("activity", exist_ok=True)
    with open("activity/final_system_validation_report.json", "w") as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📄 Final validation report saved to: activity/final_system_validation_report.json")
    
    if status == "COMPLETE":
        print("\n🎯 SYSTEM READY FOR DEPLOYMENT:")
        print("1. Run './phoenix/setup-phoenix.sh' to start observability stack")
        print("2. Access Phoenix UI at http://localhost:6006")
        print("3. Access Grafana dashboards at http://localhost:3001")
        print("4. Monitor system health and performance")
        print("5. Review nightly reports for optimization opportunities")
    else:
        print("\n🔧 REQUIRED ACTIONS:")
        print("1. Review and fix failed validation checks")
        print("2. Re-run validation before deployment")
        print("3. Ensure all original task requirements are met")
    
    return report

if __name__ == "__main__":
    print("🚀 Starting Complete System Validation...")
    print("=" * 80)
    
    report = generate_final_report()
    
    # Exit with appropriate code
    if report["status"] == "COMPLETE":
        print("\n✅ VALIDATION COMPLETED SUCCESSFULLY! 🎉")
        exit(0)
    else:
        print("\n❌ VALIDATION FAILED - REVIEW REQUIRED")
        exit(1)
