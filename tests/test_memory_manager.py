#!/usr/bin/env python3
"""
Test suite for memory manager

Tests the MemoryManager high-level operations including memory storage,
search, health integration, and fallback mechanisms.
"""

import os
import sys
import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from wheel_trader.memory.manager import MemoryManager
from wheel_trader.memory.backends import MemoryStorageError
from wheel_trader.agent_health import AgentHealthManager


class TestMemoryManager(unittest.TestCase):
    """Test MemoryManager functionality"""
    
    def setUp(self):
        """Set up test environment"""
        # Mock backend
        self.mock_backend = Mock()
        self.mock_health_manager = Mock()
        
        # Create manager with mocked backend
        with patch('wheel_trader.memory.manager.create_memory_backend', return_value=self.mock_backend):
            self.manager = MemoryManager(
                backend="pgvector",
                agent_name="test_agent",
                health_manager=self.mock_health_manager
            )
    
    def test_initialization_success(self):
        """Test successful manager initialization"""
        self.assertEqual(self.manager.agent_name, "test_agent")
        self.assertEqual(self.manager.backend_type, "pgvector")
        self.assertIsNotNone(self.manager.backend)
        self.assertEqual(self.manager.stats["stores"], 0)
        self.assertEqual(self.manager.stats["searches"], 0)
        self.assertEqual(self.manager.stats["errors"], 0)
    
    def test_initialization_with_fallback(self):
        """Test manager initialization with fallback to pgvector"""
        # Mock backend creation failure then success
        mock_create_backend = Mock()
        mock_create_backend.side_effect = [
            Exception("mem0 failed"),  # First call fails
            self.mock_backend  # Second call succeeds
        ]
        
        with patch('wheel_trader.memory.manager.create_memory_backend', mock_create_backend):
            manager = MemoryManager(backend="mem0", agent_name="test_agent")
            
            # Should fallback to pgvector
            self.assertEqual(manager.backend_type, "pgvector")
            self.assertEqual(mock_create_backend.call_count, 2)
    
    def test_store_memory_success(self):
        """Test successful memory storage"""
        # Mock backend response
        self.mock_backend.store_memory.return_value = "test-memory-id"
        
        # Test memory storage
        memory_id = self.manager.store_memory(
            content="Test trading decision",
            metadata={"symbol": "AAPL", "action": "buy"},
            mem_type="trading_decision",
            importance=0.8,
            tags=["options", "trading"],
            trace_id="trace-123"
        )
        
        self.assertEqual(memory_id, "test-memory-id")
        self.assertEqual(self.manager.stats["stores"], 1)
        self.assertEqual(self.manager.stats["errors"], 0)
        
        # Verify backend was called with enhanced metadata
        call_args = self.mock_backend.store_memory.call_args
        self.assertEqual(call_args[1]["content"], "Test trading decision")
        self.assertEqual(call_args[1]["agent_name"], "test_agent")
        self.assertEqual(call_args[1]["mem_type"], "trading_decision")
        self.assertEqual(call_args[1]["trace_id"], "trace-123")
        
        # Check enhanced metadata
        metadata = call_args[1]["metadata"]
        self.assertEqual(metadata["importance"], 0.8)
        self.assertEqual(metadata["tags"], ["options", "trading"])
        self.assertEqual(metadata["symbol"], "AAPL")
        self.assertEqual(metadata["action"], "buy")
    
    def test_store_memory_failure(self):
        """Test memory storage failure handling"""
        # Mock backend failure
        self.mock_backend.store_memory.side_effect = Exception("Storage failed")
        
        # Test memory storage failure
        with self.assertRaises(MemoryStorageError):
            self.manager.store_memory(
                content="Test content",
                agent_name="test_agent"
            )
        
        self.assertEqual(self.manager.stats["errors"], 1)
    
    def test_search_memories_success(self):
        """Test successful memory search"""
        # Mock backend response
        mock_memories = [
            {
                "id": "memory-1",
                "content": "Previous AAPL analysis",
                "similarity": 0.9,
                "mem_type": "market_analysis",
                "importance_score": 0.8
            },
            {
                "id": "memory-2", 
                "content": "AAPL options data",
                "similarity": 0.85,
                "mem_type": "options_data",
                "importance_score": 0.7
            }
        ]
        self.mock_backend.search_memories.return_value = mock_memories
        
        # Test memory search
        memories = self.manager.search_memories(
            query="AAPL analysis",
            limit=5,
            filters={"mem_type": ["market_analysis", "options_data"]}
        )
        
        self.assertEqual(len(memories), 2)
        self.assertEqual(memories[0]["id"], "memory-1")
        self.assertEqual(memories[1]["id"], "memory-2")
        self.assertEqual(self.manager.stats["searches"], 1)
        
        # Verify backend was called correctly
        call_args = self.mock_backend.search_memories.call_args
        self.assertEqual(call_args[1]["query"], "AAPL analysis")
        self.assertEqual(call_args[1]["agent_name"], "test_agent")
        self.assertEqual(call_args[1]["limit"], 5)
    
    def test_search_memories_failure_graceful_degradation(self):
        """Test graceful degradation on search failure"""
        # Mock backend failure
        self.mock_backend.search_memories.side_effect = Exception("Search failed")
        
        # Test memory search failure - should return empty list, not raise
        memories = self.manager.search_memories(query="test query")
        
        self.assertEqual(memories, [])
        self.assertEqual(self.manager.stats["errors"], 1)
    
    def test_search_by_content(self):
        """Test content-based search"""
        # Mock backend with search_by_content method
        mock_memories = [{"id": "memory-1", "content": "Exact match content"}]
        self.mock_backend.search_by_content = Mock(return_value=mock_memories)
        
        # Test content search
        memories = self.manager.search_by_content(
            search_text="exact match",
            mem_type="market_analysis"
        )
        
        self.assertEqual(len(memories), 1)
        self.assertEqual(memories[0]["id"], "memory-1")
        
        # Verify backend method was called
        self.mock_backend.search_by_content.assert_called_once_with(
            search_text="exact match",
            agent_name="test_agent",
            mem_type="market_analysis",
            limit=10
        )
    
    def test_search_by_content_fallback(self):
        """Test fallback to regular search when backend doesn't support content search"""
        # Backend without search_by_content method
        if hasattr(self.mock_backend, 'search_by_content'):
            delattr(self.mock_backend, 'search_by_content')
        
        mock_memories = [{"id": "memory-1", "content": "Fallback search result"}]
        self.mock_backend.search_memories.return_value = mock_memories
        
        # Test content search fallback
        memories = self.manager.search_by_content(
            search_text="test",
            mem_type="market_analysis"
        )
        
        self.assertEqual(len(memories), 1)
        # Should have called regular search_memories as fallback
        self.mock_backend.search_memories.assert_called_once()
    
    def test_get_recent_memories(self):
        """Test getting recent memories"""
        mock_memories = [{"id": "recent-1", "created_at": "2025-01-10T10:00:00Z"}]
        self.mock_backend.search_memories.return_value = mock_memories
        
        # Test recent memories
        memories = self.manager.get_recent_memories(hours=24, limit=10)
        
        self.assertEqual(len(memories), 1)
        
        # Verify correct filters were applied
        call_args = self.mock_backend.search_memories.call_args
        filters = call_args[1]["filters"]
        self.assertEqual(filters["time_window_hours"], 24)
    
    def test_get_important_memories(self):
        """Test getting high-importance memories"""
        mock_memories = [{"id": "important-1", "importance_score": 0.9}]
        self.mock_backend.search_memories.return_value = mock_memories
        
        # Test important memories
        memories = self.manager.get_important_memories(min_importance=0.8, limit=5)
        
        self.assertEqual(len(memories), 1)
        
        # Verify correct filters were applied
        call_args = self.mock_backend.search_memories.call_args
        filters = call_args[1]["filters"]
        self.assertEqual(filters["min_importance"], 0.8)
    
    def test_consolidate_memories(self):
        """Test memory consolidation"""
        # Mock backend with consolidate_memories method
        self.mock_backend.consolidate_memories = Mock(return_value=5)
        
        # Test consolidation
        count = self.manager.consolidate_memories(
            similarity_threshold=0.95,
            max_consolidations=50
        )
        
        self.assertEqual(count, 5)
        self.mock_backend.consolidate_memories.assert_called_once_with(
            agent_name="test_agent",
            similarity_threshold=0.95,
            max_consolidations=50
        )
    
    def test_consolidate_memories_unsupported(self):
        """Test consolidation when backend doesn't support it"""
        # Backend without consolidate_memories method
        if hasattr(self.mock_backend, 'consolidate_memories'):
            delattr(self.mock_backend, 'consolidate_memories')
        
        # Test consolidation
        count = self.manager.consolidate_memories()
        
        self.assertEqual(count, 0)  # Should return 0 when unsupported
    
    def test_cleanup_expired_memories(self):
        """Test expired memory cleanup"""
        # Mock backend with cleanup method
        self.mock_backend.cleanup_expired_memories = Mock(return_value=3)
        
        # Test cleanup
        count = self.manager.cleanup_expired_memories()
        
        self.assertEqual(count, 3)
        self.mock_backend.cleanup_expired_memories.assert_called_once()
    
    def test_get_health_status(self):
        """Test comprehensive health status"""
        # Mock backend health
        mock_backend_health = {
            "backend_type": "pgvector",
            "healthy": True,
            "success_rate": 0.95,
            "avg_latency_ms": 50
        }
        self.mock_backend.get_health_status.return_value = mock_backend_health
        
        # Add some operations to stats
        self.manager.stats["stores"] = 10
        self.manager.stats["searches"] = 20
        self.manager.stats["errors"] = 1
        
        # Test health status
        health = self.manager.get_health_status()
        
        self.assertTrue(health["manager_healthy"])
        self.assertEqual(health["agent_name"], "test_agent")
        self.assertEqual(health["backend_type"], "pgvector")
        self.assertEqual(health["backend_health"], mock_backend_health)
        
        # Check manager stats
        manager_stats = health["manager_stats"]
        self.assertEqual(manager_stats["total_operations"], 30)
        self.assertEqual(manager_stats["stores"], 10)
        self.assertEqual(manager_stats["searches"], 20)
        self.assertEqual(manager_stats["errors"], 1)
        self.assertAlmostEqual(manager_stats["success_rate"], 29/30, places=2)
    
    def test_get_memory_stats(self):
        """Test memory statistics"""
        # Mock backend health
        mock_backend_health = {"healthy": True}
        self.mock_backend.get_health_status.return_value = mock_backend_health
        
        # Test memory stats
        stats = self.manager.get_memory_stats()
        
        self.assertEqual(stats["agent_name"], "test_agent")
        self.assertEqual(stats["backend_type"], "pgvector")
        self.assertIn("operations", stats)
        self.assertEqual(stats["backend_health"], mock_backend_health)
    
    def test_importance_score_clamping(self):
        """Test that importance scores are clamped to valid range"""
        self.mock_backend.store_memory.return_value = "test-id"
        
        # Test with importance > 1.0
        self.manager.store_memory(
            content="Test content",
            importance=1.5  # Should be clamped to 1.0
        )
        
        call_args = self.mock_backend.store_memory.call_args
        metadata = call_args[1]["metadata"]
        self.assertEqual(metadata["importance"], 1.0)
        
        # Test with importance < 0.0
        self.manager.store_memory(
            content="Test content",
            importance=-0.5  # Should be clamped to 0.0
        )
        
        call_args = self.mock_backend.store_memory.call_args
        metadata = call_args[1]["metadata"]
        self.assertEqual(metadata["importance"], 0.0)


class TestMemoryManagerIntegration(unittest.TestCase):
    """Integration tests for MemoryManager"""
    
    def test_manager_with_health_integration(self):
        """Test manager integration with health manager"""
        mock_backend = Mock()
        mock_health_manager = Mock(spec=AgentHealthManager)
        
        with patch('wheel_trader.memory.manager.create_memory_backend', return_value=mock_backend):
            manager = MemoryManager(
                backend="pgvector",
                agent_name="test_agent",
                health_manager=mock_health_manager
            )
            
            self.assertEqual(manager.health_manager, mock_health_manager)
    
    def test_manager_without_health_integration(self):
        """Test manager without health manager"""
        mock_backend = Mock()
        
        with patch('wheel_trader.memory.manager.create_memory_backend', return_value=mock_backend):
            manager = MemoryManager(
                backend="pgvector",
                agent_name="test_agent"
            )
            
            self.assertIsNone(manager.health_manager)
    
    def test_end_to_end_memory_operations(self):
        """Test end-to-end memory operations"""
        mock_backend = Mock()
        
        # Mock store and search operations
        mock_backend.store_memory.return_value = "stored-memory-id"
        mock_backend.search_memories.return_value = [
            {"id": "stored-memory-id", "content": "Test content", "similarity": 0.9}
        ]
        
        with patch('wheel_trader.memory.manager.create_memory_backend', return_value=mock_backend):
            manager = MemoryManager(backend="pgvector", agent_name="test_agent")
            
            # Store a memory
            memory_id = manager.store_memory(
                content="Test trading analysis",
                metadata={"symbol": "AAPL"},
                mem_type="market_analysis",
                importance=0.8
            )
            
            # Search for memories
            memories = manager.search_memories(query="trading analysis")
            
            # Verify operations
            self.assertEqual(memory_id, "stored-memory-id")
            self.assertEqual(len(memories), 1)
            self.assertEqual(memories[0]["id"], "stored-memory-id")
            
            # Check statistics
            self.assertEqual(manager.stats["stores"], 1)
            self.assertEqual(manager.stats["searches"], 1)
            self.assertEqual(manager.stats["errors"], 0)


if __name__ == "__main__":
    print("🧪 Running Memory Manager Test Suite...")
    print("=" * 60)
    
    # Run tests
    unittest.main(verbosity=2)
