"""
Complete System Integration Tests

Tests the integration of all three phases:
- Phase 1: Database & Core Implementation
- Phase 2: Observability Infrastructure  
- Phase 3: Monitoring & Reporting

Validates end-to-end functionality and compliance with original task requirements.
"""

import pytest
import asyncio
import json
import os
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List

# Import all components
from wheel_trader.enhanced_evaluator import EnhancedEvaluator, evaluate_run
from wheel_trader.phoenix_dashboard import PhoenixDashboardManager, setup_phoenix_dashboard
from wheel_trader.enhanced_eval_report import EnhancedEvalReporter, generate_nightly_report
from wheel_trader.advanced_monitoring import AdvancedMonitoringSystem, start_system_monitoring


class TestCompleteSystemIntegration:
    """Test complete system integration across all phases."""
    
    @pytest.fixture
    def mock_supabase(self):
        """Mock Supabase client with comprehensive data."""
        mock_client = Mock()
        
        # Mock agent_metrics table data
        agent_metrics_data = [
            {
                "id": "eval-001",
                "agent_name": "options_trader",
                "task_description": "Analyze AAPL covered call opportunities",
                "evaluation_score": 0.85,
                "correctness_score": 0.9,
                "confidence_score": 0.8,
                "hallucination_score": 0.1,
                "context_relevance_score": 0.75,
                "memory_context_used": True,
                "memory_context_count": 3,
                "execution_time_ms": 5000,
                "success": True,
                "trace_id": "trace-123",
                "created_at": "2025-01-11T10:00:00Z"
            },
            {
                "id": "eval-002", 
                "agent_name": "risk_analyzer",
                "task_description": "Calculate portfolio risk metrics",
                "evaluation_score": 0.92,
                "correctness_score": 0.95,
                "confidence_score": 0.88,
                "hallucination_score": 0.05,
                "context_relevance_score": 0.82,
                "memory_context_used": True,
                "memory_context_count": 2,
                "execution_time_ms": 3500,
                "success": True,
                "trace_id": "trace-124",
                "created_at": "2025-01-11T10:05:00Z"
            }
        ]
        
        # Mock memory_embeddings table data
        memory_data = [
            {
                "id": "mem-001",
                "content": "AAPL options analysis shows high volatility",
                "mem_type": "market_analysis",
                "importance": 0.8,
                "metadata": {"symbol": "AAPL", "strategy": "covered_call"},
                "created_at": "2025-01-11T09:00:00Z"
            },
            {
                "id": "mem-002",
                "content": "Risk management requires careful position sizing",
                "mem_type": "risk_management",
                "importance": 0.9,
                "metadata": {"topic": "risk", "category": "general"},
                "created_at": "2025-01-11T09:30:00Z"
            }
        ]
        
        # Mock agent_health table data
        health_data = [
            {
                "agent_name": "options_trader",
                "health_score": 0.85,
                "last_updated": "2025-01-11T10:00:00Z"
            },
            {
                "agent_name": "risk_analyzer", 
                "health_score": 0.92,
                "last_updated": "2025-01-11T10:05:00Z"
            }
        ]
        
        # Configure mock responses based on table
        def mock_table(table_name):
            mock_table_obj = Mock()
            
            if table_name == "agent_metrics":
                mock_table_obj.select.return_value.execute.return_value.data = agent_metrics_data
                mock_table_obj.select.return_value.gte.return_value.execute.return_value.data = agent_metrics_data
                mock_table_obj.select.return_value.gte.return_value.lt.return_value.execute.return_value.data = agent_metrics_data
                mock_table_obj.select.return_value.gte.return_value.lte.return_value.execute.return_value.data = agent_metrics_data
                mock_table_obj.insert.return_value.execute.return_value.data = [{"id": "new-eval-id"}]
            elif table_name == "memory_embeddings":
                mock_table_obj.select.return_value.execute.return_value.data = memory_data
                mock_table_obj.select.return_value.gte.return_value.execute.return_value.data = memory_data
            elif table_name == "agent_health":
                mock_table_obj.select.return_value.execute.return_value.data = health_data
            
            return mock_table_obj
        
        mock_client.table = mock_table
        return mock_client
    
    @pytest.fixture
    def mock_memory_manager(self):
        """Mock memory manager with realistic data."""
        mock_manager = Mock()
        mock_manager.search_memories.return_value = [
            {
                "id": "mem-001",
                "content": "AAPL options analysis shows high volatility",
                "similarity": 0.85,
                "metadata": {"symbol": "AAPL", "strategy": "covered_call"}
            },
            {
                "id": "mem-002",
                "content": "Risk management requires careful position sizing",
                "similarity": 0.72,
                "metadata": {"topic": "risk", "category": "general"}
            }
        ]
        mock_manager.store_memory.return_value = "new-memory-id"
        mock_manager.get_memory_stats.return_value = {
            "total_memories": 1000,
            "avg_relevance": 0.75,
            "consolidation_rate": 0.1,
            "storage_efficiency": 0.85
        }
        return mock_manager
    
    @pytest.fixture
    def mock_tracer(self):
        """Mock agent tracer with trace data."""
        mock_tracer = Mock()
        mock_tracer.get_trace_stats.return_value = {
            "total_traces": 150,
            "avg_duration_ms": 4500,
            "success_rate": 0.95,
            "avg_spans_per_trace": 8,
            "recent_traces": [
                {"trace_id": "trace-123", "duration_ms": 5000},
                {"trace_id": "trace-124", "duration_ms": 3500}
            ]
        }
        return mock_tracer
    
    def test_phase1_database_schema_exists(self):
        """Test that Phase 1 database schema files exist."""
        required_files = [
            "sql/migrations/004_agent_metrics.sql"
        ]
        
        for file_path in required_files:
            assert os.path.exists(file_path), f"Phase 1 file not found: {file_path}"
    
    def test_phase2_observability_files_exist(self):
        """Test that Phase 2 observability files exist."""
        required_files = [
            "docker-compose.phoenix.yml",
            "phoenix/custom_config/phoenix.yaml",
            "phoenix/grafana/dashboards/trading-agents-dashboard.json",
            "wheel_trader/phoenix_dashboard.py"
        ]
        
        for file_path in required_files:
            assert os.path.exists(file_path), f"Phase 2 file not found: {file_path}"
    
    def test_phase3_monitoring_files_exist(self):
        """Test that Phase 3 monitoring files exist."""
        required_files = [
            "phoenix/grafana/dashboards/enhanced-trading-analytics.json",
            "wheel_trader/enhanced_eval_report.py",
            "wheel_trader/advanced_monitoring.py"
        ]
        
        for file_path in required_files:
            assert os.path.exists(file_path), f"Phase 3 file not found: {file_path}"
    
    def test_enhanced_evaluator_integration(self, mock_supabase, mock_memory_manager, mock_tracer):
        """Test enhanced evaluator integration with all systems."""
        with patch('wheel_trader.enhanced_evaluator.create_client', return_value=mock_supabase):
            evaluator = EnhancedEvaluator(mock_memory_manager, mock_tracer)
            
            # Test comprehensive evaluation
            result = evaluator.evaluate_with_memory_context(
                agent_name="options_trader",
                task="Analyze AAPL covered call opportunities",
                result="AAPL shows bullish momentum. Recommend covered call with $155 strike.",
                trace_id="trace-123",
                execution_time_ms=5000
            )
            
            # Verify integration points
            assert "evaluation_score" in result
            assert "memory_context_count" in result
            assert "evaluation_id" in result
            assert result["memory_context_count"] == 2  # From mock memory manager
            
            # Verify memory manager was called
            mock_memory_manager.search_memories.assert_called_once()
            mock_memory_manager.store_memory.assert_called_once()
    
    def test_phoenix_dashboard_integration(self, mock_supabase):
        """Test Phoenix dashboard integration."""
        with patch('wheel_trader.phoenix_dashboard.create_client', return_value=mock_supabase):
            dashboard_manager = PhoenixDashboardManager()
            
            # Test dashboard setup
            result = dashboard_manager.setup_custom_dashboard()
            
            assert result["status"] == "success"
            assert "dashboard_url" in result
            assert "panels_configured" in result
            
            # Test dashboard status
            status = dashboard_manager.get_dashboard_status()
            assert "status" in status
            assert "recent_metrics" in status
    
    @pytest.mark.asyncio
    async def test_enhanced_reporter_integration(self, mock_supabase, mock_memory_manager, mock_tracer):
        """Test enhanced reporter integration with all systems."""
        with patch('wheel_trader.enhanced_eval_report.create_client', return_value=mock_supabase):
            dashboard_manager = Mock()
            dashboard_manager.get_dashboard_status.return_value = {
                "status": "healthy",
                "phoenix": {"healthy": True},
                "data_sources": {"supabase": {"healthy": True}}
            }
            
            reporter = EnhancedEvalReporter(mock_memory_manager, mock_tracer, dashboard_manager)
            
            # Mock async methods to avoid complex setup
            reporter._save_report = AsyncMock()
            reporter._send_report_notification = AsyncMock()
            
            # Test report generation
            report = await reporter.generate_comprehensive_report()
            
            assert "report_metadata" in report
            assert "executive_summary" in report
            assert "agent_performance" in report
            assert "memory_analytics" in report
            assert "system_health" in report
            assert "recommendations" in report
    
    @pytest.mark.asyncio
    async def test_monitoring_system_integration(self, mock_supabase):
        """Test monitoring system integration."""
        with patch('wheel_trader.advanced_monitoring.create_client', return_value=mock_supabase):
            dashboard_manager = Mock()
            dashboard_manager.get_dashboard_status.return_value = {
                "phoenix": {"healthy": True}
            }
            
            monitoring = AdvancedMonitoringSystem(None, None, dashboard_manager)
            
            # Test system initialization
            assert len(monitoring.monitoring_rules) > 0
            
            # Test metric retrieval
            rule = monitoring.monitoring_rules[0]  # Get first rule
            metric_value = await monitoring._get_metric_value(rule)
            assert isinstance(metric_value, float)
            
            # Test system status
            status = monitoring.get_system_status()
            assert "monitoring_active" in status
            assert "total_rules" in status
    
    def test_backward_compatibility(self, mock_supabase):
        """Test backward compatibility with existing evaluator interface."""
        with patch('wheel_trader.enhanced_evaluator.create_client', return_value=mock_supabase):
            # Test the backward compatibility function
            result = evaluate_run(
                agent_name="test_agent",
                agent_version="1.0",
                result={
                    "task": "Test task",
                    "exit_code": 0,
                    "duration_ms": 5000,
                    "output": "Test output"
                }
            )
            
            # Verify backward compatibility
            assert "correctness" in result
            assert "latency_ms" in result
            assert "hallucination_score" in result
            assert "enhanced_metrics" in result  # New enhanced features
    
    def test_data_flow_integration(self, mock_supabase, mock_memory_manager, mock_tracer):
        """Test complete data flow from evaluation to monitoring."""
        with patch('wheel_trader.enhanced_evaluator.create_client', return_value=mock_supabase):
            with patch('wheel_trader.advanced_monitoring.create_client', return_value=mock_supabase):
                # 1. Enhanced Evaluator creates evaluation
                evaluator = EnhancedEvaluator(mock_memory_manager, mock_tracer)
                eval_result = evaluator.evaluate_with_memory_context(
                    agent_name="test_agent",
                    task="Test task",
                    result="Test result",
                    trace_id="test-trace"
                )
                
                # 2. Phoenix Dashboard can access the data
                dashboard_manager = PhoenixDashboardManager()
                dashboard_status = dashboard_manager.get_dashboard_status()
                
                # 3. Monitoring system can monitor the metrics
                monitoring = AdvancedMonitoringSystem()
                system_status = monitoring.get_system_status()
                
                # Verify data flow
                assert eval_result["evaluation_score"] is not None
                assert dashboard_status["status"] is not None
                assert system_status["total_rules"] > 0
    
    def test_original_task_requirements_compliance(self):
        """Test compliance with original task.md requirements."""
        
        # Check Phase 1 requirements
        phase1_files = [
            "sql/migrations/004_agent_metrics.sql",  # agent_metrics table
            "wheel_trader/enhanced_evaluator.py"     # enhanced evaluation harness
        ]
        
        for file_path in phase1_files:
            assert os.path.exists(file_path), f"Phase 1 requirement not met: {file_path}"
        
        # Check Phase 2 requirements  
        phase2_files = [
            "docker-compose.phoenix.yml",            # Phoenix docker-compose
            "phoenix/custom_config/phoenix.yaml",    # Custom dashboard config
            "wheel_trader/phoenix_dashboard.py"      # Dashboard integration
        ]
        
        for file_path in phase2_files:
            assert os.path.exists(file_path), f"Phase 2 requirement not met: {file_path}"
        
        # Check Phase 3 requirements
        phase3_files = [
            "phoenix/grafana/dashboards/enhanced-trading-analytics.json",  # Enhanced Grafana
            "wheel_trader/enhanced_eval_report.py",                        # Enhanced reporter
            "wheel_trader/advanced_monitoring.py"                          # Advanced monitoring
        ]
        
        for file_path in phase3_files:
            assert os.path.exists(file_path), f"Phase 3 requirement not met: {file_path}"
    
    def test_comprehensive_testing_coverage(self):
        """Test that comprehensive testing is implemented."""
        test_files = [
            "tests/test_phase1_integration.py",
            "tests/test_phase2_integration.py", 
            "tests/test_phase3_integration.py",
            "tests/validate_phase1.py",
            "tests/validate_phase2.py"
        ]
        
        for test_file in test_files:
            assert os.path.exists(test_file), f"Test file not found: {test_file}"
    
    def test_documentation_completeness(self):
        """Test that comprehensive documentation exists."""
        doc_files = [
            "docs/phoenix_setup.md",
            "activity/phase1_completion_summary.md",
            "activity/phase2_completion_summary.md"
        ]
        
        for doc_file in doc_files:
            assert os.path.exists(doc_file), f"Documentation file not found: {doc_file}"
    
    @pytest.mark.asyncio
    async def test_end_to_end_workflow(self, mock_supabase, mock_memory_manager, mock_tracer):
        """Test complete end-to-end workflow."""
        with patch('wheel_trader.enhanced_evaluator.create_client', return_value=mock_supabase):
            with patch('wheel_trader.enhanced_eval_report.create_client', return_value=mock_supabase):
                with patch('wheel_trader.advanced_monitoring.create_client', return_value=mock_supabase):
                    
                    # 1. Agent executes task with enhanced evaluation
                    evaluator = EnhancedEvaluator(mock_memory_manager, mock_tracer)
                    eval_result = evaluator.evaluate_with_memory_context(
                        agent_name="end_to_end_agent",
                        task="Complete workflow test",
                        result="Workflow executed successfully",
                        trace_id="e2e-trace-001"
                    )
                    
                    # 2. Phoenix dashboard displays real-time metrics
                    dashboard_manager = PhoenixDashboardManager()
                    dashboard_result = dashboard_manager.setup_custom_dashboard()
                    
                    # 3. Monitoring system tracks performance
                    monitoring = AdvancedMonitoringSystem()
                    monitoring_status = monitoring.get_system_status()
                    
                    # 4. Enhanced reporter generates comprehensive report
                    reporter = EnhancedEvalReporter(mock_memory_manager, mock_tracer, dashboard_manager)
                    reporter._save_report = AsyncMock()
                    reporter._send_report_notification = AsyncMock()
                    
                    report = await reporter.generate_comprehensive_report()
                    
                    # Verify end-to-end workflow
                    assert eval_result["evaluation_score"] is not None
                    assert dashboard_result["status"] == "success"
                    assert monitoring_status["total_rules"] > 0
                    assert report["executive_summary"]["status"] is not None
                    
                    # Verify integration points
                    assert eval_result["memory_context_count"] > 0  # Memory integration
                    assert eval_result["evaluation_id"] is not None  # Database integration
                    assert dashboard_result["panels_configured"] > 0  # Dashboard integration
                    assert report["recommendations"] is not None  # Reporting integration


class TestSystemPerformance:
    """Test system performance and scalability."""
    
    def test_evaluation_performance(self, mock_supabase, mock_memory_manager):
        """Test evaluation performance under load."""
        with patch('wheel_trader.enhanced_evaluator.create_client', return_value=mock_supabase):
            evaluator = EnhancedEvaluator(mock_memory_manager)
            
            # Simulate multiple evaluations
            start_time = datetime.now()
            
            for i in range(10):
                result = evaluator.evaluate_with_memory_context(
                    agent_name=f"perf_agent_{i}",
                    task=f"Performance test task {i}",
                    result=f"Performance test result {i}"
                )
                assert result["evaluation_score"] is not None
            
            end_time = datetime.now()
            total_time = (end_time - start_time).total_seconds()
            
            # Should complete 10 evaluations in reasonable time
            assert total_time < 5.0, f"Performance test took too long: {total_time}s"
    
    def test_memory_usage_efficiency(self, mock_memory_manager):
        """Test memory usage efficiency."""
        # Test that memory manager is called efficiently
        with patch('wheel_trader.enhanced_evaluator.create_client'):
            evaluator = EnhancedEvaluator(mock_memory_manager)
            
            # Multiple evaluations should reuse memory manager efficiently
            for i in range(5):
                evaluator._get_memory_context(f"agent_{i}", f"task_{i}")
            
            # Should have called search_memories for each evaluation
            assert mock_memory_manager.search_memories.call_count == 5


if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v"])
