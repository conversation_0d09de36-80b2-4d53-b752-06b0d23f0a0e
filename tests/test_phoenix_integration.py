#!/usr/bin/env python3
"""
Test script for Phoenix observability integration

This script validates that Phoenix OTEL is properly configured and working
with the coveredcalls-agents project.
"""

import os
import sys
import time
import logging
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_phoenix_imports():
    """Test that Phoenix packages can be imported"""
    logger.info("Testing Phoenix package imports...")
    
    try:
        from phoenix.otel import register
        logger.info("✅ phoenix.otel.register imported successfully")
    except ImportError as e:
        logger.error(f"❌ Failed to import phoenix.otel: {e}")
        return False
    
    try:
        from opentelemetry import trace
        from opentelemetry.trace import Status, StatusCode
        logger.info("✅ OpenTelemetry imports successful")
    except ImportError as e:
        logger.error(f"❌ Failed to import OpenTelemetry: {e}")
        return False
    
    try:
        import openinference.instrumentation.openai
        logger.info("✅ OpenInference OpenAI instrumentation available")
    except ImportError as e:
        logger.warning(f"⚠️ OpenInference OpenAI instrumentation not available: {e}")
    
    try:
        import openinference.instrumentation.langchain
        logger.info("✅ OpenInference LangChain instrumentation available")
    except ImportError as e:
        logger.warning(f"⚠️ OpenInference LangChain instrumentation not available: {e}")
    
    return True

def test_phoenix_configuration():
    """Test Phoenix OTEL configuration"""
    logger.info("Testing Phoenix OTEL configuration...")
    
    try:
        from phoenix.otel import register
        from opentelemetry import trace
        
        # Configure Phoenix with test settings
        tracer_provider = register(
            project_name="test-project",
            auto_instrument=False,  # Disable auto-instrument for testing
            batch=True,
            endpoint="http://localhost:6006/v1/traces"
        )
        
        # Get a tracer
        tracer = tracer_provider.get_tracer(__name__)
        logger.info("✅ Phoenix OTEL configured successfully")
        
        # Test span creation
        with tracer.start_as_current_span("test_span") as span:
            span.set_attribute("test.attribute", "test_value")
            span.set_attribute("test.timestamp", datetime.now().isoformat())
            time.sleep(0.1)  # Simulate some work
            
        logger.info("✅ Test span created successfully")
        return True
        
    except Exception as e:
        logger.error(f"❌ Phoenix OTEL configuration failed: {e}")
        return False

def test_agent_tracer():
    """Test the AgentTracer class"""
    logger.info("Testing AgentTracer class...")
    
    try:
        from wheel_trader.observability.tracer import AgentTracer
        
        # Create an agent tracer
        tracer = AgentTracer("test_agent", "test_session")
        logger.info("✅ AgentTracer created successfully")
        
        # Test trace execution
        with tracer.trace_execution("test_operation", {"test_key": "test_value"}) as span:
            span.set_attribute("test.operation", "validation")
            time.sleep(0.05)  # Simulate work
            
        logger.info("✅ AgentTracer trace execution successful")
        
        # Get stats
        stats = tracer.get_trace_stats()
        logger.info(f"✅ AgentTracer stats: {stats}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ AgentTracer test failed: {e}")
        return False

def test_observability_manager():
    """Test the ObservabilityManager class"""
    logger.info("Testing ObservabilityManager class...")
    
    try:
        from wheel_trader.observability import ObservabilityManager
        
        # Create observability manager
        manager = ObservabilityManager("test_agent")
        logger.info("✅ ObservabilityManager created successfully")
        
        # Start a session
        session_started = manager.start_session("test_session")
        if session_started:
            logger.info("✅ ObservabilityManager session started")
        else:
            logger.warning("⚠️ ObservabilityManager session start returned False")
        
        # Test tracing
        with manager.trace_agent_operation("test_operation") as span:
            span.set_attribute("test.manager", "validation")
            time.sleep(0.05)
            
        logger.info("✅ ObservabilityManager tracing successful")
        
        # Get stats
        stats = manager.get_observability_stats()
        logger.info(f"✅ ObservabilityManager stats: {stats}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ ObservabilityManager test failed: {e}")
        return False

def main():
    """Run all Phoenix integration tests"""
    logger.info("🚀 Starting Phoenix Integration Tests")
    logger.info("=" * 50)
    
    tests = [
        ("Phoenix Imports", test_phoenix_imports),
        ("Phoenix Configuration", test_phoenix_configuration),
        ("AgentTracer", test_agent_tracer),
        ("ObservabilityManager", test_observability_manager),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running test: {test_name}")
        logger.info("-" * 30)
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                logger.info(f"✅ {test_name} PASSED")
            else:
                logger.error(f"❌ {test_name} FAILED")
                
        except Exception as e:
            logger.error(f"💥 {test_name} CRASHED: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("📊 TEST SUMMARY")
    logger.info("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status} - {test_name}")
    
    logger.info(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Phoenix integration is working correctly.")
        return 0
    else:
        logger.error("⚠️ Some tests failed. Check the logs above for details.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
