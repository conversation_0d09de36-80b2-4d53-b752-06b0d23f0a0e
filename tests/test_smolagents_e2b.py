#!/usr/bin/env python3
"""
Test suite for native smolagents + E2B integration

Tests the native smolagents E2B support with health monitoring.
"""

import os
import sys

from wheel_trader.smolagents_e2b import (
    HealthAwareAgent,
    create_trading_agent,
    create_multi_agent_system,
    options_data_tool,
    market_analysis_tool,
    risk_assessment_tool
)
from wheel_trader.secure_coordinator import SecureCoordinator
from wheel_trader.agent_health import AgentHealthManager


def test_native_tools():
    """Test the native smolagents tools"""
    print("\n=== Testing Native smolagents Tools ===")

    try:
        # Test options data tool
        result = options_data_tool(symbol="AAPL", expiry_date="2024-12-20")
        print(f"Options data result: {result}")
        assert isinstance(result, dict), "Result should be a dictionary"
        assert "status" in result, "Result should have status field"

        # Test market analysis tool
        result = market_analysis_tool(symbol="AAPL", analysis_type="rsi")
        print(f"Market analysis result: {result}")
        assert isinstance(result, dict), "Result should be a dictionary"
        assert result.get("analysis_type") == "rsi", "Should return RSI analysis"

        # Test risk assessment tool
        result = risk_assessment_tool(symbol="AAPL", position_size=1000, portfolio_value=50000)
        print(f"Risk assessment result: {result}")
        assert isinstance(result, dict), "Result should be a dictionary"
        assert "risk_level" in result, "Should contain risk level"

        print("✅ Native tools test passed!")

    except Exception as e:
        print(f"❌ Test failed: {e}")
        raise


def test_health_aware_agent_creation():
    """Test creating health-aware agents"""
    print("\n=== Testing Health-Aware Agent Creation ===")

    try:
        # Create a simple agent
        agent = HealthAwareAgent(
            name="test_agent",
            tools=[options_data_tool, market_analysis_tool]
        )

        print(f"Agent created: {agent.name}")

        # Check health status
        health_status = agent.get_health_status()
        print(f"Health status: {health_status}")

        assert health_status["agent_name"] == "test_agent", "Agent name should match"
        assert health_status["is_healthy"], "New agent should be healthy"

        print("✅ Health-aware agent creation test passed!")

    except Exception as e:
        print(f"❌ Test failed: {e}")
        raise


def test_trading_agent_creation():
    """Test creating specialized trading agent"""
    print("\n=== Testing Trading Agent Creation ===")

    try:
        # Create trading agent with all tools
        agent = create_trading_agent("trading_test")

        print(f"Trading agent created: {agent.name}")

        # Check health status
        health_status = agent.get_health_status()
        print(f"Health status: {health_status}")

        assert health_status["agent_name"] == "trading_test", "Agent name should match"
        assert health_status["is_healthy"], "New agent should be healthy"

        print("✅ Trading agent creation test passed!")

    except Exception as e:
        print(f"❌ Test failed: {e}")
        raise


def test_multi_agent_system():
    """Test creating multi-agent system"""
    print("\n=== Testing Multi-Agent System ===")

    try:
        # Create multi-agent system
        agents = create_multi_agent_system()

        print(f"Created {len(agents)} agents:")
        for name, agent in agents.items():
            print(f"  - {name}: {agent.name}")
            health_status = agent.get_health_status()
            assert health_status["is_healthy"], f"Agent {name} should be healthy"

        expected_agents = ["data_agent", "analysis_agent", "risk_agent", "coordinator_agent"]
        for expected in expected_agents:
            assert expected in agents, f"Should have {expected}"

        print("✅ Multi-agent system test passed!")

    except Exception as e:
        print(f"❌ Test failed: {e}")
        raise


def test_secure_coordinator():
    """Test the secure coordinator with native agents"""
    print("\n=== Testing Secure Coordinator ===")

    try:
        coordinator = SecureCoordinator()

        # Test tool execution
        result = coordinator.execute_tool_directly(
            "market_analysis",
            symbol="AAPL",
            analysis_type="sma"
        )

        print(f"Direct tool execution result: {result}")
        assert result["success"], f"Tool execution should succeed: {result.get('error')}"

        # Test agent creation
        agent = coordinator.create_secure_agent("test_coordinator_agent")
        print(f"Created agent: {agent.name}")

        # Test agent status
        status = coordinator.get_agent_status()
        print(f"Coordinator status: {status}")

        assert "available_tools" in status, "Should list available tools"
        assert "market_analysis" in status["available_tools"], "Should include market analysis tool"
        assert "test_coordinator_agent" in status["registered_agents"], "Should include created agent"

        print("✅ Secure coordinator test passed!")

    except Exception as e:
        print(f"❌ Test failed: {e}")
        raise


def test_agent_health_manager():
    """Test agent health management"""
    print("\n=== Testing Agent Health Manager ===")

    try:
        health_manager = AgentHealthManager()

        # Test health check for new agent
        is_healthy = health_manager.is_agent_healthy("new_test_agent")
        print(f"New agent health status: {is_healthy}")
        assert is_healthy, "New agents should be considered healthy"

        # Test getting health metrics
        metrics = health_manager.get_health_metrics("new_test_agent")
        print(f"Health metrics for new agent: {metrics}")

        # Test getting all agent health
        all_health = health_manager.get_all_agent_health()
        print(f"All agent health count: {len(all_health)}")

        print("✅ Agent health manager test passed!")

    except Exception as e:
        print(f"❌ Test failed: {e}")
        raise


def test_agent_execution_simulation():
    """Test agent execution simulation (without actual LLM calls)"""
    print("\n=== Testing Agent Execution Simulation ===")

    # Skip if no OpenAI API key (since we can't actually run the agent)
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠️  Skipping agent execution test - OPENAI_API_KEY not set")
        return

    try:
        # Create agent
        agent = create_trading_agent("execution_test")

        print(f"Agent created for execution test: {agent.name}")

        # Test health status before execution
        health_before = agent.get_health_status()
        print(f"Health before execution: {health_before}")

        # Note: We can't actually run the agent without proper model setup
        # This test just verifies the agent can be created and health checked

        print("✅ Agent execution simulation test passed!")

    except Exception as e:
        print(f"❌ Test failed: {e}")
        # Don't raise for this test since it requires external API
        print("Note: This test requires OpenAI API key and may fail due to external dependencies")


def run_all_tests():
    """Run all tests"""
    print("🚀 Starting Native smolagents + E2B Integration Tests...")

    tests = [
        test_native_tools,
        test_health_aware_agent_creation,
        test_trading_agent_creation,
        test_multi_agent_system,
        test_secure_coordinator,
        test_agent_health_manager,
        test_agent_execution_simulation
    ]

    passed = 0
    failed = 0

    for test in tests:
        try:
            test()
            passed += 1
        except Exception as e:
            print(f"❌ {test.__name__} failed: {e}")
            failed += 1

    print("\n📊 Test Results:")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {passed/(passed+failed)*100:.1f}%")

    if failed == 0:
        print("\n🎉 All tests passed!")
    else:
        print(f"\n⚠️  {failed} test(s) failed")
        sys.exit(1)


if __name__ == "__main__":
    run_all_tests()
