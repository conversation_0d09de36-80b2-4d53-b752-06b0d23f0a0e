#!/usr/bin/env python3
"""
Memory System Test Runner

Comprehensive test runner for the memory system components.
Runs all memory-related tests and provides detailed reporting.
"""

import os
import sys
import unittest
import time
from io import StringIO

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def run_test_suite():
    """Run the complete memory system test suite"""
    
    print("🧪 Memory System Test Suite")
    print("=" * 60)
    print(f"Python version: {sys.version}")
    print(f"Test directory: {os.path.dirname(os.path.abspath(__file__))}")
    print()
    
    # Discover and run tests
    loader = unittest.TestLoader()
    start_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Load specific memory tests
    test_modules = [
        'test_memory_backends',
        'test_memory_manager', 
        'test_memory_database'
    ]
    
    suite = unittest.TestSuite()
    
    for module in test_modules:
        try:
            tests = loader.loadTestsFromName(module)
            suite.addTests(tests)
            print(f"✅ Loaded tests from {module}")
        except Exception as e:
            print(f"❌ Failed to load tests from {module}: {e}")
    
    print()
    
    # Run tests with detailed output
    runner = unittest.TextTestRunner(
        verbosity=2,
        stream=sys.stdout,
        buffer=True
    )
    
    start_time = time.time()
    result = runner.run(suite)
    end_time = time.time()
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Skipped: {len(result.skipped) if hasattr(result, 'skipped') else 0}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%" if result.testsRun > 0 else "N/A")
    print(f"Execution time: {end_time - start_time:.2f} seconds")
    
    if result.failures:
        print(f"\n❌ FAILURES ({len(result.failures)}):")
        for test, traceback in result.failures:
            print(f"  - {test}")
    
    if result.errors:
        print(f"\n💥 ERRORS ({len(result.errors)}):")
        for test, traceback in result.errors:
            print(f"  - {test}")
    
    if result.wasSuccessful():
        print("\n🎉 ALL TESTS PASSED!")
        return True
    else:
        print(f"\n⚠️  SOME TESTS FAILED")
        return False


def run_quick_validation():
    """Run a quick validation of the memory system"""
    
    print("🚀 Quick Memory System Validation")
    print("=" * 40)
    
    try:
        # Test imports
        print("📦 Testing imports...")
        from wheel_trader.memory import (
            MemoryBackend, PgVectorBackend, Mem0Backend, 
            MemoryManager, create_memory_backend
        )
        print("✅ All imports successful")
        
        # Test backend creation
        print("🔧 Testing backend creation...")
        
        # Test factory function
        try:
            with unittest.mock.patch('wheel_trader.memory.backends.create_client'):
                backend = create_memory_backend("pgvector")
                print("✅ PgVectorBackend creation successful")
        except Exception as e:
            print(f"❌ PgVectorBackend creation failed: {e}")
        
        try:
            with unittest.mock.patch('wheel_trader.memory.backends.Memory'):
                backend = create_memory_backend("mem0")
                print("✅ Mem0Backend creation successful")
        except Exception as e:
            print(f"❌ Mem0Backend creation failed: {e}")
        
        # Test manager creation
        print("🎯 Testing manager creation...")
        try:
            with unittest.mock.patch('wheel_trader.memory.manager.create_memory_backend'):
                manager = MemoryManager(backend="pgvector", agent_name="test_agent")
                print("✅ MemoryManager creation successful")
        except Exception as e:
            print(f"❌ MemoryManager creation failed: {e}")
        
        print("\n🎉 Quick validation completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n💥 Quick validation failed: {e}")
        return False


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Memory System Test Runner")
    parser.add_argument("--quick", action="store_true", help="Run quick validation only")
    parser.add_argument("--verbose", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    if args.quick:
        success = run_quick_validation()
    else:
        success = run_test_suite()
    
    sys.exit(0 if success else 1)
