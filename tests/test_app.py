#!/usr/bin/env python
import sys
import os
import pytest
from flask import json
from unittest.mock import patch

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from app import app

@pytest.fixture
def client():
    app.config['TESTING'] = True
    with app.test_client() as client:
        yield client

def test_home_page(client):
    """Test that home page loads"""
    response = client.get('/')
    assert response.status_code == 200
    assert b"Covered Calls Agent Platform" in response.data

@patch('agent_coordinator.coordinator.run_in_sandbox')
@patch('agent_coordinator.coordinator.create_client')
@patch('evaluator.create_client')
@patch('exec_manager_e2b.create_client')
@patch('observability.save_memory_func')
@patch('observability.px.trace')
def test_execute_task_endpoint(mock_px_trace, mock_save_memory, mock_exec_create_client, mock_eval_create_client, mock_create_client, mock_run_in_sandbox, client):
    """Test the /execute_task endpoint with mocked sandbox and database."""
    # Mock the Supabase client that will be created
    mock_supabase = mock_create_client.return_value
    # Mock the health check to return a healthy score
    mock_supabase.table.return_value.select.return_value.eq.return_value.eq.return_value.execute.return_value.data = [{'score': 0.9}]
    
    # Mock the result of the sandbox execution
    mock_run_in_sandbox.return_value = {
        "stdout": "Processed text from agent",
        "stderr": "",
        "exit_code": 0,
        "duration_ms": 123
    }

    test_data = {"task": "Test task"}
    response = client.post('/execute_task',
                         data=json.dumps(test_data),
                         content_type='application/json')

    assert response.status_code == 200
    data = json.loads(response.data)
    assert data['task'] == 'Test task'
    assert data['result'] == 'Processed text from agent'
    assert data['exit_code'] == 0

@patch('agent_coordinator.coordinator.get_agent_health')
@patch('evaluator.create_client')
@patch('exec_manager_e2b.create_client')
@patch('observability.save_memory_func')
@patch('observability.px.trace')
def test_health_gate_unhealthy(mock_px_trace, mock_save_memory, mock_exec_create_client, mock_eval_create_client, mock_get_agent_health, client):
    """Test that the health gate blocks an unhealthy agent."""
    # Directly mock the get_agent_health function to return an unhealthy score
    mock_get_agent_health.return_value = 0.5

    test_data = {"task": "Test task"}
    response = client.post('/execute_task',
                         data=json.dumps(test_data),
                         content_type='application/json')

    assert response.status_code == 503
    data = json.loads(response.data)
    assert "is unhealthy" in data['error']

if __name__ == '__main__':
    pytest.main(['-v'])
