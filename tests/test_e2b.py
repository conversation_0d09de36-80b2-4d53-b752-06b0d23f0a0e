#!/usr/bin/env python3

import sys
import os
from wheel_trader.exec_manager_e2b import run_code_string, run_job


def test_basic_execution():
    """Test basic Python execution in e2b sandbox"""

    # Create a simple test script
    test_script = """
import sys
print("Hello from e2b sandbox!")
print(f"Python version: {sys.version}")
print("Test completed successfully")
"""

    # Write test script to file
    with open("test_script.py", "w") as f:
        f.write(test_script)

    try:
        # Run the script in e2b
        result = run_job("test_agent", "test_script.py")

        print("=== E2B Execution Result ===")
        print(f"Success: {result['success']}")
        print(f"Exit Code: {result['exit_code']}")
        print(f"Duration: {result['duration_ms']}ms")
        print(f"CPU Time: {result['cpu_ms']}ms")
        print(f"Memory Peak: {result['mem_peak']} MiB")
        print(f"Container ID: {result['container_id']}")
        print(f"Job Name: {result['job_name']}")

        print("\n=== Output ===")
        print(result['output'])

        if result['error']:
            print("\n=== Error ===")
            print(result['error'])

        # Verify basic functionality
        assert result['success'], f"Execution failed: {result['error']}"
        assert "Hello from e2b sandbox!" in result['output']
        assert result['exit_code'] == 0

        print("\n✅ Basic execution test passed!")

    finally:
        # Clean up
        if os.path.exists("test_script.py"):
            os.remove("test_script.py")


def test_code_string_execution():
    """Test direct code string execution"""

    test_code = """
import math
result = math.sqrt(16)
print(f"Square root of 16 is: {result}")
print("Code string execution successful")
"""

    result = run_code_string("string_test_agent", test_code)

    print("\n=== Code String Execution Test ===")
    print(f"Success: {result['success']}")
    print(f"Output: {result['output']}")

    assert result['success'], f"Code string execution failed: {result['error']}"
    assert "Square root of 16 is: 4" in result['output']

    print("✅ Code string execution test passed!")


if __name__ == "__main__":
    print("Testing E2B Integration...")

    try:
        test_basic_execution()
        test_code_string_execution()
        print("\n🎉 All tests passed!")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        sys.exit(1)
