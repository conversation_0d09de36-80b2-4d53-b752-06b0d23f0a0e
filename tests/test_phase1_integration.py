"""
Comprehensive tests for Phase 1: Database & Core Implementation

Tests the agent_metrics database table and enhanced evaluation harness
with memory-context integration.
"""

from unittest.mock import Mock, patch

import pytest

# Import the components we're testing
from wheel_trader.enhanced_evaluator import EnhancedEvaluator, evaluate_run
from wheel_trader.memory.manager import MemoryManager
from wheel_trader.observability.tracer import AgentTracer


class TestAgentMetricsDatabase:
    """Test the agent_metrics database table functionality."""
    
    @pytest.fixture
    def mock_supabase(self):
        """Mock Supabase client for testing."""
        mock_client = Mock()
        mock_response = Mock()
        mock_response.data = [{"id": "test-evaluation-id"}]
        mock_client.table.return_value.insert.return_value.execute.return_value = mock_response
        return mock_client
    
    def test_agent_metrics_table_structure(self):
        """Test that the agent_metrics table has the correct structure."""
        # This would typically be tested against a test database
        # For now, we'll test the expected structure
        expected_columns = [
            "id", "agent_name", "agent_version", "task_description", "task_hash",
            "metrics_json", "execution_time_ms", "memory_context_used", "memory_ids",
            "memory_context_count", "trace_id", "span_id", "evaluation_score",
            "correctness_score", "hallucination_score", "context_relevance_score",
            "confidence_score", "latency_category", "success", "error_message",
            "created_at", "updated_at"
        ]
        
        # In a real test, you would query the database schema
        # For this test, we'll just verify the expected structure exists
        assert len(expected_columns) == 22
        assert "agent_name" in expected_columns
        assert "metrics_json" in expected_columns
        assert "memory_context_used" in expected_columns
    
    def test_database_constraints(self):
        """Test database constraints are properly defined."""
        # Test score constraints (0.0 to 1.0)
        valid_scores = [0.0, 0.5, 1.0]
        invalid_scores = [-0.1, 1.1, 2.0]
        
        for score in valid_scores:
            assert 0.0 <= score <= 1.0
        
        for score in invalid_scores:
            assert not (0.0 <= score <= 1.0)
        
        # Test latency categories
        valid_categories = ['fast', 'normal', 'slow', 'timeout']
        invalid_categories = ['very_fast', 'extremely_slow', 'unknown']
        
        for category in valid_categories:
            assert category in ['fast', 'normal', 'slow', 'timeout']
        
        for category in invalid_categories:
            assert category not in ['fast', 'normal', 'slow', 'timeout']


class TestEnhancedEvaluator:
    """Test the EnhancedEvaluator class functionality."""
    
    @pytest.fixture
    def mock_memory_manager(self):
        """Mock MemoryManager for testing."""
        mock_manager = Mock(spec=MemoryManager)
        mock_manager.search_memories.return_value = [
            {
                "id": "memory-1",
                "content": "Previous AAPL analysis showed bullish momentum",
                "similarity": 0.85,
                "metadata": {"symbol": "AAPL", "signal": "bullish"}
            },
            {
                "id": "memory-2", 
                "content": "Options trading requires careful risk management",
                "similarity": 0.72,
                "metadata": {"topic": "risk_management"}
            }
        ]
        mock_manager.store_memory.return_value = "new-memory-id"
        return mock_manager
    
    @pytest.fixture
    def mock_tracer(self):
        """Mock AgentTracer for testing."""
        mock_tracer = Mock(spec=AgentTracer)
        mock_tracer.get_trace_stats.return_value = {
            "recent_traces": [
                {"trace_id": "test-trace-123", "duration_ms": 5000}
            ]
        }
        return mock_tracer
    
    @pytest.fixture
    def mock_supabase(self):
        """Mock Supabase client for testing."""
        mock_client = Mock()
        mock_response = Mock()
        mock_response.data = [{"id": "test-evaluation-id"}]
        mock_client.table.return_value.insert.return_value.execute.return_value = mock_response
        return mock_client
    
    @pytest.fixture
    def evaluator(self, mock_memory_manager, mock_tracer):
        """Create EnhancedEvaluator instance for testing."""
        with patch('wheel_trader.enhanced_evaluator.create_client') as mock_create_client:
            mock_create_client.return_value = Mock()
            evaluator = EnhancedEvaluator(mock_memory_manager, mock_tracer)
            return evaluator
    
    def test_evaluator_initialization(self, evaluator, mock_memory_manager, mock_tracer):
        """Test evaluator initializes correctly."""
        assert evaluator.memory_manager == mock_memory_manager
        assert evaluator.tracer == mock_tracer
        assert evaluator.config["memory_context_limit"] == 5
        assert evaluator.config["hallucination_threshold"] == 0.3
    
    def test_memory_context_retrieval(self, evaluator, mock_memory_manager):
        """Test memory context retrieval."""
        context = evaluator._get_memory_context("test_agent", "Analyze AAPL options")
        
        assert len(context) == 2
        assert context[0]["content"] == "Previous AAPL analysis showed bullish momentum"
        assert context[1]["content"] == "Options trading requires careful risk management"
        
        # Verify memory manager was called correctly
        mock_memory_manager.search_memories.assert_called_once_with(
            query="Analyze AAPL options",
            limit=5,
            filters={
                "agent_name": "test_agent",
                "mem_type": ["agent_action", "market_analysis", "evaluation_result"],
                "min_importance": 0.3
            }
        )
    
    def test_correctness_evaluation(self, evaluator):
        """Test correctness evaluation logic."""
        # Test successful result
        good_result = "AAPL shows strong bullish momentum with high call volume. Recommend covered call strategy."
        memory_context = [
            {"content": "AAPL has been trending upward with increasing volume"}
        ]
        
        correctness = evaluator._evaluate_correctness("Analyze AAPL", good_result, memory_context)
        assert 0.5 <= correctness <= 1.0
        
        # Test error result
        error_result = "Error: Failed to fetch data"
        correctness_error = evaluator._evaluate_correctness("Analyze AAPL", error_result, [])
        assert correctness_error < 0.5
    
    def test_hallucination_detection(self, evaluator):
        """Test hallucination detection logic."""
        # Test consistent result
        consistent_result = "AAPL shows bullish momentum"
        consistent_memory = [{"content": "AAPL trending upward with strong momentum"}]
        
        hallucination_score = evaluator._detect_hallucination(consistent_result, consistent_memory)
        assert hallucination_score <= 0.3
        
        # Test contradictory result
        contradictory_result = "AAPL shows bearish signals"
        bullish_memory = [{"content": "AAPL shows strong bullish momentum"}]
        
        hallucination_score_high = evaluator._detect_hallucination(contradictory_result, bullish_memory)
        # Should detect contradiction
        assert isinstance(hallucination_score_high, float)
    
    def test_context_relevance_evaluation(self, evaluator):
        """Test context relevance evaluation."""
        task = "Analyze AAPL options strategy"
        relevant_memory = [
            {"content": "AAPL options analysis shows high volatility"},
            {"content": "Options strategy requires careful timing"}
        ]
        
        relevance = evaluator._evaluate_context_relevance(task, relevant_memory)
        assert 0.0 <= relevance <= 1.0
        
        # Test with no memory context
        no_context_relevance = evaluator._evaluate_context_relevance(task, [])
        assert no_context_relevance == 0.0
    
    def test_confidence_calculation(self, evaluator):
        """Test confidence score calculation."""
        # Test high confidence scenario
        detailed_result = "Based on comprehensive analysis of AAPL's technical indicators, I am confident that a covered call strategy would be optimal given the current market conditions."
        good_memory = [{"content": "AAPL analysis"}, {"content": "Options strategy"}]
        
        confidence = evaluator._calculate_confidence(detailed_result, good_memory)
        assert 0.5 <= confidence <= 1.0
        
        # Test low confidence scenario
        uncertain_result = "Maybe AAPL might go up, but I'm not sure"
        confidence_low = evaluator._calculate_confidence(uncertain_result, [])
        assert confidence_low < 0.7
    
    def test_latency_categorization(self, evaluator):
        """Test latency categorization."""
        assert evaluator._categorize_latency(500) == 'fast'
        assert evaluator._categorize_latency(5000) == 'normal'
        assert evaluator._categorize_latency(15000) == 'slow'
        assert evaluator._categorize_latency(35000) == 'timeout'
        assert evaluator._categorize_latency(None) == 'unknown'
    
    def test_task_complexity_assessment(self, evaluator):
        """Test task complexity assessment."""
        simple_task = "Get AAPL price"
        complex_task = "Analyze and compare multiple options strategies for AAPL, considering volatility, risk, and potential returns"
        
        simple_complexity = evaluator._assess_task_complexity(simple_task)
        complex_complexity = evaluator._assess_task_complexity(complex_task)
        
        assert complex_complexity > simple_complexity
        assert 0.0 <= simple_complexity <= 1.0
        assert 0.0 <= complex_complexity <= 1.0
    
    def test_result_quality_assessment(self, evaluator):
        """Test result quality assessment."""
        high_quality = """
        AAPL Analysis:
        - Current price: $150.25
        - Volatility: 25%
        - Recommendation: Covered call strategy
        - Risk assessment: Moderate
        """
        
        low_quality = "AAPL good"
        
        high_score = evaluator._assess_result_quality(high_quality)
        low_score = evaluator._assess_result_quality(low_quality)
        
        assert high_score > low_score
        assert 0.0 <= low_score <= 1.0
        assert 0.0 <= high_score <= 1.0
    
    def test_overall_score_calculation(self, evaluator):
        """Test overall evaluation score calculation."""
        good_metrics = {
            "correctness": 0.9,
            "confidence_score": 0.8,
            "context_relevance": 0.7,
            "result_quality": 0.8,
            "hallucination_score": 0.1,
            "latency_category": "normal"
        }
        
        poor_metrics = {
            "correctness": 0.3,
            "confidence_score": 0.4,
            "context_relevance": 0.2,
            "result_quality": 0.3,
            "hallucination_score": 0.8,
            "latency_category": "timeout"
        }
        
        good_score = evaluator._calculate_overall_score(good_metrics)
        poor_score = evaluator._calculate_overall_score(poor_metrics)
        
        assert good_score > poor_score
        assert 0.0 <= poor_score <= 1.0
        assert 0.0 <= good_score <= 1.0

    @patch('wheel_trader.enhanced_evaluator.create_client')
    def test_comprehensive_evaluation(self, mock_create_client, mock_memory_manager, mock_tracer):
        """Test the complete evaluation workflow."""
        # Setup mocks
        mock_supabase = Mock()
        mock_response = Mock()
        mock_response.data = [{"id": "test-evaluation-id"}]
        mock_supabase.table.return_value.insert.return_value.execute.return_value = mock_response
        mock_create_client.return_value = mock_supabase

        evaluator = EnhancedEvaluator(mock_memory_manager, mock_tracer)

        # Test evaluation
        result = evaluator.evaluate_with_memory_context(
            agent_name="test_agent",
            task="Analyze AAPL options strategy",
            result="AAPL shows bullish momentum. Recommend covered call strategy with $155 strike.",
            trace_id="test-trace-123",
            execution_time_ms=5000
        )

        # Verify results
        assert "evaluation_score" in result
        assert "correctness" in result
        assert "hallucination_score" in result
        assert "confidence_score" in result
        assert "evaluation_id" in result
        assert result["memory_context_count"] == 2

        # Verify database storage was called
        mock_supabase.table.assert_called_with("agent_metrics")

        # Verify memory storage was called
        mock_memory_manager.store_memory.assert_called_once()

    def test_error_handling(self, evaluator):
        """Test error handling in evaluation."""
        # Test with invalid inputs
        result = evaluator.evaluate_with_memory_context(
            agent_name="",
            task="",
            result=None
        )

        # Should handle gracefully
        assert "error" in result or "evaluation_score" in result

    def test_financial_correctness_evaluation(self, evaluator):
        """Test financial-specific correctness evaluation."""
        financial_result = "AAPL options show high volatility. Strike price at $150 with premium of $5.25. Consider risk management."
        non_financial_result = "The weather is nice today."

        financial_score = evaluator._evaluate_financial_correctness(financial_result)
        non_financial_score = evaluator._evaluate_financial_correctness(non_financial_result)

        assert financial_score > non_financial_score
        assert 0.0 <= financial_score <= 1.0
        assert 0.0 <= non_financial_score <= 1.0

    def test_unrealistic_claims_detection(self, evaluator):
        """Test detection of unrealistic claims."""
        realistic_claim = "AAPL options strategy may provide moderate returns with careful risk management."
        unrealistic_claim = "This strategy guarantees 100% profit with zero risk!"

        realistic_check = evaluator._contains_unrealistic_claims(realistic_claim)
        unrealistic_check = evaluator._contains_unrealistic_claims(unrealistic_claim)

        assert not realistic_check
        assert unrealistic_check


class TestBackwardCompatibility:
    """Test backward compatibility with existing evaluator.py interface."""

    @patch('wheel_trader.enhanced_evaluator.EnhancedEvaluator')
    def test_evaluate_run_compatibility(self, mock_evaluator_class):
        """Test that evaluate_run function maintains backward compatibility."""
        # Setup mock
        mock_evaluator = Mock()
        mock_evaluator.evaluate_with_memory_context.return_value = {
            "correctness": 0.9,
            "latency_ms": 5000,
            "hallucination_score": 0.1,
            "evaluation_score": 0.85,
            "confidence_score": 0.8
        }
        mock_evaluator_class.return_value = mock_evaluator

        # Test the function
        result = evaluate_run(
            agent_name="test_agent",
            agent_version="1.0",
            result={
                "task": "Analyze AAPL",
                "exit_code": 0,
                "duration_ms": 5000,
                "output": "AAPL analysis complete"
            }
        )

        # Verify backward compatibility
        assert "correctness" in result
        assert "latency_ms" in result
        assert "hallucination_score" in result
        assert result["correctness"] == 0.9
        assert result["latency_ms"] == 5000

    @patch('wheel_trader.enhanced_evaluator.EnhancedEvaluator')
    def test_evaluate_run_fallback(self, mock_evaluator_class):
        """Test fallback behavior when enhanced evaluation fails."""
        # Setup mock to raise exception
        mock_evaluator_class.side_effect = Exception("Test error")

        # Test the function
        result = evaluate_run(
            agent_name="test_agent",
            agent_version="1.0",
            result={
                "exit_code": 0,
                "duration_ms": 3000
            }
        )

        # Should fallback to basic evaluation
        assert "correctness" in result
        assert "latency_ms" in result
        assert "hallucination_score" in result
        assert result["correctness"] == 1.0  # exit_code 0 = success
        assert result["latency_ms"] == 3000


class TestIntegrationScenarios:
    """Test realistic integration scenarios."""

    @pytest.fixture
    def integration_evaluator(self):
        """Create evaluator for integration testing."""
        with patch('wheel_trader.enhanced_evaluator.create_client') as mock_create_client:
            mock_supabase = Mock()
            mock_response = Mock()
            mock_response.data = [{"id": "integration-test-id"}]
            mock_supabase.table.return_value.insert.return_value.execute.return_value = mock_response
            mock_create_client.return_value = mock_supabase

            mock_memory_manager = Mock()
            mock_memory_manager.search_memories.return_value = []
            mock_memory_manager.store_memory.return_value = "memory-id"

            return EnhancedEvaluator(mock_memory_manager, None)

    def test_trading_agent_evaluation(self, integration_evaluator):
        """Test evaluation of a trading agent scenario."""
        result = integration_evaluator.evaluate_with_memory_context(
            agent_name="options_trader",
            task="Find profitable covered call opportunities for AAPL with current market conditions",
            result="""
            Based on current market analysis:
            - AAPL trading at $150.25
            - Implied volatility: 28%
            - Recommended strategy: Covered call with $155 strike, expiring in 30 days
            - Expected premium: $3.50
            - Risk assessment: Moderate, with potential for assignment
            - Maximum profit: $8.25 per share
            """,
            execution_time_ms=8500
        )

        # Verify comprehensive evaluation
        assert result["evaluation_score"] > 0.5
        assert result["latency_category"] == "normal"
        assert "correctness" in result
        assert "confidence_score" in result

    def test_market_analysis_evaluation(self, integration_evaluator):
        """Test evaluation of market analysis scenario."""
        result = integration_evaluator.evaluate_with_memory_context(
            agent_name="market_analyzer",
            task="Analyze current market sentiment for tech stocks",
            result="Tech stocks showing mixed signals. NASDAQ up 0.5% but volume declining.",
            execution_time_ms=2500
        )

        assert result["evaluation_score"] >= 0.0
        assert result["latency_category"] == "fast"

    def test_error_scenario_evaluation(self, integration_evaluator):
        """Test evaluation of error scenarios."""
        result = integration_evaluator.evaluate_with_memory_context(
            agent_name="data_fetcher",
            task="Get latest options data for AAPL",
            result="Error: API rate limit exceeded. Unable to fetch data.",
            execution_time_ms=1000
        )

        # Should handle errors gracefully
        assert "evaluation_score" in result
        assert result["correctness"] < 0.5  # Error should reduce correctness


if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v"])
