#!/usr/bin/env python3
"""
Simple test for comparative agents
"""

import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_agent_creation():
    """Test that we can create both agents"""
    print("🧪 Testing Agent Creation...")
    
    try:
        from agents.comparative_agents import (
            create_conservative_agent,
            create_aggressive_agent,
            create_comparative_agent_pair
        )
        
        # Test individual agent creation
        print("   Creating conservative agent...")
        conservative = create_conservative_agent()
        print(f"   ✅ Conservative agent created: {conservative.name}")
        
        print("   Creating aggressive agent...")
        aggressive = create_aggressive_agent()
        print(f"   ✅ Aggressive agent created: {aggressive.name}")
        
        # Test pair creation
        print("   Creating agent pair...")
        agents = create_comparative_agent_pair()
        print(f"   ✅ Agent pair created: {list(agents.keys())}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Failed to create agents: {e}")
        return False


def test_tool_differences():
    """Test that agents have different tools"""
    print("\n🧪 Testing Tool Differences...")
    
    try:
        from agents.comparative_agents import (
            conservative_market_analysis_tool,
            aggressive_market_analysis_tool
        )
        
        # Test conservative tool (should filter biotech)
        print("   Testing conservative tool with MRNA (biotech)...")
        result = conservative_market_analysis_tool("MRNA", "rsi")
        status = result.get("status", "success")
        print(f"   Conservative MRNA result: {status}")
        
        # Test aggressive tool (should NOT filter biotech)  
        print("   Testing aggressive tool with MRNA (biotech)...")
        result = aggressive_market_analysis_tool("MRNA", "rsi")
        status = result.get("status", "success")
        print(f"   Aggressive MRNA result: {status}")
        
        # Test both with regular stock
        print("   Testing both tools with AAPL (regular)...")
        cons_result = conservative_market_analysis_tool("AAPL", "rsi")
        agg_result = aggressive_market_analysis_tool("AAPL", "rsi")
        
        print(f"   Conservative AAPL: {cons_result.get('status', 'success')}")
        print(f"   Aggressive AAPL: {agg_result.get('status', 'success')}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Failed to test tools: {e}")
        return False


def main():
    """Run all tests"""
    print("🚀 Testing Comparative Agents System")
    print("=" * 40)
    
    success = True
    
    # Test 1: Agent creation
    if not test_agent_creation():
        success = False
    
    # Test 2: Tool differences
    if not test_tool_differences():
        success = False
    
    if success:
        print("\n🎉 All tests passed!")
        print("\n📋 What we now have:")
        print("   ✅ Conservative agent (excludes biotech)")
        print("   ✅ Aggressive agent (includes biotech)")
        print("   ✅ Different filtering behavior verified")
        print("\n🎯 Ready for next step: Running them in parallel!")
    else:
        print("\n❌ Some tests failed")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
