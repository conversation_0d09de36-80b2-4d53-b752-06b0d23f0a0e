-- Migration: Agent Metrics and Evaluation System
-- Purpose: Create comprehensive evaluation tracking with memory integration
-- Date: 2025-01-11
-- Dependencies: 003_memory_system.sql (memory_embeddings table)
-- Integration: Links with memory system and Phoenix traces for enhanced evaluation

-- Create agent_metrics table for evaluation tracking
CREATE TABLE IF NOT EXISTS agent_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_name TEXT NOT NULL,
    agent_version TEXT NOT NULL DEFAULT '1.0',
    task_description TEXT,
    task_hash TEXT, -- Hash of task for deduplication
    metrics_json JSONB NOT NULL,
    execution_time_ms INTEGER,
    memory_context_used BOOLEAN DEFAULT FALSE,
    memory_ids UUID[] DEFAULT '{}', -- Links to memory_embeddings.id
    memory_context_count INTEGER DEFAULT 0,
    trace_id TEXT, -- Phoenix trace ID for observability correlation
    span_id TEXT, -- Phoenix span ID for detailed tracing
    evaluation_score DECIMAL(3,2), -- Overall evaluation score (0.0 to 1.0)
    correctness_score DECIMAL(3,2), -- Task correctness (0.0 to 1.0)
    hallucination_score DECIMAL(3,2), -- Hallucination detection (0.0 = no hallucination, 1.0 = high hallucination)
    context_relevance_score DECIMAL(3,2), -- Memory context relevance (0.0 to 1.0)
    confidence_score DECIMAL(3,2), -- Agent confidence in result (0.0 to 1.0)
    latency_category TEXT DEFAULT 'normal', -- 'fast', 'normal', 'slow', 'timeout'
    success BOOLEAN DEFAULT TRUE,
    error_message TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    -- Constraints for data integrity
    CONSTRAINT valid_evaluation_score CHECK (evaluation_score IS NULL OR (evaluation_score >= 0.0 AND evaluation_score <= 1.0)),
    CONSTRAINT valid_correctness_score CHECK (correctness_score IS NULL OR (correctness_score >= 0.0 AND correctness_score <= 1.0)),
    CONSTRAINT valid_hallucination_score CHECK (hallucination_score IS NULL OR (hallucination_score >= 0.0 AND hallucination_score <= 1.0)),
    CONSTRAINT valid_context_relevance_score CHECK (context_relevance_score IS NULL OR (context_relevance_score >= 0.0 AND context_relevance_score <= 1.0)),
    CONSTRAINT valid_confidence_score CHECK (confidence_score IS NULL OR (confidence_score >= 0.0 AND confidence_score <= 1.0)),
    CONSTRAINT valid_latency_category CHECK (latency_category IN ('fast', 'normal', 'slow', 'timeout')),
    CONSTRAINT valid_execution_time CHECK (execution_time_ms IS NULL OR execution_time_ms >= 0)
);

-- Performance indexes for fast queries
CREATE INDEX IF NOT EXISTS idx_agent_metrics_agent_name ON agent_metrics(agent_name);
CREATE INDEX IF NOT EXISTS idx_agent_metrics_created_at ON agent_metrics(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_agent_metrics_trace_id ON agent_metrics(trace_id) WHERE trace_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_agent_metrics_span_id ON agent_metrics(span_id) WHERE span_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_agent_metrics_success ON agent_metrics(success);
CREATE INDEX IF NOT EXISTS idx_agent_metrics_evaluation_score ON agent_metrics(evaluation_score DESC) WHERE evaluation_score IS NOT NULL;

-- Composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_agent_metrics_agent_time ON agent_metrics(agent_name, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_agent_metrics_agent_success ON agent_metrics(agent_name, success, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_agent_metrics_memory_used ON agent_metrics(memory_context_used, created_at DESC);

-- GIN index for JSONB metrics for flexible querying
CREATE INDEX IF NOT EXISTS idx_agent_metrics_json ON agent_metrics USING GIN(metrics_json);

-- Create function to calculate enhanced evaluation metrics
CREATE OR REPLACE FUNCTION calculate_evaluation_metrics(
    p_agent_name TEXT,
    p_task_description TEXT,
    p_result_content TEXT,
    p_memory_context JSONB DEFAULT '[]'::JSONB,
    p_execution_time_ms INTEGER DEFAULT NULL,
    p_trace_id TEXT DEFAULT NULL,
    p_span_id TEXT DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    result_metrics JSONB;
    memory_count INTEGER;
    avg_memory_relevance DECIMAL;
    hallucination_indicators INTEGER := 0;
    confidence_factors DECIMAL := 0.5;
BEGIN
    -- Calculate memory context metrics
    memory_count := COALESCE(jsonb_array_length(p_memory_context), 0);
    
    -- Calculate average memory relevance (simplified - would use actual similarity in production)
    SELECT COALESCE(AVG((mem->>'similarity')::DECIMAL), 0.0)
    INTO avg_memory_relevance
    FROM jsonb_array_elements(p_memory_context) AS mem
    WHERE mem->>'similarity' IS NOT NULL;
    
    -- Simple hallucination detection (check for contradictions with memory)
    -- In production, this would use more sophisticated NLP analysis
    IF memory_count > 0 AND avg_memory_relevance < 0.3 THEN
        hallucination_indicators := hallucination_indicators + 1;
    END IF;
    
    -- Calculate confidence based on memory context and execution time
    confidence_factors := CASE
        WHEN memory_count > 3 AND avg_memory_relevance > 0.7 THEN 0.9
        WHEN memory_count > 1 AND avg_memory_relevance > 0.5 THEN 0.7
        WHEN memory_count > 0 THEN 0.6
        ELSE 0.5
    END;
    
    -- Adjust confidence based on execution time (faster might be less thorough)
    IF p_execution_time_ms IS NOT NULL THEN
        IF p_execution_time_ms < 1000 THEN
            confidence_factors := confidence_factors * 0.9; -- Very fast might be superficial
        ELSIF p_execution_time_ms > 30000 THEN
            confidence_factors := confidence_factors * 0.8; -- Very slow might indicate issues
        END IF;
    END IF;
    
    -- Build result metrics
    result_metrics := jsonb_build_object(
        'memory_context_count', memory_count,
        'avg_memory_relevance', COALESCE(avg_memory_relevance, 0.0),
        'hallucination_indicators', hallucination_indicators,
        'confidence_score', LEAST(1.0, confidence_factors),
        'execution_time_ms', p_execution_time_ms,
        'has_memory_context', memory_count > 0,
        'memory_quality', CASE
            WHEN memory_count = 0 THEN 'none'
            WHEN avg_memory_relevance > 0.8 THEN 'excellent'
            WHEN avg_memory_relevance > 0.6 THEN 'good'
            WHEN avg_memory_relevance > 0.4 THEN 'fair'
            ELSE 'poor'
        END
    );
    
    RETURN result_metrics;
END;
$$;

-- Create function to store evaluation metrics with automatic calculation
CREATE OR REPLACE FUNCTION store_evaluation_metrics(
    p_agent_name TEXT,
    p_task_description TEXT,
    p_result_content TEXT,
    p_memory_context JSONB DEFAULT '[]'::JSONB,
    p_memory_ids UUID[] DEFAULT '{}',
    p_execution_time_ms INTEGER DEFAULT NULL,
    p_trace_id TEXT DEFAULT NULL,
    p_span_id TEXT DEFAULT NULL,
    p_success BOOLEAN DEFAULT TRUE,
    p_error_message TEXT DEFAULT NULL,
    p_custom_metrics JSONB DEFAULT '{}'::JSONB
)
RETURNS UUID
LANGUAGE plpgsql
AS $$
DECLARE
    evaluation_id UUID;
    calculated_metrics JSONB;
    memory_count INTEGER;
    task_hash_value TEXT;
BEGIN
    -- Calculate evaluation metrics
    calculated_metrics := calculate_evaluation_metrics(
        p_agent_name, p_task_description, p_result_content,
        p_memory_context, p_execution_time_ms, p_trace_id, p_span_id
    );
    
    -- Merge with custom metrics
    calculated_metrics := calculated_metrics || p_custom_metrics;
    
    -- Calculate memory context count
    memory_count := COALESCE(array_length(p_memory_ids, 1), 0);
    
    -- Generate task hash for deduplication
    task_hash_value := encode(digest(p_task_description, 'sha256'), 'hex');
    
    -- Insert evaluation record
    INSERT INTO agent_metrics (
        agent_name,
        task_description,
        task_hash,
        metrics_json,
        execution_time_ms,
        memory_context_used,
        memory_ids,
        memory_context_count,
        trace_id,
        span_id,
        evaluation_score,
        correctness_score,
        hallucination_score,
        context_relevance_score,
        confidence_score,
        latency_category,
        success,
        error_message
    ) VALUES (
        p_agent_name,
        p_task_description,
        task_hash_value,
        calculated_metrics,
        p_execution_time_ms,
        memory_count > 0,
        p_memory_ids,
        memory_count,
        p_trace_id,
        p_span_id,
        (calculated_metrics->>'confidence_score')::DECIMAL,
        COALESCE((p_custom_metrics->>'correctness')::DECIMAL, 0.8), -- Default if not provided
        CASE WHEN (calculated_metrics->>'hallucination_indicators')::INTEGER > 0 THEN 0.3 ELSE 0.1 END,
        (calculated_metrics->>'avg_memory_relevance')::DECIMAL,
        (calculated_metrics->>'confidence_score')::DECIMAL,
        CASE
            WHEN p_execution_time_ms IS NULL THEN 'normal'
            WHEN p_execution_time_ms < 1000 THEN 'fast'
            WHEN p_execution_time_ms < 10000 THEN 'normal'
            WHEN p_execution_time_ms < 30000 THEN 'slow'
            ELSE 'timeout'
        END,
        p_success,
        p_error_message
    ) RETURNING id INTO evaluation_id;
    
    RETURN evaluation_id;
END;
$$;

-- Create view for evaluation analytics
CREATE OR REPLACE VIEW evaluation_analytics AS
SELECT
    agent_name,
    COUNT(*) as total_evaluations,
    AVG(evaluation_score) as avg_evaluation_score,
    AVG(correctness_score) as avg_correctness,
    AVG(hallucination_score) as avg_hallucination,
    AVG(context_relevance_score) as avg_context_relevance,
    AVG(confidence_score) as avg_confidence,
    AVG(execution_time_ms) as avg_execution_time_ms,
    COUNT(*) FILTER (WHERE success = TRUE) as successful_evaluations,
    COUNT(*) FILTER (WHERE memory_context_used = TRUE) as evaluations_with_memory,
    COUNT(*) FILTER (WHERE latency_category = 'fast') as fast_evaluations,
    COUNT(*) FILTER (WHERE latency_category = 'slow') as slow_evaluations,
    COUNT(*) FILTER (WHERE latency_category = 'timeout') as timeout_evaluations,
    MAX(created_at) as last_evaluation_at,
    MIN(created_at) as first_evaluation_at
FROM agent_metrics
GROUP BY agent_name
ORDER BY total_evaluations DESC, avg_evaluation_score DESC;

-- Create function to get agent performance trends
CREATE OR REPLACE FUNCTION get_agent_performance_trends(
    p_agent_name TEXT,
    p_days_back INTEGER DEFAULT 7
)
RETURNS TABLE (
    date_bucket DATE,
    evaluations_count BIGINT,
    avg_score DECIMAL,
    success_rate DECIMAL,
    avg_execution_time DECIMAL
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        DATE(created_at) as date_bucket,
        COUNT(*) as evaluations_count,
        AVG(evaluation_score) as avg_score,
        (COUNT(*) FILTER (WHERE success = TRUE))::DECIMAL / COUNT(*) as success_rate,
        AVG(execution_time_ms) as avg_execution_time
    FROM agent_metrics
    WHERE agent_name = p_agent_name
    AND created_at >= NOW() - INTERVAL '1 day' * p_days_back
    GROUP BY DATE(created_at)
    ORDER BY date_bucket DESC;
END;
$$;

-- Example usage:
-- 
-- -- Store evaluation metrics
-- SELECT store_evaluation_metrics(
--     'trading_agent',
--     'Analyze AAPL options strategy',
--     'Bullish outlook with covered call recommendation',
--     '[{"id": "123", "similarity": 0.85, "content": "Previous AAPL analysis"}]'::JSONB,
--     ARRAY['550e8400-e29b-41d4-a716-************']::UUID[],
--     5000,
--     'trace_123',
--     'span_456',
--     TRUE,
--     NULL,
--     '{"correctness": 0.9, "custom_metric": "high_confidence"}'::JSONB
-- );
-- 
-- -- View evaluation analytics
-- SELECT * FROM evaluation_analytics WHERE agent_name = 'trading_agent';
-- 
-- -- Get performance trends
-- SELECT * FROM get_agent_performance_trends('trading_agent', 30);
