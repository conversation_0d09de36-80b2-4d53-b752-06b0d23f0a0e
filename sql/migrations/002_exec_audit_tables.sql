-- Migration: Create execution audit and agent health tables
-- Purpose: Track e2b sandbox execution metrics and agent health scores
-- Date: 2025-01-09

-- Create execution audit log table
CREATE TABLE IF NOT EXISTS exec_audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    container_id TEXT NOT NULL,
    agent_name TEXT NOT NULL,
    job_name TEXT NOT NULL,
    cpu_ms INTEGER DEFAULT 0,
    mem_peak_mb INTEGER DEFAULT 0,
    duration_ms INTEGER NOT NULL,
    exit_code INTEGER NOT NULL,
    success BOOLEAN NOT NULL,
    error_message TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_exec_audit_log_agent_name ON exec_audit_log(agent_name);
CREATE INDEX IF NOT EXISTS idx_exec_audit_log_created_at ON exec_audit_log(created_at);
CREATE INDEX IF NOT EXISTS idx_exec_audit_log_success ON exec_audit_log(success);

-- Create agent health table
CREATE TABLE IF NOT EXISTS agent_health (
    agent_name TEXT PRIMARY KEY,
    version TEXT DEFAULT 'unknown',
    health_score DECIMAL(3,2) DEFAULT 1.00,
    total_runs INTEGER DEFAULT 0,
    successful_runs INTEGER DEFAULT 0,
    avg_duration_ms INTEGER DEFAULT 0,
    last_updated TIMESTAMPTZ DEFAULT NOW()
);

-- Create materialized view for agent statistics
CREATE MATERIALIZED VIEW IF NOT EXISTS agent_stats AS
SELECT 
    agent_name,
    COUNT(*) as total_runs,
    SUM(CASE WHEN success THEN 1 ELSE 0 END) as successful_runs,
    ROUND(AVG(duration_ms)) as avg_duration_ms,
    ROUND(AVG(cpu_ms)) as avg_cpu_ms,
    ROUND(AVG(mem_peak_mb)) as avg_mem_mb,
    MAX(created_at) as last_run_at,
    ROUND(
        (SUM(CASE WHEN success THEN 1 ELSE 0 END)::DECIMAL / COUNT(*)) * 100, 
        2
    ) as success_rate_pct
FROM exec_audit_log 
WHERE created_at >= NOW() - INTERVAL '24 hours'
GROUP BY agent_name
WITH NO DATA;

-- Create function to refresh agent stats
CREATE OR REPLACE FUNCTION refresh_agent_stats()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW agent_stats;
END;
$$ LANGUAGE plpgsql;

-- Create function to update agent health scores
CREATE OR REPLACE FUNCTION update_agent_health()
RETURNS void AS $$
BEGIN
    -- Update agent health based on recent performance
    INSERT INTO agent_health (agent_name, health_score, total_runs, successful_runs, avg_duration_ms, last_updated)
    SELECT 
        agent_name,
        GREATEST(0.0, LEAST(1.0, 
            (successful_runs::DECIMAL / GREATEST(total_runs, 1)) - 
            (CASE WHEN avg_duration_ms > 10000 THEN 0.1 ELSE 0 END) -
            (CASE WHEN avg_cpu_ms > 5000 THEN 0.1 ELSE 0 END)
        )) as health_score,
        total_runs,
        successful_runs,
        avg_duration_ms,
        NOW()
    FROM agent_stats
    ON CONFLICT (agent_name) 
    DO UPDATE SET
        health_score = EXCLUDED.health_score,
        total_runs = EXCLUDED.total_runs,
        successful_runs = EXCLUDED.successful_runs,
        avg_duration_ms = EXCLUDED.avg_duration_ms,
        last_updated = NOW();
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-refresh stats every hour
-- Note: This would typically be done via cron job or scheduled task
-- For now, we'll create the function and call it manually

-- Grant permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE ON exec_audit_log TO your_app_user;
-- GRANT SELECT, INSERT, UPDATE ON agent_health TO your_app_user;
-- GRANT SELECT ON agent_stats TO your_app_user;

-- Example queries for monitoring:
-- 
-- -- View recent executions
-- SELECT * FROM exec_audit_log ORDER BY created_at DESC LIMIT 10;
-- 
-- -- View agent health scores
-- SELECT * FROM agent_health ORDER BY health_score DESC;
-- 
-- -- View agent statistics
-- SELECT * FROM agent_stats ORDER BY success_rate_pct DESC;
-- 
-- -- Refresh stats manually
-- SELECT refresh_agent_stats();
-- SELECT update_agent_health();
