-- ================================================
-- Migration: Enhanced Memory System with pgvector
-- Purpose: Comprehensive memory storage for agent KB
-- Date: 2025-01-10
-- Dependencies: Requires pgvector extension
-- Optimized for: Fast financial market operations with high volatility
-- ================================================

-- Enable pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- ------------------------------------------
-- 1️⃣  Memory Embeddings Table
-- Create enhanced memory embeddings table
-- ------------------------------------------
CREATE TABLE IF NOT EXISTS memory_embeddings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    trace_id TEXT,
    span_id TEXT,
    agent_name TEXT NOT NULL,
    mem_type TEXT NOT NULL DEFAULT 'agent_action',
    content TEXT NOT NULL,
    embedding vector(1536), -- OpenAI embedding dimension
    json_blob JSONB,
    metadata JSONB DEFAULT '{}',
    importance_score DECIMAL(3,2) DEFAULT 0.5,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ,
    tags TEXT[] DEFAULT '{}',
    CONSTRAINT valid_importance_score CHECK (importance_score >= 0.0 AND importance_score <= 1.0),
    CONSTRAINT valid_mem_type CHECK (mem_type IN (
        'agent_action', 'market_analysis', 'risk_assessment', 'options_data', 'execution_result', 'error_log'
    ))
);

-- ------------------------------------------
-- 2️⃣  Indexes
-- Create high-performance indexes for fast financial operations
-- ------------------------------------------
CREATE INDEX IF NOT EXISTS idx_memory_embeddings_agent_name ON memory_embeddings(agent_name);
CREATE INDEX IF NOT EXISTS idx_memory_embeddings_mem_type ON memory_embeddings(mem_type);
CREATE INDEX IF NOT EXISTS idx_memory_embeddings_created_at ON memory_embeddings(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_memory_embeddings_importance ON memory_embeddings(importance_score DESC);
CREATE INDEX IF NOT EXISTS idx_memory_embeddings_tags ON memory_embeddings USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_memory_embeddings_metadata ON memory_embeddings USING GIN(metadata);
CREATE INDEX IF NOT EXISTS idx_memory_embeddings_trace_id ON memory_embeddings(trace_id) WHERE trace_id IS NOT NULL;

-- Vector similarity index
-- Create vector similarity index (HNSW for better performance in financial contexts)
-- Optimized for fast similarity search with cosine distance
CREATE INDEX IF NOT EXISTS idx_memory_embeddings_vector
ON memory_embeddings USING hnsw (embedding vector_cosine_ops)
WITH (m = 16, ef_construction = 64);

-- Composite indexes
CREATE INDEX IF NOT EXISTS idx_memory_agent_type_time
ON memory_embeddings(agent_name, mem_type, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_memory_importance_time
ON memory_embeddings(importance_score DESC, created_at DESC);

-- ------------------------------------------
-- 3️⃣  Similarity Search Function
-- Create function for advanced similarity search with financial market optimizations
-- ------------------------------------------
CREATE OR REPLACE FUNCTION match_memories(
    query_embedding vector(1536),
    agent_filter TEXT DEFAULT NULL,
    mem_type_filter TEXT DEFAULT NULL,
    match_threshold FLOAT DEFAULT 0.78,
    match_count INT DEFAULT 5,
    min_importance FLOAT DEFAULT 0.0,
    tag_filters TEXT[] DEFAULT NULL,
    time_window_hours INT DEFAULT NULL,
    include_expired BOOLEAN DEFAULT FALSE
)
RETURNS TABLE (
    id UUID,
    agent_name TEXT,
    mem_type TEXT,
    content TEXT,
    json_blob JSONB,
    metadata JSONB,
    importance_score DECIMAL,
    similarity FLOAT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    tags TEXT[],
    trace_id TEXT,
    span_id TEXT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        m.id,
        m.agent_name,
        m.mem_type,
        m.content,
        m.json_blob,
        m.metadata,
        m.importance_score,
        (1 - (m.embedding <=> query_embedding)) as similarity,
        m.created_at,
        m.updated_at,
        m.tags,
        m.trace_id,
        m.span_id
    FROM memory_embeddings m
    WHERE
        (1 - (m.embedding <=> query_embedding)) > match_threshold
        AND (agent_filter IS NULL OR m.agent_name = agent_filter)
        AND (mem_type_filter IS NULL OR m.mem_type = mem_type_filter)
        AND m.importance_score >= min_importance
        AND (tag_filters IS NULL OR m.tags && tag_filters)
        AND (time_window_hours IS NULL OR m.created_at > NOW() - INTERVAL '1 hour' * time_window_hours)
        AND (include_expired OR m.expires_at IS NULL OR m.expires_at > NOW())
    ORDER BY similarity DESC, m.importance_score DESC, m.created_at DESC
    LIMIT match_count;
END;
$$;

-- ------------------------------------------
-- 4️⃣  Exact Content Search Function
-- Create function for fast memory search by content (for exact matches)
-- ------------------------------------------
CREATE OR REPLACE FUNCTION search_memories_by_content(
    search_text TEXT,
    agent_filter TEXT DEFAULT NULL,
    mem_type_filter TEXT DEFAULT NULL,
    limit_count INT DEFAULT 10
)
RETURNS TABLE (
    id UUID,
    agent_name TEXT,
    mem_type TEXT,
    content TEXT,
    importance_score DECIMAL,
    created_at TIMESTAMPTZ
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        m.id,
        m.agent_name,
        m.mem_type,
        m.content,
        m.importance_score,
        m.created_at
    FROM memory_embeddings m
    WHERE
        m.content ILIKE '%' || search_text || '%'
        AND (agent_filter IS NULL OR m.agent_name = agent_filter)
        AND (mem_type_filter IS NULL OR m.mem_type = mem_type_filter)
        AND (m.expires_at IS NULL OR m.expires_at > NOW())
    ORDER BY m.importance_score DESC, m.created_at DESC
    LIMIT limit_count;
END;
$$;

-- ------------------------------------------
-- 5️⃣  Consolidate Similar Memories Function
-- Create function for memory consolidation (merge similar memories)
-- Optimized for financial data where similar market conditions should be consolidated
-- ------------------------------------------
CREATE OR REPLACE FUNCTION consolidate_memories(
    similarity_threshold FLOAT DEFAULT 0.95,
    agent_filter TEXT DEFAULT NULL,
    max_consolidations INT DEFAULT 100
)
RETURNS INT
LANGUAGE plpgsql
AS $$
DECLARE
    consolidated_count INT := 0;
    memory_record RECORD;
    similar_record RECORD;
BEGIN
    FOR memory_record IN
        SELECT * FROM memory_embeddings
        WHERE (agent_filter IS NULL OR agent_name = agent_filter)
        AND (expires_at IS NULL OR expires_at > NOW())
        ORDER BY created_at DESC
        LIMIT max_consolidations * 2
    LOOP
        FOR similar_record IN
            SELECT * FROM memory_embeddings m
            WHERE m.id != memory_record.id
            AND (agent_filter IS NULL OR m.agent_name = agent_filter)
            AND (1 - (m.embedding <=> memory_record.embedding)) > similarity_threshold
            AND m.created_at < memory_record.created_at
            AND (m.expires_at IS NULL OR m.expires_at > NOW())
            LIMIT 5
        LOOP
            UPDATE memory_embeddings
            SET
                content = memory_record.content || ' | CONSOLIDATED: ' || similar_record.content,
                metadata = memory_record.metadata || similar_record.metadata,
                importance_score = GREATEST(memory_record.importance_score, similar_record.importance_score),
                tags = array(SELECT DISTINCT unnest(memory_record.tags || similar_record.tags)),
                updated_at = NOW()
            WHERE id = memory_record.id;

            DELETE FROM memory_embeddings WHERE id = similar_record.id;
            consolidated_count := consolidated_count + 1;

            EXIT WHEN consolidated_count >= max_consolidations;
        END LOOP;

        EXIT WHEN consolidated_count >= max_consolidations;
    END LOOP;

    RETURN consolidated_count;
END;
$$;

-- ------------------------------------------
-- 6️⃣  Memory Statistics View
-- Create memory statistics view for monitoring and analytics
-- ------------------------------------------
CREATE OR REPLACE VIEW memory_stats AS
SELECT
    agent_name,
    mem_type,
    COUNT(*) as memory_count,
    AVG(importance_score) as avg_importance,
    MAX(created_at) as last_memory_at,
    MIN(created_at) as first_memory_at,
    COUNT(DISTINCT t.tag) as unique_tags,
    AVG(length(content)) as avg_content_length,
    COUNT(*) FILTER (WHERE created_at > NOW() - INTERVAL '1 hour') as memories_last_hour,
    COUNT(*) FILTER (WHERE created_at > NOW() - INTERVAL '24 hours') as memories_last_day,
    COUNT(*) FILTER (WHERE importance_score > 0.7) as high_importance_count
FROM memory_embeddings,
     LATERAL unnest(tags) AS t(tag)
WHERE expires_at IS NULL OR expires_at > NOW()
GROUP BY agent_name, mem_type
ORDER BY memory_count DESC, avg_importance DESC;

-- ------------------------------------------
-- 7️⃣  Cleanup Expired Memories Function
-- Create function to cleanup expired memories
-- ------------------------------------------
CREATE OR REPLACE FUNCTION cleanup_expired_memories()
RETURNS INT
LANGUAGE plpgsql
AS $$
DECLARE
    deleted_count INT;
BEGIN
    DELETE FROM memory_embeddings 
    WHERE expires_at IS NOT NULL AND expires_at <= NOW();

    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$;

-- ------------------------------------------
-- 8️⃣  Memory Analytics Table + Indexes
-- Create memory usage analytics table
-- ------------------------------------------
CREATE TABLE IF NOT EXISTS memory_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_name TEXT NOT NULL,
    query_type TEXT NOT NULL,
    query_embedding vector(1536),
    results_count INT DEFAULT 0,
    avg_similarity FLOAT DEFAULT 0.0,
    execution_time_ms INT DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create index for analytics
CREATE INDEX IF NOT EXISTS idx_memory_analytics_agent_name ON memory_analytics(agent_name);
CREATE INDEX IF NOT EXISTS idx_memory_analytics_created_at ON memory_analytics(created_at);

-- ------------------------------------------
-- ✅ Example usage:
-- SELECT * FROM match_memories('[0.1,0.2,...]'::vector, 'trading_agent', 'market_analysis');
-- SELECT cleanup_expired_memories();
-- SELECT consolidate_memories();
-- SELECT * FROM memory_stats WHERE agent_name = 'trading_agent';
