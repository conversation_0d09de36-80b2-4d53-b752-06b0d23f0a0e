# Covered Calls Agent Platform

This project is an autonomous agent platform for managing wheel trading strategies. It uses a suite of intelligent agents to analyze market data, execute trades, and continuously learn from its decisions.

## Core Capabilities

The platform is built on three foundational pillars that ensure robust, secure, and intelligent operation:

*   **Memory & Observability**: Agents have long-term memory, powered by `pgvector`, allowing them to learn from past actions. All operations are traced in real-time using Arize Phoenix for full system visibility.
*   **Secure Execution**: Every agent and tool runs in a hardened, isolated sandbox via `e2b` containers. This prevents runaway code and secures the platform's infrastructure.
*   **Continuous Evaluation**: Agent performance is constantly measured against a set of objective metrics. A health score is calculated nightly, and underperforming agents are automatically sidelined to ensure only the most effective models are active.

## How to Run

1.  **Prerequisites**
    *   Python 3.9+
    *   Docker and Docker Compose
    *   A Supabase project with the SQL schemas from the initial setup applied.
    *   An OpenAI API key.

2.  **Setup**
    *   Clone the repository.
    *   Create a `.env` file in the root of the project and add your Supabase and OpenAI credentials:
        ```
        # .env
        SUPABASE_URL="YOUR_SUPABASE_URL"
        SUPABASE_KEY="YOUR_SUPABASE_SERVICE_ROLE_KEY"
        OPENAI_API_KEY="YOUR_OPENAI_API_KEY"
        ```
    *   Install the Python dependencies:
        ```bash
        pip install -r requirements.txt
        ```

3.  **Running the Services**
    *   Start the Phoenix observability UI:
        ```bash
        docker-compose up
        ```
    *   Run the main Flask application:
        ```bash
        python app.py
        ```

4.  **Interacting with the Platform**
    *   The main application will be running at `http://localhost:8000`.
    *   You can interact with the system by sending POST requests to the `/execute_task` endpoint. For example:
        ```bash
        curl -X POST http://localhost:8000/execute_task \
             -H "Content-Type: application/json" \
             -d '{
                   "task": "Analyze AAPL for a covered call opportunity for next month."
                 }'
        ```
    *   You can view the observability dashboard at `http://localhost:6006`.

## Nightly Health Score Calculation

The `eval_report.py` script is designed to be run as a nightly cron job to calculate the health scores for the agents. You can run it manually with:
```bash
python eval_report.py
