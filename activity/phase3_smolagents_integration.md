# Phase 3: smolagents Integration Action Plan

## Current Status
✅ **Phase 2 COMPLETED**: E2B integration working with audit logging
✅ **Database Tables**: Created and working
✅ **E2B Tests**: All passing

## Phase 3 Objectives
Create a smolagents adapter that routes tool execution through our e2b sandbox for secure code execution.

## Challenge: smolagents Dependency Issues
**Problem**: smolagents has PIL dependency with hash mismatch
**Impact**: Cannot install full smolagents package

## Solution Approach: Custom smolagents Adapter

Instead of installing the full smolagents package, we'll create a lightweight adapter that:
1. Mimics the smolagents Tool interface
2. Routes execution through our e2b sandbox
3. Provides the same API for agent coordination

## Implementation Plan

### 3.1 Create smolagents-Compatible Tool Base Class
**File**: `wheel_trader/smolagents_adapter.py`

**Features**:
- Base Tool class compatible with smolagents interface
- Automatic e2b execution routing
- Agent health checking before execution
- Comprehensive logging and metrics

### 3.2 Create Specific Tool Implementations
**Tools to Create**:
1. **OptionsDataTool**: Fetch options data via e2b
2. **MarketAnalysisTool**: Run analysis code via e2b
3. **TradingDecisionTool**: Execute trading logic via e2b
4. **RiskAssessmentTool**: Run risk calculations via e2b

### 3.3 Update Coordinator Integration
**File**: `wheel_trader/coordinator.py`

**Updates**:
- Register custom tools with coordinator
- Add agent health gating
- Add execution routing through e2b
- Add performance monitoring

### 3.4 Create Agent Health Manager
**File**: `wheel_trader/agent_health.py`

**Features**:
- Calculate health scores from audit logs
- Implement health gating (block agents with score < 0.7)
- Automatic health score updates
- Health monitoring dashboard data

## Detailed Implementation Steps

### Step 1: Create smolagents Adapter Base
```python
class SecureTool:
    """Base class for tools that execute in e2b sandbox"""
    
    def __init__(self, name, description):
        self.name = name
        self.description = description
    
    def run(self, *args, **kwargs):
        # Check agent health before execution
        # Route through e2b sandbox
        # Log execution metrics
        # Return results
```

### Step 2: Implement Specific Tools
```python
class OptionsDataTool(SecureTool):
    def run(self, symbol, expiry_date):
        code = f"""
import requests
# Fetch options data for {symbol}
# Return structured data
"""
        return run_code_string("options_data_tool", code)
```

### Step 3: Create Health Manager
```python
class AgentHealthManager:
    def get_health_score(self, agent_name):
        # Query agent_health table
        # Return current health score
    
    def is_agent_healthy(self, agent_name, threshold=0.7):
        # Check if agent can execute
        # Return boolean
```

### Step 4: Update Coordinator
```python
class SecureCoordinator:
    def __init__(self):
        self.health_manager = AgentHealthManager()
        self.tools = {}
    
    def execute_tool(self, tool_name, *args, **kwargs):
        # Check agent health
        # Route through e2b if healthy
        # Log execution
```

## Benefits of This Approach

1. **No Dependency Issues**: Avoid smolagents installation problems
2. **Full Control**: Custom implementation tailored to our needs
3. **Security First**: All execution goes through e2b sandbox
4. **Health Monitoring**: Built-in agent health checking
5. **Audit Trail**: Complete execution logging
6. **Performance**: Optimized for our use case

## Risk Assessment

**LOW RISK**:
- Creating custom tool classes
- Health score calculation
- Database integration

**MEDIUM RISK**:
- Coordinator integration (may affect existing functionality)
- Tool interface compatibility

**HIGH RISK**:
- None (we're not modifying core systems)

## Success Criteria

1. ✅ Custom Tool base class created
2. ✅ At least 2 specific tools implemented
3. ✅ Health manager working
4. ✅ Coordinator integration complete
5. ✅ All tools execute via e2b sandbox
6. ✅ Health gating prevents unhealthy agents from running
7. ✅ Comprehensive testing passes

## Testing Plan

1. **Unit Tests**: Test each tool individually
2. **Integration Tests**: Test coordinator with tools
3. **Health Tests**: Test health gating functionality
4. **Performance Tests**: Verify e2b execution performance
5. **Security Tests**: Verify sandbox isolation

## Timeline

- **Step 1-2**: Create adapter and tools (~30 minutes)
- **Step 3**: Health manager (~20 minutes)
- **Step 4**: Coordinator integration (~20 minutes)
- **Step 5**: Testing and validation (~20 minutes)

**Total Estimated Time**: ~90 minutes

## Next Actions

1. **AWAIT APPROVAL** for Phase 3 implementation
2. **CREATE** smolagents_adapter.py with base classes
3. **IMPLEMENT** specific tools (OptionsDataTool, etc.)
4. **CREATE** agent health manager
5. **UPDATE** coordinator integration
6. **TEST** complete integration

## Alternative Approach (if needed)

If custom adapter doesn't work, we can:
1. Install smolagents in isolated environment
2. Use subprocess calls to smolagents
3. Create API wrapper around smolagents
4. Use different agent framework (LangChain, etc.)

## Recommendation

**PROCEED** with custom smolagents adapter approach:
- Avoids dependency issues
- Provides full control and security
- Maintains compatibility with existing code
- Enables comprehensive monitoring and health checking
