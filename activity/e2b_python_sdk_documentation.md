# E2B Python SDK Documentation

## Overview

E2B is an open-source infrastructure that allows you to run AI-generated code in secure isolated sandboxes in the cloud. The Python SDK provides a powerful interface for creating and managing these sandboxes.

## Key Features

- **Secure Code Execution**: Run AI-generated Python code in isolated cloud environments
- **Fast Startup**: Sandboxes start in ~150ms
- **Multiple SDK Types**: Core SDK, Code Interpreter SDK, and Desktop SDK
- **Persistence**: Pause and resume sandboxes with full state preservation (beta)
- **File Operations**: Upload, download, read, write files
- **Command Execution**: Run terminal commands inside sandboxes
- **Internet Access**: Sandboxes have internet connectivity
- **Pre-installed Libraries**: Common data science and ML libraries included

## Installation

```bash
# Code Interpreter SDK (recommended for AI code execution)
pip install e2b-code-interpreter

# Core SDK
pip install e2b

# Desktop SDK
pip install e2b-desktop
```

## Quick Start

### 1. Get API Key
- Sign up at https://e2b.dev/auth/sign-up
- Get $100 in free credits
- Copy API key from dashboard

### 2. Set Environment Variable
```bash
export E2B_API_KEY=e2b_***
```

### 3. Basic Usage

```python
from e2b_code_interpreter import Sandbox

# Create sandbox
sandbox = Sandbox()

# Execute Python code
execution = sandbox.run_code('print("Hello World")')
print(execution.logs.stdout)

# List files
files = sandbox.files.list('/')
print(files)

# Close sandbox (optional - auto-closes after 5 minutes)
sandbox.close()
```

## Core Classes and Methods

### Sandbox Class

```python
class Sandbox:
    def __init__(self, api_key=None, timeout=300000):
        """Create a new sandbox instance"""
        
    @classmethod
    def create(cls, **kwargs):
        """Alternative constructor"""
        
    def run_code(self, code, language="python", **kwargs):
        """Execute code in the sandbox"""
        
    def create_code_context(self, cwd=None, language=None):
        """Create isolated execution context"""
        
    def close(self):
        """Close the sandbox"""
```

### AsyncSandbox Class

```python
class AsyncSandbox:
    @classmethod
    async def create(cls, **kwargs):
        """Create async sandbox"""
        
    async def run_code(self, code, **kwargs):
        """Execute code asynchronously"""
```

### Execution Results

```python
@dataclass
class Execution:
    results: List[Result]  # Output data (charts, tables, etc.)
    logs: Logs            # stdout/stderr
    error: ExecutionError # Error if occurred
    execution_count: int  # Cell execution number
    
    @property
    def text(self) -> str:
        """Get text representation of results"""
```

### File Operations

```python
# Upload file
sandbox.files.write('/path/to/file.txt', content)

# Read file
content = sandbox.files.read('/path/to/file.txt')

# List directory
files = sandbox.files.list('/path')

# Download file
data = sandbox.files.download('/path/to/file.txt')
```

### Command Execution

```python
# Run terminal command
result = sandbox.commands.run('ls -la')
print(result.stdout)
print(result.stderr)
print(result.exit_code)
```

## Advanced Features

### Sandbox Persistence (Beta)

```python
# Pause sandbox (saves full state)
sandbox_id = sandbox.pause()

# Resume from saved state
resumed_sandbox = Sandbox.resume(sandbox_id)

# List paused sandboxes
paused = Sandbox.list(query={'state': ['paused']})
```

### Streaming Output

```python
def on_stdout(output):
    print(f"STDOUT: {output.line}")

def on_stderr(output):
    print(f"STDERR: {output.line}")

execution = sandbox.run_code(
    "print('Hello')", 
    on_stdout=on_stdout,
    on_stderr=on_stderr
)
```

### Custom Contexts

```python
# Create isolated execution context
context = sandbox.create_code_context(
    cwd="/home/<USER>/project",
    language="python"
)

# Run code in specific context
execution = sandbox.run_code("import os; print(os.getcwd())", context=context)
```

## Data Analysis Example

```python
import pandas as pd
from e2b_code_interpreter import Sandbox

# Create sandbox and upload data
sandbox = Sandbox()
sandbox.files.write('data.csv', csv_content)

# Analyze data with AI-generated code
code = """
import pandas as pd
import matplotlib.pyplot as plt

df = pd.read_csv('data.csv')
print(df.describe())

# Create visualization
plt.figure(figsize=(10, 6))
df['column'].hist()
plt.title('Data Distribution')
plt.savefig('chart.png')
plt.show()
"""

execution = sandbox.run_code(code)

# Check for charts in results
for result in execution.results:
    if result._repr_png_():
        # Save chart (base64 encoded PNG)
        with open('output_chart.png', 'wb') as f:
            f.write(base64.b64decode(result._repr_png_()))
```

## Error Handling

```python
execution = sandbox.run_code("1/0")  # Division by zero

if execution.error:
    print(f"Error: {execution.error.name}")
    print(f"Message: {execution.error.value}")
    print(f"Traceback: {execution.error.traceback}")
```

## Best Practices

1. **Resource Management**: Sandboxes auto-close after 5 minutes, but explicitly close them when done
2. **Error Handling**: Always check `execution.error` before processing results
3. **File Paths**: Use absolute paths for reliability
4. **Timeouts**: Set appropriate timeouts for long-running operations
5. **Persistence**: Use pause/resume for long-running workflows
6. **Security**: Sandboxes are isolated but avoid running untrusted code

## Pricing & Limits

- **Hobby Tier**: 1GB disk space
- **Pro Tier**: 5GB disk space
- **Free Credits**: $100 for new accounts
- **Timeout**: Default 5 minutes per sandbox

## Common Use Cases

- AI code execution and validation
- Data analysis and visualization
- Code generation testing
- Educational coding environments
- Automated code evaluation
- Research and experimentation

## Links

- Documentation: https://e2b.dev/docs
- Python SDK: https://pypi.org/project/e2b-code-interpreter/
- GitHub: https://github.com/e2b-dev/code-interpreter
- Examples: https://github.com/e2b-dev/e2b-cookbook
