# Phase 2: Update E2B Integration

## Current Status
✅ **Phase 1 COMPLETED**: 
- e2b-code-interpreter installed successfully
- smolagents partially installed (missing PIL dependency due to hash mismatch)

## Phase 2 Objectives
Update the existing e2b integration to use the latest API and add proper resource limits, network ACLs, and audit logging.

## Actions Required

### 2.1 Update exec_manager_e2b.py
**File**: `wheel_trader/exec_manager_e2b.py`

**Current Issues**:
- Uses outdated e2b API
- Missing resource limits (256MB RAM, 0.5 CPU, 30s timeout)
- No network ACL implementation
- Basic error handling
- No audit logging integration

**Updates Needed**:
1. Import latest e2b API: `from e2b_code_interpreter import Sandbox`
2. Add resource limits configuration
3. Implement network ACL (allow-list domains)
4. Add comprehensive error handling
5. Add execution metrics collection
6. Add audit logging to database

### 2.2 Create Database Tables
**Files**: Create SQL migration files

**Tables to Create**:
```sql
-- Execution audit log
CREATE TABLE exec_audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    container_id TEXT,
    agent_name TEXT,
    job_name TEXT,
    cpu_ms INTEGER,
    mem_peak_mb INTEGER,
    duration_ms INTEGER,
    exit_code INTEGER,
    success BOOLEAN,
    error_message TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Agent health tracking
CREATE TABLE agent_health (
    agent_name TEXT PRIMARY KEY,
    version TEXT,
    health_score DECIMAL(3,2),
    last_updated TIMESTAMPTZ DEFAULT NOW(),
    total_runs INTEGER DEFAULT 0,
    successful_runs INTEGER DEFAULT 0,
    avg_duration_ms INTEGER DEFAULT 0
);
```

### 2.3 Update Sample Agent Test
**File**: `test_e2b.py`

**Updates**:
- Test with updated exec_manager_e2b.py
- Add resource limit testing
- Add audit logging verification
- Add error handling tests

### 2.4 Network ACL Implementation
**Approach**: Since e2b doesn't directly support network ACLs in the Python SDK, we'll implement this as:
1. Documentation of allowed domains
2. Code-level validation in agents
3. Monitoring and alerting for unauthorized requests

**Allowed Domains**:
- polygon.io (market data)
- supabase.co (database)
- api.openai.com (AI services)
- barchart.com (additional market data)
- marketdata.app (market data)
- optionwatch.io (options data)

## Implementation Steps

### Step 1: Update exec_manager_e2b.py
- Modernize e2b API usage
- Add resource limits
- Add audit logging
- Improve error handling

### Step 2: Create Database Migration
- Create SQL files for new tables
- Test table creation

### Step 3: Test Updated Integration
- Run test_e2b.py with updated code
- Verify audit logging works
- Test resource limits

### Step 4: Document Network ACL
- Create network policy documentation
- Add validation helpers

## Risk Assessment
- **LOW**: Updating e2b API calls (well documented)
- **LOW**: Adding database tables (straightforward SQL)
- **MEDIUM**: Resource limit testing (need to verify limits work)
- **HIGH**: Network ACL (limited e2b SDK support)

## Success Criteria
1. ✅ Updated exec_manager_e2b.py uses latest e2b API
2. ✅ Resource limits are configured and enforced
3. ✅ Audit logging writes to database
4. ✅ test_e2b.py passes with updated code
5. ✅ Error handling is comprehensive
6. ✅ Documentation is updated

## Next Phase
After Phase 2 completion, proceed to Phase 3 (smolagents integration) once PIL dependency issue is resolved.
