# Secure Execution Implementation Plan

## Objective
Implement secure execution for agents by running a sample agent inside an e2b sandbox. This will serve as the foundation for subsequent memory and observability integration.

---

## A. Directory Structure

- `activity/secure_execution.md` (this plan)
- `agents/` (existing, will contain the sample agent)
- `wheel_trader/exec_manager_e2b.py` (driver for e2b execution)
- Other supporting files as needed

---

## B. Implementation Steps

### 1. Sample Agent Creation
- Create a minimal agent in `agents/sample_agent.py` that performs a simple, deterministic task (e.g., fetches latest options or prints a message).
- Ensure the agent can be invoked programmatically.

### 2. e2b Sandbox Integration
- Implement `exec_manager_e2b.py` in `wheel_trader/`:
  - Expose a `run_job(name, code, env)` API.
  - **Integration Steps (to be completed once e2b SDK is available):**
    1. Import the correct e2b Python SDK package (confirm import path, e.g., `from e2b_code_interpreter import Sandbox`).
    2. Initialize the sandbox with appropriate environment variables and resource limits (256 MiB RAM, 0.5 CPU, 30s timeout).
    3. Enforce outbound network ACL (allow only polygon.io, supabase.co, api.openai.com, barchart.com, marketdata.app, optionwatch.io).
    4. Use the SDK's method (e.g., `run_code`) to execute the agent's code in the sandbox.
    5. Capture and return execution logs, success/failure, and any available resource usage stats (cpu_ms, mem_peak, exit_code).
    6. Handle exceptions and errors gracefully, returning structured error information.
    7. Ensure the implementation is fully tested with both normal and resource-violation scenarios.
  - Stream logs and capture execution stats (cpu_ms, mem_peak, exit_code).
  - Return results and errors to the caller.

### 3. Agent Execution via Sandbox
- Modify the agent invocation flow so that the sample agent is executed via `exec_manager_e2b.run_job`.
- Ensure the agent's code is submitted to the sandbox, not run directly on the host.

### 4. Audit Logging
- Create or update the `exec_audit_log` table (SQL migration).
- Log each execution: container_id, cpu_ms, mem_peak, exit_code, timestamp.

### 5. Testing
- Write a test (e.g., `test_e2b.py`) to:
  - Run the sample agent in the sandbox.
  - Assert correct output and that resource limits are enforced (e.g., infinite loop is killed).

### 6. Documentation
- Document setup and usage in `README.md` and/or a dedicated section in `activity/secure_execution.md`.

---

## C. Open Questions

1. **Sample Agent Task:**  
   Should the sample agent simply print a message, fetch options data, or perform another specific task?  
   (Please specify the desired behavior.)
   Ans: The sample agent should fetch options data, since this is a trading agent we want to work on

2. **e2b Configuration:**  
   Is the e2b Python SDK already installed, or should setup instructions be included?  
   (If not installed, I will add setup steps.)
   Ans: e2b python SDK is already installed

3. **Database:**  
   Should the audit log be stored in the existing database (`database.sql`), or a new one?  
   (Default: use existing.)
   Ans: I have added to the .env my supabase credentials and the table is created their already, so we can use that. 

4. **Network ACL:**  
   Are the allowed domains for outbound traffic limited to polygon.io, supabase.co, and api.openai.com, or should others be included?
   Ans: For now let's limit the traffic to the ones mentioned, but if you see others that can be included do let me know, then we include them. for example we can include barchart.com, marketdata.app, optionwatch.io, as these are all necessary places to get data from.

5. **Testing:**  
   Should the test simulate a resource violation (e.g., infinite loop), or just verify normal execution?
   Ans: Test should verify normal execution, simulate resource violation, and any edge cases required

---

## D. Next Steps

- Await your approval or feedback on this plan.
- Once approved, proceed with implementation as described.

---

*Please review and provide feedback or approval. I will not proceed until you confirm this plan or answer the open questions above.*
