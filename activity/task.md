# Project Roadmap: Memory, Observability, Secure Execution, and Evaluation

## Epic 1: Long-Term Memory & Observability (MEM-O11)
**Tech Stack:** smolagents + Phoenix (Arize AI)

**User Story:**  
As an Agent Developer, I want every smolagent/tool call to (1) save embeddings of key events for RAG retrieval and (2) emit Phoenix traces (spans + metrics), so that I can debug failures in one UI and let agents recall prior context.

**Acceptance Criteria:**
- Phoenix Tracer Integration: `observability.py` decorator wraps each Tool.run / Agent.__call__() with `from phoenix import trace` context. Traces visible in Phoenix UI (http://localhost:6006).
- Memory Writer (`memory_writer.py`): After trace completes, embeds JSON payload via openai.Embedding and upserts to `memory_embeddings` table. Span ID stored alongside embedding to link trace ↔ memory.
- Memory Search Tool (MemoryTool, smolagents): Queries pgvector with cosine distance; returns JSON blobs to calling agent.
- Dashboard Panel: Phoenix auto-plots p95 latency & error-rate; link each span to Supabase row.
- Unit Test: `pytest::test_trace_and_memory` executes dummy tool; asserts a span exists in Phoenix export + embedding row count++.

**Sub-tasks:**
- O11.1: SQL create `memory_embeddings` with vector(1536)
- O11.2: `observability.py` Phoenix decorator
- O11.3: `memory_writer.py` embedding helper
- O11.4: MemoryTool wrapper
- O11.5: Phoenix docker-compose service + docs

---

## Epic 2: Secure Code Execution via e2b (SEC-E2B)
**Tech Stack:** e2b Containers

**User Story:**  
As a Security Engineer, I want smolagents to execute their code inside e2b containers with 256 MiB RAM / 0.5 CPU and outbound-host allow-list, so that malicious or runaway code cannot harm infra.

**Acceptance Criteria:**
- e2b Workspace Driver (`exec_manager_e2b.py`): `run_job(name, code, env)` spins up e2b container runtime:python3.11, streams logs, enforces maxRuntime=30s.
- smolagents Adapter: Monkey-patch Tool.run to submit the tool’s Python code string to `exec_manager_e2b.run_job`.
- Outbound ACL: e2b policy file only allows domains polygon.io, supabase.co, api.openai.com.
- Execution Audit (`exec_audit_log`): Keeps container_id, cpu_ms, mem_peak, exit_code.
- Fail-safe: If tool exceeds limits, e2b returns OOMKilled; Wheel Agent receives error and logs alert.

**Sub-tasks:**
- E2B.1: SQL create `exec_audit_log`
- E2B.2: `exec_manager_e2b.py` driver
- E2B.3: Patch smolagents decorator
- E2B.4: Allow-list policy YAML in repo
- E2B.5: CI test that infinite loop is killed ≤30 s

---

## Epic 3: Continuous Monitoring & Evaluation (MON-EVL)
**Tech Stack:** smolagents evaluation API + workflow

**User Story:**  
As a Team Lead, I want to record success/failure, latency, and custom quality scores for every agent execution, compare A/B versions, and gate deployments when health < 70%.

**Acceptance Criteria:**
- Evaluation Harness (`evaluator.py`): Follows Hugging Face smolagents course recipe: captures AgentResult, computes correct, latency, hallucination_score, etc.
- agent_metrics Table: Stores per-run agent, version, metrics_json, ts.
- Nightly Reporter (`eval_report.py`): Aggregates last-24 h metrics → writes agent_health.score = 1-error_rate-latency_penalty.
- Deployment Gate: Wheel Agent refuses to run agent versions with health <0.7 (uses same gate as SEC story, but driven by eval metrics).
- Grafana Panel: Trend of health scores per agent version.

**Sub-tasks:**
- EVL.1: SQL create `agent_metrics` & `agent_health`
- EVL.2: `evaluator.py` based on smolagents course code
- EVL.3: `eval_report.py` nightly cron
- EVL.4: Wheel-Agent health gate
- EVL.5: Grafana dashboard json

---

## Model Deployment for Options Trading Agents

- **Recommended Model:** FinGPT-Forecaster Llama-2 7B (GGUF) for options trading tasks, or Mistral-Stock-Model for lower memory.
- **Deployment:** Use Ollama or llama.cpp on a DigitalOcean droplet (2 vCPU, 4 GiB RAM, $18-24/mo). Expose via OpenAI-compatible API for agent orchestration.
- **Integration:** Register local LLM endpoint in smolagents/e2b; use OpenAI o3 for fallback or function-calling.

---

## Previous Tasks (for reference)
- Setup running the application using e2b
- Setup a sample agent to get the latest options
- Run it in e2b, bare metal, and docker

---

**Next Steps:**  
Prioritize implementation of the above epics, starting with secure execution (e2b) and a sample agent (smolagents) then memory/observability (Phoenix + smolagents), then monitoring/evaluation (smolagents eval API).
