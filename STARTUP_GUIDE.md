# CoveredCalls-Agents Startup Guide with Phoenix Integration

This guide provides step-by-step instructions for starting the CoveredCalls-Agents application with proper Phoenix observability integration.

## 🎯 Quick Start (Recommended)

### Option 1: Using the Startup Script
```bash
# Activate virtual environment
source venv/bin/activate

# Run the automated startup script
./start.sh
```

### Option 2: Using Python Startup Script
```bash
# Activate virtual environment
source venv/bin/activate

# Run the comprehensive startup script
python start_with_phoenix.py
```

## 📋 Prerequisites

Before starting, ensure you have:

1. **Docker and Docker Compose** installed
2. **Python 3.9+** with virtual environment activated
3. **Required API Keys** configured in `.env` file
4. **Phoenix dependencies** installed (included in requirements.txt)

## 🔧 Step-by-Step Setup

### Step 1: Environment Configuration

1. **Copy environment template:**
   ```bash
   cp .env.example .env
   ```

2. **Edit `.env` file with your actual values:**
   ```bash
   # Required API Keys
   SUPABASE_URL=your_supabase_project_url
   SUPABASE_KEY=your_supabase_service_role_key
   OPENAI_API_KEY=your_openai_api_key
   E2B_API_KEY=your_e2b_api_key
   POLYGON_API_KEY=your_polygon_api_key
   
   # Phoenix Configuration (defaults work for local setup)
   PHOENIX_COLLECTOR_ENDPOINT=http://localhost:6006/v1/traces
   PHOENIX_PROJECT_NAME=coveredcalls-agents
   PHOENIX_ENABLE_TRACING=true
   ```

### Step 2: Start Phoenix Observability

Phoenix should be running in Docker. Check status:
```bash
docker ps --filter "name=phoenix"
```

If not running, start Phoenix:
```bash
# Using docker-compose
docker-compose -f docker-compose.phoenix.yml up -d

# Or using the setup script
./phoenix/setup-phoenix.sh
```

### Step 3: Verify Phoenix Integration

Test that Phoenix integration is working:
```bash
source venv/bin/activate
python test_phoenix_integration.py
```

Expected output should show 3/4 tests passing (ObservabilityManager may have minor issues but core functionality works).

### Step 4: Start the Application

**Method A: Direct Flask startup**
```bash
source venv/bin/activate
export PHOENIX_COLLECTOR_ENDPOINT=http://localhost:6006/v1/traces
export PHOENIX_PROJECT_NAME=coveredcalls-agents
python app.py
```

**Method B: Using startup scripts**
```bash
# Quick start
./start.sh

# Or comprehensive startup
python start_with_phoenix.py
```

## 🌐 Access Points

Once started, you can access:

- **Application**: http://localhost:8000
- **Phoenix UI**: http://localhost:6006
- **Grafana Dashboards**: http://localhost:3001 (admin/phoenix_admin)

## 🔍 Verification Steps

### 1. Check Application Health
```bash
curl http://localhost:8000/
```

### 2. Verify Phoenix Tracing
1. Open Phoenix UI: http://localhost:6006
2. Navigate to "Traces" section
3. You should see traces from the application

### 3. Test Agent Execution
```bash
curl -X POST http://localhost:8000/execute_task \
  -H "Content-Type: application/json" \
  -d '{"task": "Analyze AAPL for covered call opportunities"}'
```

## 🚨 Troubleshooting

### Phoenix Not Accessible
```bash
# Check Docker containers
docker ps --filter "name=phoenix"

# Restart Phoenix
docker-compose -f docker-compose.phoenix.yml down
docker-compose -f docker-compose.phoenix.yml up -d

# Wait for startup
sleep 10
```

### Missing Dependencies
```bash
# Reinstall requirements
pip install -r requirements.txt

# Specifically install Phoenix packages
pip install arize-phoenix-otel>=5.0.0
pip install openinference-instrumentation-openai>=0.1.0
pip install openinference-instrumentation-langchain>=0.1.0
```

### Environment Variables Not Set
```bash
# Check current environment
env | grep PHOENIX

# Set manually if needed
export PHOENIX_COLLECTOR_ENDPOINT=http://localhost:6006/v1/traces
export PHOENIX_PROJECT_NAME=coveredcalls-agents
export PHOENIX_ENABLE_TRACING=true
```

### Application Won't Start
```bash
# Check virtual environment
which python
# Should show path in venv/bin/python

# Check .env file exists
ls -la .env

# Check required API keys
grep -E "(SUPABASE_URL|OPENAI_API_KEY|E2B_API_KEY)" .env
```

## 📊 Monitoring and Observability

### Phoenix Features Available:
- **Real-time trace monitoring** for all agent operations
- **Performance metrics** and latency tracking
- **Error tracking** and debugging information
- **Memory system integration** for context correlation
- **Agent health monitoring** with automatic gating

### Key Metrics to Monitor:
- Agent execution times
- Success/failure rates
- Memory retrieval performance
- API call latencies
- Health scores

## 🔄 Development Workflow

For development with Phoenix integration:

1. **Start Phoenix** (once per session)
2. **Activate virtual environment**
3. **Set environment variables**
4. **Start application** with debug mode
5. **Monitor traces** in Phoenix UI
6. **Test agent operations**
7. **Review performance** in dashboards

## ✅ Verification Checklist

After startup, verify everything is working:

- [ ] Phoenix UI accessible at http://localhost:6006
- [ ] Application accessible at http://localhost:8000
- [ ] Test request succeeds: `curl -X POST http://localhost:8000/execute_task -H "Content-Type: application/json" -d '{"task": "Test"}'`
- [ ] Traces visible in Phoenix UI under "Traces" section
- [ ] No error messages in application logs
- [ ] Virtual environment activated (`which python` shows venv path)

## 🎉 Success Indicators

When everything is working correctly, you should see:

1. **Application logs** showing Phoenix OTEL configuration
2. **Phoenix UI** displaying real-time traces
3. **Successful API responses** from test requests
4. **Trace data** correlating with agent operations
5. **Health monitoring** active in the system

## 📞 Support

If you encounter issues:

1. **Check logs** in the terminal output
2. **Verify Phoenix status** at http://localhost:6006
3. **Run integration tests** with `python test_phoenix_integration.py`
4. **Check Docker containers** with `docker ps`
5. **Review environment variables** in `.env` file

The application is designed to work with fallback modes if Phoenix is unavailable, but full observability requires Phoenix to be running and properly configured.

## 🚀 Next Steps

Once the application is running with Phoenix integration:

1. **Explore Phoenix UI** to understand trace visualization
2. **Test different agent operations** to see various trace patterns
3. **Monitor performance metrics** in real-time
4. **Set up custom dashboards** in Grafana (optional)
5. **Configure alerts** for production monitoring

Your CoveredCalls-Agents application is now ready for production use with comprehensive observability!
