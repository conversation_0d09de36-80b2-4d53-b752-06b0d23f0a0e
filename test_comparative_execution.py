#!/usr/bin/env python3
"""
Test script to demonstrate comparative agent execution

This script shows how the conservative and aggressive agents
behave differently when given the same trading tasks.
"""

import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_biotech_stock_analysis():
    """Test how both agents handle biotech stock analysis"""
    print("🧪 TEST 1: Biotech Stock Analysis")
    print("=" * 60)
    
    from agents.agents import run_comparative_test, print_comparison_results
    
    # Test with a biotech stock - should show different behavior
    task = "Analyze MRNA for covered call opportunities"
    
    results = run_comparative_test(task)
    print_comparison_results(results)
    
    # Check if conservative agent filtered out biotech
    conservative_result = results.get("conservative", {})
    aggressive_result = results.get("aggressive", {})
    
    print("\n🔍 ANALYSIS:")
    if conservative_result.get("success") and "filtered_out" in str(conservative_result.get("result", "")):
        print("✅ Conservative agent correctly filtered biotech stock")
    elif not conservative_result.get("success"):
        print("⚠️ Conservative agent had an error (expected if biotech filtering works)")
    else:
        print("❓ Conservative agent behavior unclear")
        
    if aggressive_result.get("success"):
        print("✅ Aggressive agent processed biotech stock")
    else:
        print("❌ Aggressive agent failed unexpectedly")


def test_regular_stock_analysis():
    """Test how both agents handle regular stock analysis"""
    print("\n\n🧪 TEST 2: Regular Stock Analysis")
    print("=" * 60)
    
    from agents.agents import run_comparative_test, print_comparison_results
    
    # Test with a regular stock - both should work the same
    task = "Analyze AAPL for covered call opportunities"
    
    results = run_comparative_test(task)
    print_comparison_results(results)
    
    # Check if both agents processed regular stock
    conservative_result = results.get("conservative", {})
    aggressive_result = results.get("aggressive", {})
    
    print("\n🔍 ANALYSIS:")
    if conservative_result.get("success"):
        print("✅ Conservative agent processed regular stock")
    else:
        print("❌ Conservative agent failed on regular stock")
        
    if aggressive_result.get("success"):
        print("✅ Aggressive agent processed regular stock")
    else:
        print("❌ Aggressive agent failed on regular stock")


def test_tool_differences():
    """Test that agents have different tools"""
    print("\n\n🧪 TEST 3: Tool Differences")
    print("=" * 60)
    
    try:
        from agents.tools import conservative_market_analysis_tool, aggressive_market_analysis_tool
        
        print("Testing tool behavior directly...")
        
        # Test conservative tool with biotech
        print("\n🔒 Conservative tool with MRNA:")
        cons_result = conservative_market_analysis_tool("MRNA", "rsi")
        print(f"   Result: {cons_result}")
        
        # Test aggressive tool with biotech
        print("\n🚀 Aggressive tool with MRNA:")
        agg_result = aggressive_market_analysis_tool("MRNA", "rsi")
        print(f"   Result: {agg_result}")
        
        # Test both with regular stock
        print("\n📊 Both tools with AAPL:")
        cons_aapl = conservative_market_analysis_tool("AAPL", "rsi")
        agg_aapl = aggressive_market_analysis_tool("AAPL", "rsi")
        print(f"   Conservative: {cons_aapl.get('status', 'success')}")
        print(f"   Aggressive: {agg_aapl.get('status', 'success')}")
        
    except Exception as e:
        print(f"❌ Tool test failed: {e}")


def main():
    """Run all comparative tests"""
    print("🚀 Comparative Agent Testing")
    print("=" * 60)
    print("This demonstrates how conservative and aggressive agents")
    print("behave differently with biotech vs regular stocks.")
    print()
    
    try:
        # Test 1: Biotech stock (should show difference)
        test_biotech_stock_analysis()
        
        # Test 2: Regular stock (should be similar)
        test_regular_stock_analysis()
        
        # Test 3: Direct tool testing
        test_tool_differences()
        
        print("\n\n🎉 SUMMARY")
        print("=" * 60)
        print("✅ Comparative testing system working!")
        print("📋 Key Differences Observed:")
        print("   • Conservative agent filters biotech stocks")
        print("   • Aggressive agent analyzes all stocks")
        print("   • Both agents work the same on regular stocks")
        print("\n🎯 Ready for next step: Parallel execution in E2B!")
        
    except Exception as e:
        print(f"\n❌ Testing failed: {e}")
        print("Make sure the application is running and dependencies are installed.")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
