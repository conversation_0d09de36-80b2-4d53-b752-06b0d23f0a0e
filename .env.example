# CoveredCalls-Agents Environment Configuration

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Supabase Configuration (Required)
SUPABASE_URL=your_supabase_project_url_here
SUPABASE_KEY=your_supabase_service_role_key_here

# =============================================================================
# API KEYS (Required for full functionality)
# =============================================================================
# OpenAI API Key (Required for LLM operations)
OPENAI_API_KEY=your_openai_api_key_here

# E2B API Key (Required for secure code execution)
E2B_API_KEY=your_e2b_api_key_here

# Polygon API Key (Required for market data)
POLYGON_API_KEY=your_polygon_api_key_here

# =============================================================================
# PHOENIX OBSERVABILITY CONFIGURATION
# =============================================================================
# Phoenix Collector Endpoint (Default: http://localhost:6006/v1/traces)
PHOENIX_COLLECTOR_ENDPOINT=http://localhost:6006/v1/traces

# Phoenix Project Name (Default: coveredcalls-agents)
PHOENIX_PROJECT_NAME=coveredcalls-agents

# Enable Phoenix Tracing (Default: true)
PHOENIX_ENABLE_TRACING=true

# Phoenix API Key (Optional - only needed for Phoenix Cloud)
# PHOENIX_API_KEY=your_phoenix_cloud_api_key_here

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# Flask Configuration
FLASK_ENV=development
FLASK_DEBUG=true

# Agent Health Monitoring
AGENT_HEALTH_THRESHOLD=0.7
ENABLE_HEALTH_MONITORING=true

# Memory System Configuration
MEMORY_BACKEND=pgvector
ENABLE_MEMORY_SYSTEM=true

# =============================================================================
# OPTIONAL CONFIGURATIONS
# =============================================================================
# Logging Level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# Enable Observability Features
ENABLE_OBSERVABILITY=true

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING=true

# =============================================================================
# DEVELOPMENT/TESTING OVERRIDES
# =============================================================================
# Uncomment these for development/testing
# SUPABASE_URL=http://localhost:54321
# PHOENIX_COLLECTOR_ENDPOINT=http://localhost:6006/v1/traces
# OPENAI_API_KEY=sk-test-key-for-development
