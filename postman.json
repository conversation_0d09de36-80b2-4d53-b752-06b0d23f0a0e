{"info": {"_postman_id": "coveredcalls-agents-api", "name": "CoveredCalls-Agents API Collection", "description": "Comprehensive Postman collection for testing the CoveredCalls-Agents application with Phoenix observability integration.\n\n## Features Tested:\n- Agent task execution with E2B sandboxes\n- Market analysis and options data retrieval\n- Risk assessment and trading strategies\n- Health monitoring and system status\n- Phoenix observability integration\n\n## Prerequisites:\n- Application running on http://localhost:8000\n- Phoenix observability running on http://localhost:6006\n- Valid API keys configured in .env file\n- Virtual environment activated\n\n## Usage:\n1. Import this collection into Postman\n2. Set up the environment variables\n3. Start the CoveredCalls-Agents application\n4. Run the requests to test functionality\n\n## Observability:\nAll requests will generate traces in Phoenix UI at http://localhost:6006", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Health & Status", "description": "Health checks and system status endpoints", "item": [{"name": "Application Home Page", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/", "host": ["{{base_url}}"], "path": [""]}, "description": "Get the main documentation page of the application"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains application title', function () {", "    pm.expect(pm.response.text()).to.include('Covered Calls');", "});"], "type": "text/javascript"}}]}, {"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "Check application health status including coordinator and tools availability"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Application is healthy', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.healthy).to.be.true;", "});", "", "pm.test('Coordinator is operational', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.coordinator).to.eql('operational');", "});"], "type": "text/javascript"}}]}, {"name": "System Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/status", "host": ["{{base_url}}"], "path": ["status"]}, "description": "Get detailed system status including agent health, memory system, and observability"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response contains status information', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('status');", "});"], "type": "text/javascript"}}]}, {"name": "Available Tools", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/tools", "host": ["{{base_url}}"], "path": ["tools"]}, "description": "List all available trading tools and their descriptions"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Tools are available', function () {", "    const jsonData = pm.response.json();", "    pm.expect(Object.keys(jsonData).length).to.be.greaterThan(0);", "});", "", "pm.test('Market analysis tool is available', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('market_analysis');", "});"], "type": "text/javascript"}}]}]}, {"name": "Agent Operations", "description": "Core agent task execution endpoints", "item": [{"name": "Basic Task Execution", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"task\": \"Test Phoenix integration\"\n}"}, "url": {"raw": "{{base_url}}/execute_task", "host": ["{{base_url}}"], "path": ["execute_task"]}, "description": "Execute a basic task to test agent functionality and Phoenix tracing"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Task executed successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});", "", "pm.test('Response contains execution time', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('execution_time');", "    pm.expect(jsonData.execution_time).to.be.a('number');", "});", "", "pm.test('Response contains result', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('result');", "});"], "type": "text/javascript"}}]}, {"name": "Invalid Task Request", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"invalid_field\": \"test\"\n}"}, "url": {"raw": "{{base_url}}/execute_task", "host": ["{{base_url}}"], "path": ["execute_task"]}, "description": "Test error handling for invalid request format"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 400', function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test('Error message is returned', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.false;", "    pm.expect(jsonData).to.have.property('error');", "});"], "type": "text/javascript"}}]}]}, {"name": "Trading Analysis", "description": "Market analysis and trading strategy requests", "item": [{"name": "Market Analysis - RSI", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"task\": \"Analyze AAPL RSI for trading signals\"\n}"}, "url": {"raw": "{{base_url}}/execute_task", "host": ["{{base_url}}"], "path": ["execute_task"]}, "description": "Perform RSI technical analysis on AAPL stock"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Analysis completed successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});", "", "pm.test('RSI analysis result returned', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.result).to.have.property('analysis_type', 'rsi');", "    pm.expect(jsonData.result).to.have.property('rsi_value');", "});"], "type": "text/javascript"}}]}, {"name": "Market Analysis - SMA", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"task\": \"Analyze TSLA SMA for trend analysis\"\n}"}, "url": {"raw": "{{base_url}}/execute_task", "host": ["{{base_url}}"], "path": ["execute_task"]}, "description": "Perform Simple Moving Average analysis on TSLA stock"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('SMA analysis completed', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.result).to.have.property('analysis_type', 'rsi');", "});"], "type": "text/javascript"}}]}, {"name": "Market Analysis - Bollinger Bands", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"task\": \"Analyze MSFT Bollinger bands for volatility assessment\"\n}"}, "url": {"raw": "{{base_url}}/execute_task", "host": ["{{base_url}}"], "path": ["execute_task"]}, "description": "Perform Bollinger Bands analysis on MSFT stock for volatility assessment"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('<PERSON><PERSON><PERSON> analysis completed', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});"], "type": "text/javascript"}}]}]}, {"name": "Options Trading", "description": "Options data retrieval and covered call analysis", "item": [{"name": "Options Chain Data", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"task\": \"Get AAPL options chain for next Friday expiration\"\n}"}, "url": {"raw": "{{base_url}}/execute_task", "host": ["{{base_url}}"], "path": ["execute_task"]}, "description": "Retrieve options chain data for AAPL with next Friday expiration"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Options data retrieved', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});"], "type": "text/javascript"}}]}, {"name": "Covered Call Analysis", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"task\": \"Analyze AAPL for covered call opportunities with high premium\"\n}"}, "url": {"raw": "{{base_url}}/execute_task", "host": ["{{base_url}}"], "path": ["execute_task"]}, "description": "Analyze AAPL for profitable covered call opportunities"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Covered call analysis completed', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData).to.have.property('result');", "});"], "type": "text/javascript"}}]}, {"name": "Multi-Symbol Options Analysis", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"task\": \"Compare options opportunities for AAPL, TSLA, and MSFT\"\n}"}, "url": {"raw": "{{base_url}}/execute_task", "host": ["{{base_url}}"], "path": ["execute_task"]}, "description": "Compare options trading opportunities across multiple symbols"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Multi-symbol analysis completed', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});"], "type": "text/javascript"}}]}]}, {"name": "Risk Management", "description": "Risk assessment and position sizing requests", "item": [{"name": "Position Risk Assessment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"task\": \"Assess risk for $10,000 AAPL position in $100,000 portfolio\"\n}"}, "url": {"raw": "{{base_url}}/execute_task", "host": ["{{base_url}}"], "path": ["execute_task"]}, "description": "Assess risk for a specific position size relative to portfolio value"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Risk assessment completed', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});", "", "pm.test('Risk metrics provided', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.result).to.have.property('position_percentage');", "    pm.expect(jsonData.result).to.have.property('risk_level');", "});"], "type": "text/javascript"}}]}, {"name": "Portfolio Risk Analysis", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"task\": \"Analyze portfolio risk for multiple positions\"\n}"}, "url": {"raw": "{{base_url}}/execute_task", "host": ["{{base_url}}"], "path": ["execute_task"]}, "description": "Comprehensive portfolio risk analysis"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Portfolio analysis completed', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});"], "type": "text/javascript"}}]}]}, {"name": "Phoenix Monitoring", "description": "Phoenix observability and monitoring endpoints", "item": [{"name": "Phoenix UI Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{phoenix_url}}/health", "host": ["{{phoenix_url}}"], "path": ["health"]}, "description": "Check if Phoenix observability UI is accessible"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Phoenix UI is accessible', function () {", "    pm.response.to.have.status(200);", "});", "", "console.log('Phoenix UI is running at: {{phoenix_url}}');"], "type": "text/javascript"}}]}, {"name": "Generate Trace Data", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"task\": \"Generate trace data for Phoenix monitoring\"\n}"}, "url": {"raw": "{{base_url}}/execute_task", "host": ["{{base_url}}"], "path": ["execute_task"]}, "description": "Execute a task specifically to generate trace data in Phoenix"}, "response": [], "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Trace generated successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "});", "", "console.log('Check Phoenix UI for new traces at: {{phoenix_url}}');"], "type": "text/javascript"}}]}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set timestamp for requests", "pm.globals.set('timestamp', new Date().toISOString());"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test to log response time", "pm.test('Response time is reasonable', function () {", "    pm.expect(pm.response.responseTime).to.be.below(30000);", "});", "", "// Log execution details", "console.log('Request:', pm.request.url.toString());", "console.log('Response time:', pm.response.responseTime + 'ms');", "console.log('Status:', pm.response.status);"]}}], "variable": [{"key": "base_url", "value": "http://localhost:8000", "description": "Base URL for the CoveredCalls-Agents application"}, {"key": "phoenix_url", "value": "http://localhost:6006", "description": "Phoenix observability UI URL"}, {"key": "timestamp", "value": "", "description": "Current timestamp for requests"}]}