# CoveredCalls-Agents Postman Collection

This comprehensive Postman collection provides complete testing coverage for the CoveredCalls-Agents application with Phoenix observability integration.

## 📋 Collection Overview

The collection includes **20+ requests** organized into **6 main categories**:

1. **Health & Status** - System health checks and status monitoring
2. **Agent Operations** - Core agent task execution and error handling
3. **Trading Analysis** - Market analysis with RSI, SMA, and Bollinger Bands
4. **Options Trading** - Options chain data and covered call analysis
5. **Risk Management** - Position sizing and portfolio risk assessment
6. **Phoenix Monitoring** - Observability and trace generation

## 🚀 Quick Setup

### 1. Import Collection
1. Open Postman
2. Click **Import** button
3. Select `postman.json` file
4. Collection will be imported with all requests and environment variables

### 2. Start Application
```bash
# Activate virtual environment
source venv/bin/activate

# Start with Phoenix integration
./start.sh
```

### 3. Verify Setup
Run the **Health Check** request to ensure everything is working.

## 🔧 Environment Variables

The collection includes pre-configured variables:

| Variable | Default Value | Description |
|----------|---------------|-------------|
| `base_url` | `http://localhost:8000` | Application base URL |
| `phoenix_url` | `http://localhost:6006` | Phoenix UI URL |
| `timestamp` | Auto-generated | Request timestamp |

## 📊 Request Categories

### Health & Status
- **Application Home Page** - GET / (documentation page)
- **Health Check** - GET /health (system health)
- **System Status** - GET /status (detailed status)
- **Available Tools** - GET /tools (list trading tools)

### Agent Operations
- **Basic Task Execution** - POST /execute_task (basic functionality)
- **Invalid Task Request** - POST /execute_task (error handling)

### Trading Analysis
- **Market Analysis - RSI** - Technical analysis with RSI
- **Market Analysis - SMA** - Simple Moving Average analysis
- **Market Analysis - Bollinger Bands** - Volatility assessment

### Options Trading
- **Options Chain Data** - Retrieve options data
- **Covered Call Analysis** - Find profitable opportunities
- **Multi-Symbol Options Analysis** - Compare multiple stocks

### Risk Management
- **Position Risk Assessment** - Evaluate position risk
- **Portfolio Risk Analysis** - Comprehensive portfolio analysis

### Phoenix Monitoring
- **Phoenix UI Health Check** - Verify Phoenix accessibility
- **Generate Trace Data** - Create traces for monitoring

## 🧪 Testing Features

### Automated Tests
Each request includes automated tests that verify:
- ✅ **Response Status** - Correct HTTP status codes
- ✅ **Response Structure** - Expected JSON structure
- ✅ **Business Logic** - Domain-specific validations
- ✅ **Performance** - Response time under 30 seconds

### Example Test Results
```javascript
✅ Status code is 200
✅ Task executed successfully
✅ Response contains execution time
✅ Response contains result
✅ Response time is reasonable
```

### Global Scripts
- **Pre-request Script** - Sets timestamp for all requests
- **Test Script** - Logs response details and validates performance

## 📈 Phoenix Integration

### Trace Generation
Every request to `/execute_task` generates traces in Phoenix:
1. Execute any trading analysis request
2. Open Phoenix UI at http://localhost:6006
3. Navigate to "Traces" section
4. View real-time execution traces

### Observability Features
- **Request Tracing** - Full request/response tracing
- **Performance Monitoring** - Execution time tracking
- **Error Tracking** - Detailed error information
- **Memory Integration** - Context-aware operations

## 🎯 Usage Examples

### Basic Workflow
1. **Health Check** - Verify system is running
2. **Available Tools** - See what tools are available
3. **Market Analysis** - Run technical analysis
4. **Options Analysis** - Find trading opportunities
5. **Risk Assessment** - Evaluate position risk
6. **Phoenix Monitoring** - Check traces

### Advanced Testing
1. Run entire collection using **Collection Runner**
2. Set up **Monitors** for continuous testing
3. Use **Newman** for CI/CD integration
4. Export **Test Results** for reporting

## 🔍 Troubleshooting

### Common Issues

**Application Not Responding**
```bash
# Check if application is running
curl http://localhost:8000/health

# Restart if needed
./start.sh
```

**Phoenix Not Accessible**
```bash
# Check Phoenix container
docker ps --filter "name=phoenix"

# Restart Phoenix
docker-compose -f docker-compose.phoenix.yml up -d
```

**Invalid API Keys**
- Check `.env` file has correct API keys
- Verify `POLYGON_API_KEY` for options data
- Ensure `OPENAI_API_KEY` for LLM operations

### Request Failures
1. **400 Bad Request** - Check JSON payload format
2. **500 Internal Error** - Check application logs
3. **503 Service Unavailable** - Coordinator not initialized

## 📝 Request Examples

### Market Analysis Request
```json
{
    "task": "Analyze AAPL RSI for trading signals"
}
```

### Options Analysis Request
```json
{
    "task": "Get AAPL options chain for next Friday expiration"
}
```

### Risk Assessment Request
```json
{
    "task": "Assess risk for $10,000 AAPL position in $100,000 portfolio"
}
```

## 🔄 CI/CD Integration

### Newman Command Line
```bash
# Install Newman
npm install -g newman

# Run collection
newman run postman.json

# Run with environment
newman run postman.json -e environment.json

# Generate reports
newman run postman.json --reporters cli,html
```

### GitHub Actions Example
```yaml
- name: Run Postman Tests
  run: |
    newman run postman.json \
      --reporters cli,junit \
      --reporter-junit-export results.xml
```

## 📞 Support

For issues with the Postman collection:

1. **Check Prerequisites** - Application and Phoenix running
2. **Verify Environment** - Correct URLs and API keys
3. **Review Logs** - Application and Phoenix logs
4. **Test Manually** - Use curl commands to isolate issues

The collection is designed to provide comprehensive testing coverage while being easy to use and maintain.
