{"dashboard": {"id": null, "title": "Enhanced Trading Analytics - Advanced Monitoring", "tags": ["trading", "analytics", "advanced", "memory", "performance"], "timezone": "America/New_York", "refresh": "15s", "time": {"from": "now-4h", "to": "now"}, "templating": {"list": [{"name": "agent", "type": "query", "datasource": "Supabase Main", "query": "SELECT DISTINCT agent_name FROM agent_metrics ORDER BY agent_name", "multi": true, "includeAll": true, "allValue": ".*", "current": {"text": "All", "value": "$__all"}}, {"name": "symbol", "type": "query", "datasource": "Supabase Main", "query": "SELECT DISTINCT (metadata->>'symbol') as symbol FROM memory_embeddings WHERE metadata->>'symbol' IS NOT NULL ORDER BY symbol", "multi": true, "includeAll": true, "allValue": ".*"}, {"name": "strategy", "type": "custom", "query": "covered_call,protective_put,iron_condor,butterfly,straddle,strangle,cash_secured_put", "multi": true, "includeAll": true, "allValue": ".*"}]}, "panels": [{"id": 1, "title": "Trading Strategy Performance Matrix", "type": "heatmap", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "targets": [{"datasource": "Supabase Main", "rawSql": "SELECT DATE_TRUNC('hour', created_at) as time, (metadata->>'strategy') as strategy, AVG(evaluation_score) as avg_score FROM agent_metrics WHERE agent_name IN ($agent) AND created_at >= $__timeFrom() AND created_at <= $__timeTo() AND metadata->>'strategy' IS NOT NULL GROUP BY DATE_TRUNC('hour', created_at), metadata->>'strategy' ORDER BY time", "format": "time_series"}], "fieldConfig": {"defaults": {"custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "color": {"mode": "continuous-GrYlRd", "steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 0.6}, {"color": "green", "value": 0.8}]}, "min": 0, "max": 1}}}, {"id": 2, "title": "Memory Effectiveness by Symbol", "type": "bargauge", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "targets": [{"datasource": "Supabase Main", "rawSql": "SELECT (metadata->>'symbol') as symbol, AVG(CASE WHEN memory_context_used THEN evaluation_score ELSE 0 END) as with_memory, AVG(CASE WHEN NOT memory_context_used THEN evaluation_score ELSE 0 END) as without_memory FROM agent_metrics WHERE agent_name IN ($agent) AND created_at >= $__timeFrom() AND metadata->>'symbol' IN ($symbol) GROUP BY metadata->>'symbol' ORDER BY with_memory DESC", "format": "table"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"displayMode": "gradient", "orientation": "horizontal"}, "min": 0, "max": 1, "unit": "percentunit"}}}, {"id": 3, "title": "Real-Time Agent Execution Flow", "type": "nodeGraph", "gridPos": {"h": 10, "w": 24, "x": 0, "y": 8}, "targets": [{"datasource": "Phoenix PostgreSQL", "rawSql": "SELECT agent_name as source, operation_type as target, COUNT(*) as value, AVG(duration_ms) as arc_weight FROM trading_analytics.agent_trace_summary WHERE start_time >= $__timeFrom() GROUP BY agent_name, operation_type ORDER BY value DESC LIMIT 50", "format": "table"}], "fieldConfig": {"defaults": {"custom": {"nodeOptions": {"mainStatUnit": "short", "secondaryStatUnit": "ms"}}}}}, {"id": 4, "title": "Hallucination Detection Trends", "type": "timeseries", "gridPos": {"h": 8, "w": 8, "x": 0, "y": 18}, "targets": [{"datasource": "Supabase Main", "rawSql": "SELECT DATE_TRUNC('minute', created_at) as time, agent_name, AVG(hallucination_score) as avg_hallucination, COUNT(*) FILTER (WHERE hallucination_score > 0.3) as high_hallucination_count FROM agent_metrics WHERE agent_name IN ($agent) AND created_at >= $__timeFrom() GROUP BY DATE_TRUNC('minute', created_at), agent_name ORDER BY time", "format": "time_series"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "smooth", "pointSize": 3, "fillOpacity": 20}, "min": 0, "max": 1, "unit": "percentunit"}}, "alert": {"conditions": [{"evaluator": {"params": [0.3], "type": "gt"}, "operator": {"type": "and"}, "query": {"params": ["A", "5m", "now"]}, "reducer": {"params": [], "type": "avg"}, "type": "query"}], "executionErrorState": "alerting", "for": "2m", "frequency": "30s", "handler": 1, "name": "High Hallucination Rate Alert", "noDataState": "no_data", "notifications": []}}, {"id": 5, "title": "Memory Context Quality Distribution", "type": "piechart", "gridPos": {"h": 8, "w": 8, "x": 8, "y": 18}, "targets": [{"datasource": "Supabase Main", "rawSql": "SELECT CASE WHEN context_relevance_score >= 0.8 THEN 'Excellent' WHEN context_relevance_score >= 0.6 THEN 'Good' WHEN context_relevance_score >= 0.4 THEN 'Fair' ELSE 'Poor' END as quality, COUNT(*) as count FROM agent_metrics WHERE agent_name IN ($agent) AND created_at >= $__timeFrom() AND memory_context_used = true GROUP BY CASE WHEN context_relevance_score >= 0.8 THEN 'Excellent' WHEN context_relevance_score >= 0.6 THEN 'Good' WHEN context_relevance_score >= 0.4 THEN 'Fair' ELSE 'Poor' END", "format": "table"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "unit": "short"}}}, {"id": 6, "title": "Performance vs Memory Usage Correlation", "type": "scatterplot", "gridPos": {"h": 8, "w": 8, "x": 16, "y": 18}, "targets": [{"datasource": "Supabase Main", "rawSql": "SELECT memory_context_count as x, evaluation_score as y, agent_name as series FROM agent_metrics WHERE agent_name IN ($agent) AND created_at >= $__timeFrom() AND memory_context_used = true ORDER BY created_at DESC LIMIT 1000", "format": "table"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"pointSize": {"fixed": 5}}}}}, {"id": 7, "title": "Trading Session Performance", "type": "stat", "gridPos": {"h": 6, "w": 6, "x": 0, "y": 26}, "targets": [{"datasource": "Supabase Main", "rawSql": "SELECT CASE WHEN EXTRACT(hour FROM created_at) BETWEEN 9 AND 16 THEN 'Market Hours' ELSE 'After Hours' END as session, AVG(evaluation_score) as avg_score, COUNT(*) as operations FROM agent_metrics WHERE agent_name IN ($agent) AND created_at >= $__timeFrom() GROUP BY CASE WHEN EXTRACT(hour FROM created_at) BETWEEN 9 AND 16 THEN 'Market Hours' ELSE 'After Hours' END", "format": "table"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds", "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 0.6}, {"color": "green", "value": 0.8}]}}, "unit": "percentunit"}}}, {"id": 8, "title": "Erro<PERSON>", "type": "table", "gridPos": {"h": 6, "w": 18, "x": 6, "y": 26}, "targets": [{"datasource": "Supabase Main", "rawSql": "SELECT agent_name, error_message, COUNT(*) as error_count, MAX(created_at) as last_occurrence FROM agent_metrics WHERE agent_name IN ($agent) AND created_at >= $__timeFrom() AND success = false AND error_message IS NOT NULL GROUP BY agent_name, error_message ORDER BY error_count DESC, last_occurrence DESC LIMIT 20", "format": "table"}], "fieldConfig": {"overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "error_count"}, "properties": [{"id": "color", "value": {"mode": "thresholds", "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 5}, {"color": "red", "value": 10}]}}}]}]}}, {"id": 9, "title": "Memory System Health", "type": "gauge", "gridPos": {"h": 6, "w": 6, "x": 0, "y": 32}, "targets": [{"datasource": "Supabase Main", "rawSql": "SELECT 'Memory Hit Rate' as metric, COUNT(*) FILTER (WHERE memory_context_used = true)::float / COUNT(*) as value FROM agent_metrics WHERE created_at >= $__timeFrom() UNION ALL SELECT 'Avg Context Relevance' as metric, AVG(context_relevance_score) as value FROM agent_metrics WHERE created_at >= $__timeFrom() AND memory_context_used = true UNION ALL SELECT 'Memory Utilization' as metric, COUNT(DISTINCT id)::float / (SELECT COUNT(*) FROM memory_embeddings) as value FROM memory_embeddings WHERE created_at >= $__timeFrom()", "format": "table"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds", "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 0.5}, {"color": "green", "value": 0.8}]}}, "min": 0, "max": 1, "unit": "percentunit"}}}, {"id": 10, "title": "Agent Workload Distribution", "type": "piechart", "gridPos": {"h": 6, "w": 6, "x": 6, "y": 32}, "targets": [{"datasource": "Phoenix PostgreSQL", "rawSql": "SELECT agent_name, COUNT(*) as operations FROM trading_analytics.agent_trace_summary WHERE start_time >= $__timeFrom() GROUP BY agent_name ORDER BY operations DESC", "format": "table"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "short"}}}, {"id": 11, "title": "Latency Percentiles by Operation", "type": "timeseries", "gridPos": {"h": 6, "w": 12, "x": 12, "y": 32}, "targets": [{"datasource": "Phoenix PostgreSQL", "rawSql": "SELECT DATE_TRUNC('minute', start_time) as time, operation_type, percentile_cont(0.50) WITHIN GROUP (ORDER BY duration_ms) as p50, percentile_cont(0.95) WITHIN GROUP (ORDER BY duration_ms) as p95, percentile_cont(0.99) WITHIN GROUP (ORDER BY duration_ms) as p99 FROM trading_analytics.agent_trace_summary WHERE start_time >= $__timeFrom() GROUP BY DATE_TRUNC('minute', start_time), operation_type ORDER BY time", "format": "time_series"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "smooth"}, "unit": "ms"}}}], "annotations": {"list": [{"name": "High Error Rate Events", "datasource": "Supabase Main", "query": "SELECT created_at as time, 'High Error Rate: ' || agent_name as text, 'error' as tags FROM (SELECT agent_name, DATE_TRUNC('hour', created_at) as created_at, COUNT(*) FILTER (WHERE success = false)::float / COUNT(*) as error_rate FROM agent_metrics WHERE created_at >= $__timeFrom() GROUP BY agent_name, DATE_TRUNC('hour', created_at) HAVING COUNT(*) FILTER (WHERE success = false)::float / COUNT(*) > 0.1) t", "iconColor": "red"}, {"name": "Memory System Updates", "datasource": "Supabase Main", "query": "SELECT created_at as time, 'Memory Update: ' || mem_type as text, 'memory' as tags FROM memory_embeddings WHERE created_at >= $__timeFrom() AND importance > 0.8", "iconColor": "blue"}]}}}