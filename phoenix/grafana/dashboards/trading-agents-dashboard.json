{"dashboard": {"id": null, "title": "Options Trading Agents - Performance & Observability", "tags": ["trading", "agents", "phoenix", "memory", "evaluation"], "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "Agent Health Scores", "type": "stat", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "targets": [{"datasource": "Supabase Main", "rawSql": "SELECT agent_name, health_score FROM agent_health ORDER BY health_score DESC", "format": "table"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 0.7}, {"color": "green", "value": 0.8}]}, "min": 0, "max": 1}}}, {"id": 2, "title": "Evaluation Scores Over Time", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "targets": [{"datasource": "Supabase Main", "rawSql": "SELECT created_at as time, agent_name, evaluation_score FROM agent_metrics WHERE created_at >= NOW() - INTERVAL '1 hour' ORDER BY created_at", "format": "time_series"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "smooth", "pointSize": 3}, "min": 0, "max": 1}}}, {"id": 3, "title": "Memory Usage Trends", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"datasource": "Supabase Main", "rawSql": "SELECT DATE_TRUNC('minute', created_at) as time, COUNT(*) as memory_count FROM memory_embeddings WHERE created_at >= NOW() - INTERVAL '1 hour' GROUP BY DATE_TRUNC('minute', created_at) ORDER BY time", "format": "time_series"}], "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "custom": {"drawStyle": "bars", "fillOpacity": 80}}}}, {"id": 4, "title": "Phoenix Trace Performance", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"datasource": "Phoenix PostgreSQL", "rawSql": "SELECT start_time as time, agent_name, duration_ms FROM trading_analytics.agent_trace_summary WHERE start_time >= NOW() - INTERVAL '1 hour' ORDER BY start_time", "format": "time_series"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "unit": "ms", "custom": {"drawStyle": "line", "pointSize": 2}}}}, {"id": 5, "title": "Agent Operations Summary", "type": "table", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 16}, "targets": [{"datasource": "Phoenix PostgreSQL", "rawSql": "SELECT * FROM trading_analytics.dashboard_metrics ORDER BY total_operations DESC", "format": "table"}], "fieldConfig": {"overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "avg_evaluation_score"}, "properties": [{"id": "color", "value": {"mode": "thresholds", "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 0.6}, {"color": "green", "value": 0.8}]}}}]}]}}, {"id": 6, "title": "Hallucination Detection", "type": "gauge", "gridPos": {"h": 8, "w": 8, "x": 0, "y": 24}, "targets": [{"datasource": "Supabase Main", "rawSql": "SELECT AVG(hallucination_score) as avg_hallucination FROM agent_metrics WHERE created_at >= NOW() - INTERVAL '1 hour'", "format": "table"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds", "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 0.3}, {"color": "red", "value": 0.5}]}}, "min": 0, "max": 1, "unit": "percentunit"}}}, {"id": 7, "title": "Memory Context Utilization", "type": "gauge", "gridPos": {"h": 8, "w": 8, "x": 8, "y": 24}, "targets": [{"datasource": "Supabase Main", "rawSql": "SELECT COUNT(*) FILTER (WHERE memory_context_used = true)::float / COUNT(*) as memory_usage_rate FROM agent_metrics WHERE created_at >= NOW() - INTERVAL '1 hour'", "format": "table"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds", "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 0.5}, {"color": "green", "value": 0.8}]}}, "min": 0, "max": 1, "unit": "percentunit"}}}, {"id": 8, "title": "Error Rate", "type": "gauge", "gridPos": {"h": 8, "w": 8, "x": 16, "y": 24}, "targets": [{"datasource": "Phoenix PostgreSQL", "rawSql": "SELECT COUNT(*) FILTER (WHERE success = false)::float / COUNT(*) as error_rate FROM trading_analytics.agent_trace_summary WHERE start_time >= NOW() - INTERVAL '1 hour'", "format": "table"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds", "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 0.05}, {"color": "red", "value": 0.1}]}}, "min": 0, "max": 0.2, "unit": "percentunit"}}}], "templating": {"list": [{"name": "agent", "type": "query", "datasource": "Supabase Main", "query": "SELECT DISTINCT agent_name FROM agent_metrics ORDER BY agent_name", "multi": true, "includeAll": true, "allValue": ".*"}, {"name": "time_range", "type": "interval", "query": "1m,5m,15m,30m,1h,3h,6h,12h,1d", "current": {"text": "5m", "value": "5m"}}]}, "annotations": {"list": [{"name": "Agent Deployments", "datasource": "Supabase Main", "query": "SELECT created_at as time, 'Agent Health Update' as text, agent_name as tags FROM agent_health WHERE created_at >= NOW() - INTERVAL '24 hours'", "iconColor": "blue"}]}}}