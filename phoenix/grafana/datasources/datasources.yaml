# Grafana Datasources Configuration for Phoenix Integration
# Connects Grafana to Phoenix PostgreSQL and main Supabase database

apiVersion: 1

datasources:
  # Phoenix PostgreSQL Database
  - name: Phoenix PostgreSQL
    type: postgres
    access: proxy
    url: phoenix-postgres:5432
    database: phoenix_db
    user: phoenix_user
    secureJsonData:
      password: phoenix_pass
    jsonData:
      sslmode: disable
      maxOpenConns: 10
      maxIdleConns: 2
      connMaxLifetime: 14400
      postgresVersion: 1500
      timescaledb: false
    isDefault: true
    editable: true

  # Main Supabase Database (for agent_metrics and memory data)
  - name: Supabase Main
    type: postgres
    access: proxy
    url: ${SUPABASE_DB_HOST:localhost:5432}
    database: ${SUPABASE_DB_NAME:postgres}
    user: ${SUPABASE_DB_USER:postgres}
    secureJsonData:
      password: ${SUPABASE_DB_PASSWORD:your_password}
    jsonData:
      sslmode: require
      maxOpenConns: 10
      maxIdleConns: 2
      connMaxLifetime: 14400
      postgresVersion: 1400
      timescaledb: false
    editable: true

  # Prometheus (if available)
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: false
    editable: true
    jsonData:
      httpMethod: POST
      queryTimeout: 60s
      timeInterval: 15s
