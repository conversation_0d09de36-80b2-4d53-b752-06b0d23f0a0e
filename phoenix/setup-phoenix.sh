#!/bin/bash
# Phoenix Observability Setup Script
# Sets up Phoenix with custom configuration for options trading agents

set -e

echo "🚀 Setting up Phoenix Observability for Options Trading Agents..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    print_status "Checking Docker availability..."
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    print_success "Docker is running"
}

# Check if docker-compose is available
check_docker_compose() {
    print_status "Checking docker-compose availability..."
    if ! command -v docker-compose &> /dev/null; then
        print_error "docker-compose is not installed. Please install docker-compose and try again."
        exit 1
    fi
    print_success "docker-compose is available"
}

# Create necessary directories
create_directories() {
    print_status "Creating Phoenix directories..."
    
    directories=(
        "phoenix/data"
        "phoenix/config"
        "phoenix/postgres_data"
        "phoenix/redis_data"
        "phoenix/grafana_data"
        "phoenix/logs"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_status "Created directory: $dir"
        fi
    done
    
    print_success "All directories created"
}

# Set proper permissions
set_permissions() {
    print_status "Setting proper permissions..."
    
    # Set permissions for data directories
    chmod 755 phoenix/data
    chmod 700 phoenix/postgres_data
    chmod 755 phoenix/redis_data
    chmod 755 phoenix/grafana_data
    
    # Set permissions for configuration files
    chmod 644 phoenix/custom_config/phoenix.yaml
    chmod 644 phoenix/grafana/datasources/datasources.yaml
    chmod 644 phoenix/grafana/dashboards/*.yaml
    chmod 644 phoenix/grafana/dashboards/*.json
    
    print_success "Permissions set"
}

# Validate configuration files
validate_config() {
    print_status "Validating configuration files..."
    
    required_files=(
        "docker-compose.phoenix.yml"
        "phoenix/custom_config/phoenix.yaml"
        "phoenix/init-scripts/01-init-phoenix.sql"
        "phoenix/grafana/datasources/datasources.yaml"
        "phoenix/grafana/dashboards/dashboards.yaml"
        "phoenix/grafana/dashboards/trading-agents-dashboard.json"
    )
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            print_error "Required file not found: $file"
            exit 1
        fi
    done
    
    print_success "All configuration files validated"
}

# Start Phoenix services
start_services() {
    print_status "Starting Phoenix services..."
    
    # Stop any existing services
    docker-compose -f docker-compose.phoenix.yml down > /dev/null 2>&1 || true
    
    # Start services
    docker-compose -f docker-compose.phoenix.yml up -d
    
    if [ $? -eq 0 ]; then
        print_success "Phoenix services started successfully"
    else
        print_error "Failed to start Phoenix services"
        exit 1
    fi
}

# Wait for services to be healthy
wait_for_services() {
    print_status "Waiting for services to be healthy..."
    
    # Wait for PostgreSQL
    print_status "Waiting for PostgreSQL..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if docker-compose -f docker-compose.phoenix.yml exec -T phoenix-postgres pg_isready -U phoenix_user -d phoenix_db > /dev/null 2>&1; then
            print_success "PostgreSQL is ready"
            break
        fi
        sleep 2
        timeout=$((timeout - 2))
    done
    
    if [ $timeout -le 0 ]; then
        print_error "PostgreSQL failed to start within timeout"
        exit 1
    fi
    
    # Wait for Phoenix
    print_status "Waiting for Phoenix..."
    timeout=120
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost:6006/health > /dev/null 2>&1; then
            print_success "Phoenix is ready"
            break
        fi
        sleep 5
        timeout=$((timeout - 5))
    done
    
    if [ $timeout -le 0 ]; then
        print_warning "Phoenix may not be fully ready yet, but continuing..."
    fi
}

# Display service information
show_service_info() {
    print_success "Phoenix Observability Setup Complete!"
    echo ""
    echo "📊 Service URLs:"
    echo "  Phoenix UI:      http://localhost:6006"
    echo "  Grafana:         http://localhost:3001 (admin/phoenix_admin)"
    echo "  PostgreSQL:      localhost:5433 (phoenix_user/phoenix_pass)"
    echo "  Redis:           localhost:6380"
    echo ""
    echo "🔧 OTLP Endpoints:"
    echo "  gRPC:            localhost:4317"
    echo "  HTTP:            localhost:4318"
    echo ""
    echo "📁 Data Directories:"
    echo "  Phoenix Data:    ./phoenix/data"
    echo "  PostgreSQL:      ./phoenix/postgres_data"
    echo "  Grafana:         ./phoenix/grafana_data"
    echo ""
    echo "🚀 Next Steps:"
    echo "  1. Configure your agents to send traces to localhost:4317 (gRPC) or localhost:4318 (HTTP)"
    echo "  2. Access Phoenix UI at http://localhost:6006"
    echo "  3. Access Grafana dashboards at http://localhost:3001"
    echo "  4. Monitor agent performance and memory utilization"
    echo ""
    echo "📖 Documentation:"
    echo "  - Phoenix: https://docs.arize.com/phoenix"
    echo "  - OpenTelemetry: https://opentelemetry.io/docs/"
}

# Main execution
main() {
    echo "🔧 Phoenix Observability Setup for Options Trading Agents"
    echo "========================================================"
    echo ""
    
    check_docker
    check_docker_compose
    create_directories
    set_permissions
    validate_config
    start_services
    wait_for_services
    show_service_info
    
    echo ""
    print_success "Setup completed successfully! 🎉"
}

# Run main function
main "$@"
