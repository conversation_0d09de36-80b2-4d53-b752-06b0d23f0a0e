# Phoenix Custom Configuration for Options Trading Agents
# Optimized for financial market operations with memory integration

# Server configuration
server:
  host: "0.0.0.0"
  port: 6006
  cors_enabled: true
  cors_origins: ["*"]

# Database configuration
database:
  url: "************************************************************/phoenix_db"
  pool_size: 20
  max_overflow: 30
  pool_timeout: 30
  pool_recycle: 3600

# OTLP (OpenTelemetry) configuration
otlp:
  grpc:
    host: "0.0.0.0"
    port: 4317
    max_message_size: 4194304  # 4MB
  http:
    host: "0.0.0.0"
    port: 4318
    max_message_size: 4194304  # 4MB

# Trace processing configuration
traces:
  # Retention policy
  retention_days: 30
  
  # Sampling configuration for high-volume trading operations
  sampling:
    default_rate: 1.0  # Sample all traces initially
    rules:
      - service_name: "options_trader"
        operation_name: "market_data_fetch"
        rate: 0.1  # Sample 10% of market data fetches
      - service_name: "risk_analyzer"
        operation_name: "risk_calculation"
        rate: 1.0  # Sample all risk calculations
      - service_name: "*"
        operation_name: "agent_execution"
        rate: 1.0  # Sample all agent executions

# Span processing configuration
spans:
  # Batch processing for performance
  batch_size: 1000
  batch_timeout: 5s
  max_queue_size: 10000
  
  # Span attributes to index for fast queries
  indexed_attributes:
    - "agent.name"
    - "agent.version"
    - "operation.type"
    - "memory.context_count"
    - "evaluation.score"
    - "trading.symbol"
    - "trading.strategy"
    - "error.type"

# Memory integration configuration
memory:
  # Enable memory correlation
  enable_correlation: true
  
  # Memory context tracking
  track_context: true
  max_context_size: 10
  
  # Memory performance metrics
  track_performance: true

# Evaluation integration configuration
evaluation:
  # Enable evaluation correlation
  enable_correlation: true
  
  # Evaluation metrics to track
  tracked_metrics:
    - "correctness"
    - "hallucination_score"
    - "confidence_score"
    - "context_relevance"
    - "evaluation_score"
  
  # Performance thresholds for alerting
  thresholds:
    min_evaluation_score: 0.7
    max_hallucination_score: 0.3
    min_confidence_score: 0.6

# Trading-specific configuration
trading:
  # Market session tracking
  track_sessions: true
  session_timezone: "America/New_York"
  
  # Symbol tracking
  track_symbols: true
  max_symbols_per_trace: 50
  
  # Strategy tracking
  track_strategies: true
  strategy_categories:
    - "covered_call"
    - "protective_put"
    - "iron_condor"
    - "butterfly"
    - "straddle"
    - "strangle"

# Performance optimization
performance:
  # Query optimization
  enable_query_cache: true
  cache_ttl: 300  # 5 minutes
  max_cache_size: 1000
  
  # Compression
  enable_compression: true
  compression_level: 6
  
  # Connection pooling
  connection_pool_size: 20
  max_connections: 100

# Monitoring and alerting
monitoring:
  # Health checks
  health_check_interval: 30s
  
  # Metrics collection
  enable_metrics: true
  metrics_interval: 60s
  
  # Alerting rules
  alerts:
    - name: "high_error_rate"
      condition: "error_rate > 0.1"
      duration: "5m"
      severity: "warning"
    
    - name: "low_evaluation_score"
      condition: "avg_evaluation_score < 0.6"
      duration: "10m"
      severity: "critical"
    
    - name: "high_latency"
      condition: "p95_duration > 30000"  # 30 seconds
      duration: "5m"
      severity: "warning"

# Dashboard configuration
dashboard:
  # Default time range
  default_time_range: "1h"
  
  # Refresh intervals
  auto_refresh: true
  refresh_interval: 30s
  
  # Custom panels
  custom_panels:
    - name: "Agent Performance"
      type: "timeseries"
      metrics: ["avg_duration", "success_rate", "evaluation_score"]
    
    - name: "Memory Utilization"
      type: "gauge"
      metrics: ["memory_context_count", "memory_hit_rate"]
    
    - name: "Trading Operations"
      type: "table"
      metrics: ["symbol", "strategy", "success_rate", "avg_duration"]

# Logging configuration
logging:
  level: "INFO"
  format: "json"
  
  # Log rotation
  max_size: "100MB"
  max_files: 10
  
  # Structured logging fields
  fields:
    - "timestamp"
    - "level"
    - "message"
    - "trace_id"
    - "span_id"
    - "agent_name"
    - "operation_type"

# Security configuration
security:
  # Authentication (disabled for development)
  enable_auth: false
  
  # Rate limiting
  enable_rate_limiting: true
  rate_limit: 1000  # requests per minute
  
  # CORS
  cors_origins: ["http://localhost:3000", "http://localhost:8080"]
  
  # Headers
  security_headers:
    x_frame_options: "DENY"
    x_content_type_options: "nosniff"
    x_xss_protection: "1; mode=block"
