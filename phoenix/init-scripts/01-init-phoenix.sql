-- Phoenix Database Initialization Script
-- Purpose: Set up Phoenix database with trading-specific optimizations
-- Date: 2025-01-11

-- <PERSON>reate extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- Create custom schemas for organization
CREATE SCHEMA IF NOT EXISTS phoenix_traces;
CREATE SCHEMA IF NOT EXISTS phoenix_spans;
CREATE SCHEMA IF NOT EXISTS phoenix_metrics;
CREATE SCHEMA IF NOT EXISTS trading_analytics;

-- Grant permissions to Phoenix user
GRANT ALL PRIVILEGES ON SCHEMA phoenix_traces TO phoenix_user;
GRANT ALL PRIVILEGES ON SCHEMA phoenix_spans TO phoenix_user;
GRANT ALL PRIVILEGES ON SCHEMA phoenix_metrics TO phoenix_user;
GRANT ALL PRIVILEGES ON SCHEMA trading_analytics TO phoenix_user;

-- Create custom tables for trading-specific analytics
CREATE TABLE IF NOT EXISTS trading_analytics.agent_trace_summary (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trace_id TEXT NOT NULL,
    agent_name TEXT NOT NULL,
    operation_type TEXT NOT NULL,
    start_time TIMESTAMPTZ NOT NULL,
    end_time TIMESTAMPTZ,
    duration_ms INTEGER,
    success BOOLEAN DEFAULT TRUE,
    error_message TEXT,
    memory_context_count INTEGER DEFAULT 0,
    evaluation_score DECIMAL(3,2),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_agent_trace_summary_trace_id ON trading_analytics.agent_trace_summary(trace_id);
CREATE INDEX IF NOT EXISTS idx_agent_trace_summary_agent_name ON trading_analytics.agent_trace_summary(agent_name);
CREATE INDEX IF NOT EXISTS idx_agent_trace_summary_start_time ON trading_analytics.agent_trace_summary(start_time DESC);
CREATE INDEX IF NOT EXISTS idx_agent_trace_summary_operation_type ON trading_analytics.agent_trace_summary(operation_type);

-- Create function to link traces with evaluation data
CREATE OR REPLACE FUNCTION trading_analytics.link_trace_evaluation(
    p_trace_id TEXT,
    p_evaluation_score DECIMAL,
    p_memory_context_count INTEGER DEFAULT 0
)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
BEGIN
    UPDATE trading_analytics.agent_trace_summary
    SET 
        evaluation_score = p_evaluation_score,
        memory_context_count = p_memory_context_count,
        updated_at = NOW()
    WHERE trace_id = p_trace_id;
    
    RETURN FOUND;
END;
$$;

-- Create view for Phoenix dashboard integration
CREATE OR REPLACE VIEW trading_analytics.dashboard_metrics AS
SELECT
    agent_name,
    operation_type,
    COUNT(*) as total_operations,
    AVG(duration_ms) as avg_duration_ms,
    AVG(evaluation_score) as avg_evaluation_score,
    COUNT(*) FILTER (WHERE success = TRUE) as successful_operations,
    COUNT(*) FILTER (WHERE memory_context_count > 0) as operations_with_memory,
    MAX(start_time) as last_operation_time
FROM trading_analytics.agent_trace_summary
WHERE start_time >= NOW() - INTERVAL '24 hours'
GROUP BY agent_name, operation_type
ORDER BY total_operations DESC;

-- Create performance monitoring view
CREATE OR REPLACE VIEW trading_analytics.performance_trends AS
SELECT
    DATE_TRUNC('hour', start_time) as time_bucket,
    agent_name,
    COUNT(*) as operations_count,
    AVG(duration_ms) as avg_duration,
    AVG(evaluation_score) as avg_score,
    COUNT(*) FILTER (WHERE success = FALSE) as error_count
FROM trading_analytics.agent_trace_summary
WHERE start_time >= NOW() - INTERVAL '7 days'
GROUP BY DATE_TRUNC('hour', start_time), agent_name
ORDER BY time_bucket DESC, agent_name;

-- Grant permissions on new objects
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA trading_analytics TO phoenix_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA trading_analytics TO phoenix_user;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA trading_analytics TO phoenix_user;
