# Phoenix Observability Setup Guide

This guide provides comprehensive instructions for setting up Phoenix observability for the options trading agents system.

## 🎯 Overview

Phoenix provides comprehensive observability for your trading agents with:
- **Real-time trace monitoring** for agent executions
- **Memory-trace correlation** linking observability with memory system
- **Evaluation metrics integration** with enhanced evaluation harness
- **Custom dashboards** for trading-specific analytics
- **Performance monitoring** and alerting

## 🚀 Quick Start

### 1. Prerequisites

- Docker and Docker Compose installed
- At least 4GB RAM available for containers
- Ports 6006, 5433, 6380, 3001 available

### 2. Setup Phoenix

```bash
# Run the setup script
./phoenix/setup-phoenix.sh

# Or manually with docker-compose
docker-compose -f docker-compose.phoenix.yml up -d
```

### 3. Access Services

- **Phoenix UI**: http://localhost:6006
- **Grafana**: http://localhost:3001 (admin/phoenix_admin)
- **PostgreSQL**: localhost:5433 (phoenix_user/phoenix_pass)

## 📊 Service Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Phoenix Observability Stack              │
├─────────────────────────────────────────────────────────────┤
│  Phoenix UI (6006)     │  Grafana (3001)                   │
│  ├─ Custom Dashboards  │  ├─ Trading Dashboards            │
│  ├─ Trace Viewer       │  ├─ Performance Metrics           │
│  └─ Real-time Monitor  │  └─ Alert Management              │
├─────────────────────────────────────────────────────────────┤
│  Phoenix Server        │  PostgreSQL (5433)                │
│  ├─ OTLP Receivers     │  ├─ Trace Storage                 │
│  ├─ Trace Processing   │  ├─ Analytics Tables              │
│  └─ API Endpoints      │  └─ Custom Functions              │
├─────────────────────────────────────────────────────────────┤
│  Redis Cache (6380)    │  Data Integration                 │
│  ├─ Performance Cache  │  ├─ Supabase Connection           │
│  └─ Session Storage    │  └─ Memory System Link            │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 Configuration

### Phoenix Configuration

The main Phoenix configuration is in `phoenix/custom_config/phoenix.yaml`:

```yaml
# Key configuration sections
server:
  host: "0.0.0.0"
  port: 6006

database:
  url: "************************************************************/phoenix_db"

otlp:
  grpc:
    port: 4317
  http:
    port: 4318

# Trading-specific settings
trading:
  track_sessions: true
  track_symbols: true
  track_strategies: true
```

### Grafana Configuration

Grafana is pre-configured with:
- **Data sources**: Phoenix PostgreSQL and Supabase
- **Dashboards**: Trading agents performance dashboard
- **Alerts**: Performance and health monitoring

## 📈 Custom Dashboards

### Trading Agents Dashboard

The main dashboard includes:

1. **Agent Health Scores** - Real-time health monitoring
2. **Evaluation Scores Over Time** - Trend analysis
3. **Memory Usage Trends** - Memory system utilization
4. **Phoenix Trace Performance** - Execution latency
5. **Agent Operations Summary** - Detailed operations table
6. **Hallucination Detection** - AI safety monitoring
7. **Memory Context Utilization** - Context usage rates
8. **Error Rate** - System reliability metrics

### Custom Panel Configuration

```python
from wheel_trader.phoenix_dashboard import PhoenixDashboardManager

# Create dashboard manager
manager = PhoenixDashboardManager()

# Setup custom dashboard
result = manager.setup_custom_dashboard()
print(f"Dashboard URL: {result['dashboard_url']}")
```

## 🔗 Integration with Trading Agents

### 1. Enhanced Evaluator Integration

The enhanced evaluator automatically correlates with Phoenix traces:

```python
from wheel_trader.enhanced_evaluator import EnhancedEvaluator

evaluator = EnhancedEvaluator(memory_manager, tracer)

# Evaluation automatically links to Phoenix traces
result = evaluator.evaluate_with_memory_context(
    agent_name="options_trader",
    task="Analyze AAPL options",
    result=agent_result,
    trace_id="phoenix_trace_123"  # Automatic correlation
)
```

### 2. Memory System Integration

Memory operations are automatically traced:

```python
from wheel_trader.memory.manager import MemoryManager

memory_manager = MemoryManager(backend="pgvector")

# Memory operations create Phoenix spans
memories = memory_manager.search_memories(
    query="AAPL analysis",
    limit=5
)
```

### 3. Agent Execution Tracing

All agent executions are automatically traced:

```python
from wheel_trader.smolagents_e2b import HealthAwareAgent

agent = HealthAwareAgent(
    name="trading_agent",
    enable_observability=True  # Enables Phoenix tracing
)

# Execution automatically creates traces
result = agent.run("Find AAPL covered call opportunities")
```

## 📊 Monitoring and Alerting

### Health Checks

Phoenix includes comprehensive health monitoring:

```python
from wheel_trader.phoenix_dashboard import get_dashboard_health

health = get_dashboard_health()
print(f"Phoenix Status: {health['status']}")
print(f"Data Sources: {health['data_sources']}")
```

### Performance Metrics

Key metrics monitored:
- **Trace Ingestion Rate**: Traces per second
- **Evaluation Score Trends**: Agent performance over time
- **Memory Utilization**: Context usage patterns
- **Error Rates**: System reliability
- **Latency Percentiles**: Response time distribution

### Alerting Rules

Pre-configured alerts:
- High error rate (>10%)
- Low evaluation scores (<0.6)
- High latency (>30 seconds)
- Memory system issues
- Database connectivity problems

## 🛠️ Troubleshooting

### Common Issues

1. **Phoenix not starting**
   ```bash
   # Check logs
   docker-compose -f docker-compose.phoenix.yml logs phoenix
   
   # Restart services
   docker-compose -f docker-compose.phoenix.yml restart
   ```

2. **Database connection issues**
   ```bash
   # Test PostgreSQL connection
   docker-compose -f docker-compose.phoenix.yml exec phoenix-postgres \
     pg_isready -U phoenix_user -d phoenix_db
   ```

3. **Grafana dashboard not loading**
   ```bash
   # Check Grafana logs
   docker-compose -f docker-compose.phoenix.yml logs phoenix-grafana
   
   # Reset Grafana data
   docker-compose -f docker-compose.phoenix.yml down
   rm -rf phoenix/grafana_data/*
   docker-compose -f docker-compose.phoenix.yml up -d
   ```

### Performance Optimization

1. **Increase memory allocation**
   ```yaml
   # In docker-compose.phoenix.yml
   phoenix:
     deploy:
       resources:
         limits:
           memory: 2G
         reservations:
           memory: 1G
   ```

2. **Optimize PostgreSQL**
   ```sql
   -- Increase shared_buffers
   ALTER SYSTEM SET shared_buffers = '256MB';
   
   -- Optimize for analytics workload
   ALTER SYSTEM SET work_mem = '64MB';
   ```

## 📚 Advanced Configuration

### Custom Trace Attributes

Add custom attributes to traces:

```python
from wheel_trader.observability.tracer import AgentTracer

tracer = AgentTracer("trading_agent")

with tracer.trace_execution("options_analysis") as span:
    span.set_attribute("trading.symbol", "AAPL")
    span.set_attribute("trading.strategy", "covered_call")
    span.set_attribute("market.session", "regular")
    
    # Your trading logic here
    result = analyze_options("AAPL")
```

### Custom Dashboard Panels

Create custom panels for specific metrics:

```python
custom_panel = {
    "type": "timeseries",
    "title": "Custom Trading Metric",
    "query": """
        SELECT 
            created_at as timestamp,
            custom_metric_value
        FROM agent_metrics 
        WHERE agent_name = 'custom_trader'
    """,
    "visualization": {
        "type": "line",
        "color": "blue"
    }
}
```

## 🔐 Security Considerations

### Network Security
- Phoenix services run on isolated Docker network
- External access only through defined ports
- Database credentials stored in environment variables

### Data Privacy
- Trace data retention: 30 days (configurable)
- Sensitive data filtering in trace attributes
- Access control through Grafana authentication

### Production Deployment
- Use external PostgreSQL for production
- Configure SSL/TLS for all connections
- Implement proper backup strategies
- Set up monitoring and alerting

## 📖 Additional Resources

- [Phoenix Documentation](https://docs.arize.com/phoenix)
- [OpenTelemetry Python](https://opentelemetry.io/docs/instrumentation/python/)
- [Grafana Dashboard Guide](https://grafana.com/docs/grafana/latest/dashboards/)
- [PostgreSQL Performance Tuning](https://wiki.postgresql.org/wiki/Performance_Optimization)
