# E2B + smolagents Integration Action Plan

## Current State Analysis

### What's Already Implemented:
1. **Basic e2b structure**: `wheel_trader/exec_manager_e2b.py` exists but uses outdated e2b API
2. **Sample agent**: `agents/sample_agent.py` for fetching options data
3. **Test harness**: `test_e2b.py` for testing sandbox execution
4. **Coordinator**: `wheel_trader/coordinator.py` with basic agent orchestration
5. **Evaluation**: `wheel_trader/evaluator.py` for agent performance tracking

### Issues Found:
1. **Missing e2b SDK**: Not installed in virtual environment
2. **Outdated e2b API**: Current code uses old `e2b_code_interpreter` import
3. **Missing smolagents integration**: No direct smolagents Tool.run() patching
4. **Incomplete audit logging**: SQL tables not created
5. **Missing network ACLs**: No outbound domain restrictions

## Action Plan

### Phase 1: Environment Setup & Dependencies
**Status**: ⏳ PENDING APPROVAL

**Actions**:
1. Install correct e2b SDK packages
2. Update requirements.txt with proper e2b dependencies
3. Verify virtual environment activation

**Commands to execute**:
```bash
pip install e2b-code-interpreter
pip install smolagents
pip freeze > requirements.txt
```

### Phase 2: Update E2B Integration
**Status**: ⏳ PENDING APPROVAL

**Actions**:
1. Update `wheel_trader/exec_manager_e2b.py` to use latest e2b API
2. Add proper resource limits (256MB RAM, 0.5 CPU, 30s timeout)
3. Implement network ACL for allowed domains
4. Add comprehensive error handling and logging

**Key Changes**:
- Use `from e2b_code_interpreter import Sandbox` (latest API)
- Add sandbox configuration with resource limits
- Implement execution audit logging
- Add network policy enforcement

### Phase 3: smolagents Integration
**Status**: ⏳ PENDING APPROVAL

**Actions**:
1. Create smolagents Tool wrapper that routes execution through e2b
2. Implement decorator to patch Tool.run() methods
3. Add agent health checking before execution
4. Integrate with existing coordinator.py

**Files to modify**:
- Create `wheel_trader/smolagents_adapter.py`
- Update `wheel_trader/coordinator.py`
- Patch smolagents Tool execution flow

### Phase 4: Database Schema & Audit Logging
**Status**: ⏳ PENDING APPROVAL

**Actions**:
1. Create SQL migration for `exec_audit_log` table
2. Create SQL migration for `agent_health` table
3. Update evaluator.py to compute health scores
4. Add health gate in coordinator

**SQL Tables**:
```sql
CREATE TABLE exec_audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    container_id TEXT,
    agent_name TEXT,
    cpu_ms INTEGER,
    mem_peak_mb INTEGER,
    duration_ms INTEGER,
    exit_code INTEGER,
    success BOOLEAN,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE agent_health (
    agent_name TEXT PRIMARY KEY,
    version TEXT,
    health_score DECIMAL(3,2),
    last_updated TIMESTAMPTZ DEFAULT NOW()
);
```

### Phase 5: Testing & Validation
**Status**: ⏳ PENDING APPROVAL

**Actions**:
1. Update `test_e2b.py` with comprehensive tests
2. Test resource limit enforcement
3. Test network ACL restrictions
4. Test smolagents integration
5. Test health scoring and gating

**Test Cases**:
- Memory limit enforcement (>256MB should fail)
- CPU timeout enforcement (>30s should fail)
- Network ACL (unauthorized domains should fail)
- Agent health gating (score <0.7 should block execution)

## Implementation Priority

1. **IMMEDIATE**: Install e2b SDK and update basic integration
2. **HIGH**: Update exec_manager_e2b.py with latest API
3. **HIGH**: Create database tables for audit logging
4. **MEDIUM**: Implement smolagents adapter
5. **MEDIUM**: Add comprehensive testing
6. **LOW**: Add advanced monitoring and alerting

## Risk Assessment

**LOW RISK**:
- Installing e2b SDK
- Updating API calls
- Creating database tables

**MEDIUM RISK**:
- Patching smolagents Tool.run() (may affect existing functionality)
- Network ACL implementation (may block legitimate requests)

**HIGH RISK**:
- Resource limit enforcement (may kill legitimate long-running tasks)

## Next Steps

1. **AWAIT APPROVAL** for Phase 1 (dependency installation)
2. **CONFIRM** e2b API key is available in environment
3. **VERIFY** Supabase connection for audit logging
4. **PROCEED** with implementation once approved

## Notes

- Virtual environment is confirmed active: `(venv)`
- Current e2b packages are missing from environment
- smolagents not yet installed
- Existing code structure is good foundation for integration
