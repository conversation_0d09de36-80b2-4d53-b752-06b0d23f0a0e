# Phoenix Observability Stack for Options Trading Agents
# 
# This docker-compose configuration sets up Phoenix for comprehensive
# observability of trading agents with memory and evaluation integration.
#
# Usage:
#   docker-compose -f docker-compose.phoenix.yml up -d
#
# Access:
#   Phoenix UI: http://localhost:6006
#   PostgreSQL: localhost:5433 (to avoid conflicts with main DB)

version: '3.8'

services:
  # Phoenix Observability Platform
  phoenix:
    image: arizephoenix/phoenix:latest
    container_name: phoenix-observability
    ports:
      - "6006:6006"  # Phoenix UI
      - "4317:4317"  # OTLP gRPC receiver
      - "4318:4318"  # OTLP HTTP receiver
    environment:
      # Database configuration
      - PHOENIX_SQL_DATABASE_URL=************************************************************/phoenix_db
      
      # Phoenix configuration
      - PHOENIX_WORKING_DIR=/app/data
      - PHOENIX_HOST=0.0.0.0
      - PHOENIX_PORT=6006
      
      # OTLP configuration for trace ingestion
      - PHOENIX_OTLP_GRPC_HOST=0.0.0.0
      - PHOENIX_OTLP_GRPC_PORT=4317
      - PHOENIX_OTLP_HTTP_HOST=0.0.0.0
      - PHOENIX_OTLP_HTTP_PORT=4318
      
      # Memory and storage configuration
      - PHOENIX_ENABLE_PROMETHEUS=true
      - PHOENIX_ENABLE_CORS=true
      - PHOENIX_LOG_LEVEL=INFO
      
      # Trading-specific configuration
      - PHOENIX_PROJECT_NAME=options-trading-agents
      - PHOENIX_ENVIRONMENT=development
    volumes:
      - phoenix_data:/app/data
      - phoenix_config:/app/config
      - ./phoenix/custom_config:/app/custom_config:ro
    depends_on:
      phoenix-postgres:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6006/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - phoenix-network

  # PostgreSQL Database for Phoenix
  phoenix-postgres:
    image: postgres:15-alpine
    container_name: phoenix-postgres
    ports:
      - "5433:5432"  # External access (avoid conflict with main DB)
    environment:
      - POSTGRES_DB=phoenix_db
      - POSTGRES_USER=phoenix_user
      - POSTGRES_PASSWORD=phoenix_pass
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - phoenix_postgres_data:/var/lib/postgresql/data
      - ./phoenix/init-scripts:/docker-entrypoint-initdb.d:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U phoenix_user -d phoenix_db"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - phoenix-network

  # Redis for Phoenix caching (optional but recommended for performance)
  phoenix-redis:
    image: redis:7-alpine
    container_name: phoenix-redis
    ports:
      - "6380:6379"  # External access (avoid conflict)
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - phoenix_redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - phoenix-network

  # Grafana for additional dashboards (optional)
  phoenix-grafana:
    image: grafana/grafana:latest
    container_name: phoenix-grafana
    ports:
      - "3001:3000"  # External access (avoid conflict with main Grafana)
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=phoenix_admin
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-postgresql-datasource
    volumes:
      - phoenix_grafana_data:/var/lib/grafana
      - ./phoenix/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./phoenix/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - phoenix-postgres
    restart: unless-stopped
    networks:
      - phoenix-network

# Named volumes for data persistence
volumes:
  phoenix_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./phoenix/data
  
  phoenix_config:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./phoenix/config
  
  phoenix_postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./phoenix/postgres_data
  
  phoenix_redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./phoenix/redis_data
  
  phoenix_grafana_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./phoenix/grafana_data

# Network for Phoenix services
networks:
  phoenix-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Health check and monitoring
x-healthcheck-defaults: &healthcheck-defaults
  interval: 30s
  timeout: 10s
  retries: 3
  start_period: 60s
